{"version": 3, "file": "jquery.contextMenu.min.js", "sources": ["jquery.contextMenu.min.js"], "sourcesContent": ["/**\r\n * jQuery contextMenu v2.9.2 - Plugin for simple contextMenu handling\r\n *\r\n * Version: v2.9.2\r\n *\r\n * Authors: <AUTHORS>\n * Web: http://swisnl.github.io/jQuery-contextMenu/\r\n *\r\n * Copyright (c) 2011-2020 SWIS BV and contributors\r\n *\r\n * Licensed under\r\n *   MIT License http://www.opensource.org/licenses/mit-license\r\n *\r\n * Date: 2020-05-13T13:55:36.983Z\r\n */\r\n\r\n// jscs:disable\r\n/* jshint ignore:start */\r\n(function (factory) {\r\n    if (typeof define === 'function' && define.amd) {\r\n        // AMD. Register as anonymous module.\r\n        define(['jquery'], factory);\r\n    } else if (typeof exports === 'object') {\r\n        // Node / CommonJS\r\n        factory(require('jquery'));\r\n    } else {\r\n        // Browser globals.\r\n        factory(jQuery);\r\n    }\r\n})(function ($) {\r\n\r\n    'use strict';\r\n\r\n    // TODO: -\r\n    // ARIA stuff: menuitem, menuitemcheckbox und menuitemradio\r\n    // create <menu> structure if $.support[htmlCommand || htmlMenuitem] and !opt.disableNative\r\n\r\n    // determine html5 compatibility\r\n    $.support.htmlMenuitem = ('HTMLMenuItemElement' in window);\r\n    $.support.htmlCommand = ('HTMLCommandElement' in window);\r\n    $.support.eventSelectstart = ('onselectstart' in document.documentElement);\r\n    /* // should the need arise, test for css user-select\r\n     $.support.cssUserSelect = (function(){\r\n     var t = false,\r\n     e = document.createElement('div');\r\n\r\n     $.each('Moz|Webkit|Khtml|O|ms|Icab|'.split('|'), function(i, prefix) {\r\n     var propCC = prefix + (prefix ? 'U' : 'u') + 'serSelect',\r\n     prop = (prefix ? ('-' + prefix.toLowerCase() + '-') : '') + 'user-select';\r\n\r\n     e.style.cssText = prop + ': text;';\r\n     if (e.style[propCC] == 'text') {\r\n     t = true;\r\n     return false;\r\n     }\r\n\r\n     return true;\r\n     });\r\n\r\n     return t;\r\n     })();\r\n     */\r\n\r\n\r\n    if (!$.ui || !$.widget) {\r\n        // duck punch $.cleanData like jQueryUI does to get that remove event\r\n        $.cleanData = (function (orig) {\r\n            return function (elems) {\r\n                var events, elem, i;\r\n                for (i = 0; elems[i] != null; i++) {\r\n                    elem = elems[i];\r\n                    try {\r\n                        // Only trigger remove when necessary to save time\r\n                        events = $._data(elem, 'events');\r\n                        if (events && events.remove) {\r\n                            $(elem).triggerHandler('remove');\r\n                        }\r\n\r\n                        // Http://bugs.jquery.com/ticket/8235\r\n                    } catch (e) {\r\n                    }\r\n                }\r\n                orig(elems);\r\n            };\r\n        })($.cleanData);\r\n    }\r\n    /* jshint ignore:end */\r\n    // jscs:enable\r\n\r\n    var // currently active contextMenu trigger\r\n        $currentTrigger = null,\r\n        // is contextMenu initialized with at least one menu?\r\n        initialized = false,\r\n        // window handle\r\n        $win = $(window),\r\n        // number of registered menus\r\n        counter = 0,\r\n        // mapping selector to namespace\r\n        namespaces = {},\r\n        // mapping namespace to options\r\n        menus = {},\r\n        // custom command type handlers\r\n        types = {},\r\n        // default values\r\n        defaults = {\r\n            // selector of contextMenu trigger\r\n            selector: null,\r\n            // where to append the menu to\r\n            appendTo: null,\r\n            // method to trigger context menu [\"right\", \"left\", \"hover\"]\r\n            trigger: 'right',\r\n            // hide menu when mouse leaves trigger / menu elements\r\n            autoHide: false,\r\n            // ms to wait before showing a hover-triggered context menu\r\n            delay: 200,\r\n            // flag denoting if a second trigger should simply move (true) or rebuild (false) an open menu\r\n            // as long as the trigger happened on one of the trigger-element's child nodes\r\n            reposition: true,\r\n            // Flag denoting if a second trigger should close the menu, as long as\r\n            // the trigger happened on one of the trigger-element's child nodes.\r\n            // This overrides the reposition option.\r\n            hideOnSecondTrigger: false,\r\n\r\n            //ability to select submenu\r\n            selectableSubMenu: false,\r\n\r\n            // Default classname configuration to be able avoid conflicts in frameworks\r\n            classNames: {\r\n                hover: 'context-menu-hover', // Item hover\r\n                disabled: 'context-menu-disabled', // Item disabled\r\n                visible: 'context-menu-visible', // Item visible\r\n                notSelectable: 'context-menu-not-selectable', // Item not selectable\r\n\r\n                icon: 'context-menu-icon',\r\n                iconEdit: 'context-menu-icon-edit',\r\n                iconCut: 'context-menu-icon-cut',\r\n                iconCopy: 'context-menu-icon-copy',\r\n                iconPaste: 'context-menu-icon-paste',\r\n                iconDelete: 'context-menu-icon-delete',\r\n                iconAdd: 'context-menu-icon-add',\r\n                iconQuit: 'context-menu-icon-quit',\r\n                iconLoadingClass: 'context-menu-icon-loading'\r\n            },\r\n\r\n            // determine position to show menu at\r\n            determinePosition: function ($menu) {\r\n                // position to the lower middle of the trigger element\r\n                if ($.ui && $.ui.position) {\r\n                    // .position() is provided as a jQuery UI utility\r\n                    // (...and it won't work on hidden elements)\r\n                    $menu.css('display', 'block').position({\r\n                        my: 'center top',\r\n                        at: 'center bottom',\r\n                        of: this,\r\n                        offset: '0 5',\r\n                        collision: 'fit'\r\n                    }).css('display', 'none');\r\n                } else {\r\n                    // determine contextMenu position\r\n                    var offset = this.offset();\r\n                    offset.top += this.outerHeight();\r\n                    offset.left += this.outerWidth() / 2 - $menu.outerWidth() / 2;\r\n                    $menu.css(offset);\r\n                }\r\n            },\r\n            // position menu\r\n            position: function (opt, x, y) {\r\n                var offset;\r\n                // determine contextMenu position\r\n                if (!x && !y) {\r\n                    opt.determinePosition.call(this, opt.$menu);\r\n                    return;\r\n                } else if (x === 'maintain' && y === 'maintain') {\r\n                    // x and y must not be changed (after re-show on command click)\r\n                    offset = opt.$menu.position();\r\n                } else {\r\n                    // x and y are given (by mouse event)\r\n                    var offsetParentOffset = opt.$menu.offsetParent().offset();\r\n                    offset = {top: y - offsetParentOffset.top, left: x -offsetParentOffset.left};\r\n                }\r\n\r\n                // correct offset if viewport demands it\r\n                var bottom = $win.scrollTop() + $win.height(),\r\n                    right = $win.scrollLeft() + $win.width(),\r\n                    height = opt.$menu.outerHeight(),\r\n                    width = opt.$menu.outerWidth();\r\n\r\n                if (offset.top + height > bottom) {\r\n                    offset.top -= height;\r\n                }\r\n\r\n                if (offset.top < 0) {\r\n                    offset.top = 0;\r\n                }\r\n\r\n                if (offset.left + width > right) {\r\n                    offset.left -= width;\r\n                }\r\n\r\n                if (offset.left < 0) {\r\n                    offset.left = 0;\r\n                }\r\n\r\n                opt.$menu.css(offset);\r\n            },\r\n            // position the sub-menu\r\n            positionSubmenu: function ($menu) {\r\n                if (typeof $menu === 'undefined') {\r\n                    // When user hovers over item (which has sub items) handle.focusItem will call this.\r\n                    // but the submenu does not exist yet if opt.items is a promise. just return, will\r\n                    // call positionSubmenu after promise is completed.\r\n                    return;\r\n                }\r\n                if ($.ui && $.ui.position) {\r\n                    // .position() is provided as a jQuery UI utility\r\n                    // (...and it won't work on hidden elements)\r\n                    $menu.css('display', 'block').position({\r\n                        my: 'left top-5',\r\n                        at: 'right top',\r\n                        of: this,\r\n                        collision: 'flipfit fit'\r\n                    }).css('display', '');\r\n                } else {\r\n                    // determine contextMenu position\r\n                    var offset = {\r\n                        top: -9,\r\n                        left: this.outerWidth() - 5\r\n                    };\r\n                    $menu.css(offset);\r\n                }\r\n            },\r\n            // offset to add to zIndex\r\n            zIndex: 1,\r\n            // show hide animation settings\r\n            animation: {\r\n                duration: 50,\r\n                show: 'slideDown',\r\n                hide: 'slideUp'\r\n            },\r\n            // events\r\n            events: {\r\n                preShow: $.noop,\r\n                show: $.noop,\r\n                hide: $.noop,\r\n                activated: $.noop\r\n            },\r\n            // default callback\r\n            callback: null,\r\n            // list of contextMenu items\r\n            items: {}\r\n        },\r\n        // mouse position for hover activation\r\n        hoveract = {\r\n            timer: null,\r\n            pageX: null,\r\n            pageY: null\r\n        },\r\n        // determine zIndex\r\n        zindex = function ($t) {\r\n            var zin = 0,\r\n                $tt = $t;\r\n\r\n            while (true) {\r\n                zin = Math.max(zin, parseInt($tt.css('z-index'), 10) || 0);\r\n                $tt = $tt.parent();\r\n                if (!$tt || !$tt.length || 'html body'.indexOf($tt.prop('nodeName').toLowerCase()) > -1) {\r\n                    break;\r\n                }\r\n            }\r\n            return zin;\r\n        },\r\n        // event handlers\r\n        handle = {\r\n            // abort anything\r\n            abortevent: function (e) {\r\n                e.preventDefault();\r\n                e.stopImmediatePropagation();\r\n            },\r\n            // contextmenu show dispatcher\r\n            contextmenu: function (e) {\r\n                var $this = $(this);\r\n\r\n                //Show browser context-menu when preShow returns false\r\n                if (e.data.events.preShow($this,e) === false) {\r\n                    return;\r\n                }\r\n\r\n                // disable actual context-menu if we are using the right mouse button as the trigger\r\n                if (e.data.trigger === 'right') {\r\n                    e.preventDefault();\r\n                    e.stopImmediatePropagation();\r\n                }\r\n\r\n                // abort native-triggered events unless we're triggering on right click\r\n                if ((e.data.trigger !== 'right' && e.data.trigger !== 'demand') && e.originalEvent) {\r\n                    return;\r\n                }\r\n\r\n                // Let the current contextmenu decide if it should show or not based on its own trigger settings\r\n                if (typeof e.mouseButton !== 'undefined' && e.data) {\r\n                    if (!(e.data.trigger === 'left' && e.mouseButton === 0) && !(e.data.trigger === 'right' && e.mouseButton === 2)) {\r\n                        // Mouse click is not valid.\r\n                        return;\r\n                    }\r\n                }\r\n\r\n                // abort event if menu is visible for this trigger\r\n                if ($this.hasClass('context-menu-active')) {\r\n                    return;\r\n                }\r\n\r\n                if (!$this.hasClass('context-menu-disabled')) {\r\n                    // theoretically need to fire a show event at <menu>\r\n                    // http://www.whatwg.org/specs/web-apps/current-work/multipage/interactive-elements.html#context-menus\r\n                    // var evt = jQuery.Event(\"show\", { data: data, pageX: e.pageX, pageY: e.pageY, relatedTarget: this });\r\n                    // e.data.$menu.trigger(evt);\r\n\r\n                    $currentTrigger = $this;\r\n                    if (e.data.build) {\r\n                        var built = e.data.build($currentTrigger, e);\r\n                        // abort if build() returned false\r\n                        if (built === false) {\r\n                            return;\r\n                        }\r\n\r\n                        // dynamically build menu on invocation\r\n                        e.data = $.extend(true, {}, defaults, e.data, built || {});\r\n\r\n                        // abort if there are no items to display\r\n                        if (!e.data.items || $.isEmptyObject(e.data.items)) {\r\n                            // Note: jQuery captures and ignores errors from event handlers\r\n                            if (window.console) {\r\n                                (console.error || console.log).call(console, 'No items specified to show in contextMenu');\r\n                            }\r\n\r\n                            throw new Error('No Items specified');\r\n                        }\r\n\r\n                        // backreference for custom command type creation\r\n                        e.data.$trigger = $currentTrigger;\r\n\r\n                        op.create(e.data);\r\n                    }\r\n                    op.show.call($this, e.data, e.pageX, e.pageY);\r\n                }\r\n            },\r\n            // contextMenu left-click trigger\r\n            click: function (e) {\r\n                e.preventDefault();\r\n                e.stopImmediatePropagation();\r\n                $(this).trigger($.Event('contextmenu', {data: e.data, pageX: e.pageX, pageY: e.pageY}));\r\n            },\r\n            // contextMenu right-click trigger\r\n            mousedown: function (e) {\r\n                // register mouse down\r\n                var $this = $(this);\r\n\r\n                // hide any previous menus\r\n                if ($currentTrigger && $currentTrigger.length && !$currentTrigger.is($this)) {\r\n                    $currentTrigger.data('contextMenu').$menu.trigger('contextmenu:hide');\r\n                }\r\n\r\n                // activate on right click\r\n                if (e.button === 2) {\r\n                    $currentTrigger = $this.data('contextMenuActive', true);\r\n                }\r\n            },\r\n            // contextMenu right-click trigger\r\n            mouseup: function (e) {\r\n                // show menu\r\n                var $this = $(this);\r\n                if ($this.data('contextMenuActive') && $currentTrigger && $currentTrigger.length && $currentTrigger.is($this) && !$this.hasClass('context-menu-disabled')) {\r\n                    e.preventDefault();\r\n                    e.stopImmediatePropagation();\r\n                    $currentTrigger = $this;\r\n                    $this.trigger($.Event('contextmenu', {data: e.data, pageX: e.pageX, pageY: e.pageY}));\r\n                }\r\n\r\n                $this.removeData('contextMenuActive');\r\n            },\r\n            // contextMenu hover trigger\r\n            mouseenter: function (e) {\r\n                var $this = $(this),\r\n                    $related = $(e.relatedTarget),\r\n                    $document = $(document);\r\n\r\n                // abort if we're coming from a menu\r\n                if ($related.is('.context-menu-list') || $related.closest('.context-menu-list').length) {\r\n                    return;\r\n                }\r\n\r\n                // abort if a menu is shown\r\n                if ($currentTrigger && $currentTrigger.length) {\r\n                    return;\r\n                }\r\n\r\n                hoveract.pageX = e.pageX;\r\n                hoveract.pageY = e.pageY;\r\n                hoveract.data = e.data;\r\n                $document.on('mousemove.contextMenuShow', handle.mousemove);\r\n                hoveract.timer = setTimeout(function () {\r\n                    hoveract.timer = null;\r\n                    $document.off('mousemove.contextMenuShow');\r\n                    $currentTrigger = $this;\r\n                    $this.trigger($.Event('contextmenu', {\r\n                        data: hoveract.data,\r\n                        pageX: hoveract.pageX,\r\n                        pageY: hoveract.pageY\r\n                    }));\r\n                }, e.data.delay);\r\n            },\r\n            // contextMenu hover trigger\r\n            mousemove: function (e) {\r\n                hoveract.pageX = e.pageX;\r\n                hoveract.pageY = e.pageY;\r\n            },\r\n            // contextMenu hover trigger\r\n            mouseleave: function (e) {\r\n                // abort if we're leaving for a menu\r\n                var $related = $(e.relatedTarget);\r\n                if ($related.is('.context-menu-list') || $related.closest('.context-menu-list').length) {\r\n                    return;\r\n                }\r\n\r\n                try {\r\n                    clearTimeout(hoveract.timer);\r\n                } catch (e) {\r\n                }\r\n\r\n                hoveract.timer = null;\r\n            },\r\n            // click on layer to hide contextMenu\r\n            layerClick: function (e) {\r\n                var $this = $(this),\r\n                    root = $this.data('contextMenuRoot'),\r\n                    button = e.button,\r\n                    x = e.pageX,\r\n                    y = e.pageY,\r\n                    fakeClick = x === undefined,\r\n                    target,\r\n                    offset;\r\n\r\n                e.preventDefault();\r\n\r\n                setTimeout(function () {\r\n                    // If the click is not real, things break: https://github.com/swisnl/jQuery-contextMenu/issues/132\r\n                    if(fakeClick){\r\n                        if (root !== null && typeof root !== 'undefined' && root.$menu !== null  && typeof root.$menu !== 'undefined') {\r\n                            root.$menu.trigger('contextmenu:hide');\r\n                        }\r\n                        return;\r\n                    }\r\n\r\n                    var $window;\r\n                    var triggerAction = ((root.trigger === 'left' && button === 0) || (root.trigger === 'right' && button === 2));\r\n\r\n                    // find the element that would've been clicked, wasn't the layer in the way\r\n                    if (document.elementFromPoint && root.$layer) {\r\n                        root.$layer.hide();\r\n                        target = document.elementFromPoint(x - $win.scrollLeft(), y - $win.scrollTop());\r\n\r\n                        // also need to try and focus this element if we're in a contenteditable area,\r\n                        // as the layer will prevent the browser mouse action we want\r\n                        if (target !== null && target.isContentEditable) {\r\n                            var range = document.createRange(),\r\n                                sel = window.getSelection();\r\n                            range.selectNode(target);\r\n                            range.collapse(true);\r\n                            sel.removeAllRanges();\r\n                            sel.addRange(range);\r\n                        }\r\n                        $(target).trigger(e);\r\n                        root.$layer.show();\r\n                    }\r\n\r\n                    if (root.hideOnSecondTrigger && triggerAction && root.$menu !== null && typeof root.$menu !== 'undefined') {\r\n                      root.$menu.trigger('contextmenu:hide');\r\n                      return;\r\n                    }\r\n\r\n                    if (root.reposition && triggerAction) {\r\n                        if (document.elementFromPoint) {\r\n                            if (root.$trigger.is(target)) {\r\n                                root.position.call(root.$trigger, root, x, y);\r\n                                return;\r\n                            }\r\n                        } else {\r\n                            offset = root.$trigger.offset();\r\n                            $window = $(window);\r\n                            // while this looks kinda awful, it's the best way to avoid\r\n                            // unnecessarily calculating any positions\r\n                            offset.top += $window.scrollTop();\r\n                            if (offset.top <= e.pageY) {\r\n                                offset.left += $window.scrollLeft();\r\n                                if (offset.left <= e.pageX) {\r\n                                    offset.bottom = offset.top + root.$trigger.outerHeight();\r\n                                    if (offset.bottom >= e.pageY) {\r\n                                        offset.right = offset.left + root.$trigger.outerWidth();\r\n                                        if (offset.right >= e.pageX) {\r\n                                            // reposition\r\n                                            root.position.call(root.$trigger, root, x, y);\r\n                                            return;\r\n                                        }\r\n                                    }\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    if (target && triggerAction) {\r\n                        root.$trigger.one('contextmenu:hidden', function () {\r\n                            $(target).contextMenu({x: x, y: y, button: button});\r\n                        });\r\n                    }\r\n\r\n                    if (root !== null && typeof root !== 'undefined' && root.$menu !== null  && typeof root.$menu !== 'undefined') {\r\n                        root.$menu.trigger('contextmenu:hide');\r\n                    }\r\n                }, 50);\r\n            },\r\n            // key handled :hover\r\n            keyStop: function (e, opt) {\r\n                if (!opt.isInput) {\r\n                    e.preventDefault();\r\n                }\r\n\r\n                e.stopPropagation();\r\n            },\r\n            key: function (e) {\r\n\r\n                var opt = {};\r\n\r\n                // Only get the data from $currentTrigger if it exists\r\n                if ($currentTrigger) {\r\n                    opt = $currentTrigger.data('contextMenu') || {};\r\n                }\r\n                // If the trigger happen on a element that are above the contextmenu do this\r\n                if (typeof opt.zIndex === 'undefined') {\r\n                    opt.zIndex = 0;\r\n                }\r\n                var targetZIndex = 0;\r\n                var getZIndexOfTriggerTarget = function (target) {\r\n                    if (target.style.zIndex !== '') {\r\n                        targetZIndex = target.style.zIndex;\r\n                    } else {\r\n                        if (target.offsetParent !== null && typeof target.offsetParent !== 'undefined') {\r\n                            getZIndexOfTriggerTarget(target.offsetParent);\r\n                        }\r\n                        else if (target.parentElement !== null && typeof target.parentElement !== 'undefined') {\r\n                            getZIndexOfTriggerTarget(target.parentElement);\r\n                        }\r\n                    }\r\n                };\r\n                getZIndexOfTriggerTarget(e.target);\r\n                // If targetZIndex is heigher then opt.zIndex dont progress any futher.\r\n                // This is used to make sure that if you are using a dialog with a input / textarea / contenteditable div\r\n                // and its above the contextmenu it wont steal keys events\r\n                if (opt.$menu && parseInt(targetZIndex,10) > parseInt(opt.$menu.css(\"zIndex\"),10)) {\r\n                    return;\r\n                }\r\n                switch (e.keyCode) {\r\n                    case 9:\r\n                    case 38: // up\r\n                        handle.keyStop(e, opt);\r\n                        // if keyCode is [38 (up)] or [9 (tab) with shift]\r\n                        if (opt.isInput) {\r\n                            if (e.keyCode === 9 && e.shiftKey) {\r\n                                e.preventDefault();\r\n                                if (opt.$selected) {\r\n                                    opt.$selected.find('input, textarea, select').blur();\r\n                                }\r\n                                if (opt.$menu !== null && typeof opt.$menu !== 'undefined') {\r\n                                    opt.$menu.trigger('prevcommand');\r\n                                }\r\n                                return;\r\n                            } else if (e.keyCode === 38 && opt.$selected.find('input, textarea, select').prop('type') === 'checkbox') {\r\n                                // checkboxes don't capture this key\r\n                                e.preventDefault();\r\n                                return;\r\n                            }\r\n                        } else if (e.keyCode !== 9 || e.shiftKey) {\r\n                            if (opt.$menu !== null && typeof opt.$menu !== 'undefined') {\r\n                                opt.$menu.trigger('prevcommand');\r\n                            }\r\n                            return;\r\n                        }\r\n                        break;\r\n                    // omitting break;\r\n                    // case 9: // tab - reached through omitted break;\r\n                    case 40: // down\r\n                        handle.keyStop(e, opt);\r\n                        if (opt.isInput) {\r\n                            if (e.keyCode === 9) {\r\n                                e.preventDefault();\r\n                                if (opt.$selected) {\r\n                                    opt.$selected.find('input, textarea, select').blur();\r\n                                }\r\n                                if (opt.$menu !== null && typeof opt.$menu !== 'undefined') {\r\n                                    opt.$menu.trigger('nextcommand');\r\n                                }\r\n                                return;\r\n                            } else if (e.keyCode === 40 && opt.$selected.find('input, textarea, select').prop('type') === 'checkbox') {\r\n                                // checkboxes don't capture this key\r\n                                e.preventDefault();\r\n                                return;\r\n                            }\r\n                        } else {\r\n                            if (opt.$menu !== null && typeof opt.$menu !== 'undefined') {\r\n                                opt.$menu.trigger('nextcommand');\r\n                            }\r\n                            return;\r\n                        }\r\n                        break;\r\n\r\n                    case 37: // left\r\n                        handle.keyStop(e, opt);\r\n                        if (opt.isInput || !opt.$selected || !opt.$selected.length) {\r\n                            break;\r\n                        }\r\n\r\n                        if (!opt.$selected.parent().hasClass('context-menu-root')) {\r\n                            var $parent = opt.$selected.parent().parent();\r\n                            opt.$selected.trigger('contextmenu:blur');\r\n                            opt.$selected = $parent;\r\n                            return;\r\n                        }\r\n                        break;\r\n\r\n                    case 39: // right\r\n                        handle.keyStop(e, opt);\r\n                        if (opt.isInput || !opt.$selected || !opt.$selected.length) {\r\n                            break;\r\n                        }\r\n\r\n                        var itemdata = opt.$selected.data('contextMenu') || {};\r\n                        if (itemdata.$menu && opt.$selected.hasClass('context-menu-submenu')) {\r\n                            opt.$selected = null;\r\n                            itemdata.$selected = null;\r\n                            itemdata.$menu.trigger('nextcommand');\r\n                            return;\r\n                        }\r\n                        break;\r\n\r\n                    case 35: // end\r\n                    case 36: // home\r\n                        if (opt.$selected && opt.$selected.find('input, textarea, select').length) {\r\n                            return;\r\n                        } else {\r\n                            (opt.$selected && opt.$selected.parent() || opt.$menu)\r\n                                .children(':not(.' + opt.classNames.disabled + ', .' + opt.classNames.notSelectable + ')')[e.keyCode === 36 ? 'first' : 'last']()\r\n                                .trigger('contextmenu:focus');\r\n                            e.preventDefault();\r\n                            return;\r\n                        }\r\n                        break;\r\n\r\n                    case 13: // enter\r\n                        handle.keyStop(e, opt);\r\n                        if (opt.isInput) {\r\n                            if (opt.$selected && !opt.$selected.is('textarea, select')) {\r\n                                e.preventDefault();\r\n                                return;\r\n                            }\r\n                            break;\r\n                        }\r\n                        if (typeof opt.$selected !== 'undefined' && opt.$selected !== null) {\r\n                            opt.$selected.trigger('mouseup');\r\n                        }\r\n                        return;\r\n\r\n                    case 32: // space\r\n                    case 33: // page up\r\n                    case 34: // page down\r\n                        // prevent browser from scrolling down while menu is visible\r\n                        handle.keyStop(e, opt);\r\n                        return;\r\n\r\n                    case 27: // esc\r\n                        handle.keyStop(e, opt);\r\n                        if (opt.$menu !== null && typeof opt.$menu !== 'undefined') {\r\n                            opt.$menu.trigger('contextmenu:hide');\r\n                        }\r\n                        return;\r\n\r\n                    default: // 0-9, a-z\r\n                        var k = (String.fromCharCode(e.keyCode)).toUpperCase();\r\n                        if (opt.accesskeys && opt.accesskeys[k]) {\r\n                            // according to the specs accesskeys must be invoked immediately\r\n                            opt.accesskeys[k].$node.trigger(opt.accesskeys[k].$menu ? 'contextmenu:focus' : 'mouseup');\r\n                            return;\r\n                        }\r\n                        break;\r\n                }\r\n                // pass event to selected item,\r\n                // stop propagation to avoid endless recursion\r\n                e.stopPropagation();\r\n                if (typeof opt.$selected !== 'undefined' && opt.$selected !== null) {\r\n                    opt.$selected.trigger(e);\r\n                }\r\n            },\r\n            // select previous possible command in menu\r\n            prevItem: function (e) {\r\n                e.stopPropagation();\r\n                var opt = $(this).data('contextMenu') || {};\r\n                var root = $(this).data('contextMenuRoot') || {};\r\n\r\n                // obtain currently selected menu\r\n                if (opt.$selected) {\r\n                    var $s = opt.$selected;\r\n                    opt = opt.$selected.parent().data('contextMenu') || {};\r\n                    opt.$selected = $s;\r\n                }\r\n\r\n                var $children = opt.$menu.children(),\r\n                    $prev = !opt.$selected || !opt.$selected.prev().length ? $children.last() : opt.$selected.prev(),\r\n                    $round = $prev;\r\n\r\n                // skip disabled or hidden elements\r\n                while ($prev.hasClass(root.classNames.disabled) || $prev.hasClass(root.classNames.notSelectable) || $prev.is(':hidden')) {\r\n                    if ($prev.prev().length) {\r\n                        $prev = $prev.prev();\r\n                    } else {\r\n                        $prev = $children.last();\r\n                    }\r\n                    if ($prev.is($round)) {\r\n                        // break endless loop\r\n                        return;\r\n                    }\r\n                }\r\n\r\n                // leave current\r\n                if (opt.$selected) {\r\n                    handle.itemMouseleave.call(opt.$selected.get(0), e);\r\n                }\r\n\r\n                // activate next\r\n                handle.itemMouseenter.call($prev.get(0), e);\r\n\r\n                // focus input\r\n                var $input = $prev.find('input, textarea, select');\r\n                if ($input.length) {\r\n                    $input.focus();\r\n                }\r\n            },\r\n            // select next possible command in menu\r\n            nextItem: function (e) {\r\n                e.stopPropagation();\r\n                var opt = $(this).data('contextMenu') || {};\r\n                var root = $(this).data('contextMenuRoot') || {};\r\n\r\n                // obtain currently selected menu\r\n                if (opt.$selected) {\r\n                    var $s = opt.$selected;\r\n                    opt = opt.$selected.parent().data('contextMenu') || {};\r\n                    opt.$selected = $s;\r\n                }\r\n\r\n                var $children = opt.$menu.children(),\r\n                    $next = !opt.$selected || !opt.$selected.next().length ? $children.first() : opt.$selected.next(),\r\n                    $round = $next;\r\n\r\n                // skip disabled\r\n                while ($next.hasClass(root.classNames.disabled) || $next.hasClass(root.classNames.notSelectable) || $next.is(':hidden')) {\r\n                    if ($next.next().length) {\r\n                        $next = $next.next();\r\n                    } else {\r\n                        $next = $children.first();\r\n                    }\r\n                    if ($next.is($round)) {\r\n                        // break endless loop\r\n                        return;\r\n                    }\r\n                }\r\n\r\n                // leave current\r\n                if (opt.$selected) {\r\n                    handle.itemMouseleave.call(opt.$selected.get(0), e);\r\n                }\r\n\r\n                // activate next\r\n                handle.itemMouseenter.call($next.get(0), e);\r\n\r\n                // focus input\r\n                var $input = $next.find('input, textarea, select');\r\n                if ($input.length) {\r\n                    $input.focus();\r\n                }\r\n            },\r\n            // flag that we're inside an input so the key handler can act accordingly\r\n            focusInput: function () {\r\n                var $this = $(this).closest('.context-menu-item'),\r\n                    data = $this.data(),\r\n                    opt = data.contextMenu,\r\n                    root = data.contextMenuRoot;\r\n\r\n                root.$selected = opt.$selected = $this;\r\n                root.isInput = opt.isInput = true;\r\n            },\r\n            // flag that we're inside an input so the key handler can act accordingly\r\n            blurInput: function () {\r\n                var $this = $(this).closest('.context-menu-item'),\r\n                    data = $this.data(),\r\n                    opt = data.contextMenu,\r\n                    root = data.contextMenuRoot;\r\n\r\n                root.isInput = opt.isInput = false;\r\n            },\r\n            // :hover on menu\r\n            menuMouseenter: function () {\r\n                var root = $(this).data().contextMenuRoot;\r\n                root.hovering = true;\r\n            },\r\n            // :hover on menu\r\n            menuMouseleave: function (e) {\r\n                var root = $(this).data().contextMenuRoot;\r\n                if (root.$layer && root.$layer.is(e.relatedTarget)) {\r\n                    root.hovering = false;\r\n                }\r\n            },\r\n            // :hover done manually so key handling is possible\r\n            itemMouseenter: function (e) {\r\n                var $this = $(this),\r\n                    data = $this.data(),\r\n                    opt = data.contextMenu,\r\n                    root = data.contextMenuRoot;\r\n\r\n                root.hovering = true;\r\n\r\n                // abort if we're re-entering\r\n                if (e && root.$layer && root.$layer.is(e.relatedTarget)) {\r\n                    e.preventDefault();\r\n                    e.stopImmediatePropagation();\r\n                }\r\n\r\n                // make sure only one item is selected\r\n                (opt.$menu ? opt : root).$menu\r\n                    .children('.' + root.classNames.hover).trigger('contextmenu:blur')\r\n                    .children('.hover').trigger('contextmenu:blur');\r\n\r\n                if ($this.hasClass(root.classNames.disabled) || $this.hasClass(root.classNames.notSelectable)) {\r\n                    opt.$selected = null;\r\n                    return;\r\n                }\r\n\r\n\r\n                $this.trigger('contextmenu:focus');\r\n            },\r\n            // :hover done manually so key handling is possible\r\n            itemMouseleave: function (e) {\r\n                var $this = $(this),\r\n                    data = $this.data(),\r\n                    opt = data.contextMenu,\r\n                    root = data.contextMenuRoot;\r\n\r\n                if (root !== opt && root.$layer && root.$layer.is(e.relatedTarget)) {\r\n                    if (typeof root.$selected !== 'undefined' && root.$selected !== null) {\r\n                        root.$selected.trigger('contextmenu:blur');\r\n                    }\r\n                    e.preventDefault();\r\n                    e.stopImmediatePropagation();\r\n                    root.$selected = opt.$selected = opt.$node;\r\n                    return;\r\n                }\r\n\r\n                if(opt && opt.$menu && opt.$menu.hasClass('context-menu-visible')){\r\n                    return;\r\n                }\r\n\r\n                $this.trigger('contextmenu:blur');\r\n            },\r\n            // contextMenu item click\r\n            itemClick: function (e) {\r\n                var $this = $(this),\r\n                    data = $this.data(),\r\n                    opt = data.contextMenu,\r\n                    root = data.contextMenuRoot,\r\n                    key = data.contextMenuKey,\r\n                    callback;\r\n\r\n                // abort if the key is unknown or disabled or is a menu\r\n                if (!opt.items[key] || $this.is('.' + root.classNames.disabled + ', .context-menu-separator, .' + root.classNames.notSelectable) || ($this.is('.context-menu-submenu') && root.selectableSubMenu === false )) {\r\n                    return;\r\n                }\r\n\r\n                e.preventDefault();\r\n                e.stopImmediatePropagation();\r\n\r\n                if ($.isFunction(opt.callbacks[key]) && Object.prototype.hasOwnProperty.call(opt.callbacks, key)) {\r\n                    // item-specific callback\r\n                    callback = opt.callbacks[key];\r\n                } else if ($.isFunction(root.callback)) {\r\n                    // default callback\r\n                    callback = root.callback;\r\n                } else {\r\n                    // no callback, no action\r\n                    return;\r\n                }\r\n\r\n                // hide menu if callback doesn't stop that\r\n                if (callback.call(root.$trigger, key, root, e) !== false) {\r\n                    root.$menu.trigger('contextmenu:hide');\r\n                } else if (root.$menu.parent().length) {\r\n                    op.update.call(root.$trigger, root);\r\n                }\r\n            },\r\n            // ignore click events on input elements\r\n            inputClick: function (e) {\r\n                e.stopImmediatePropagation();\r\n            },\r\n            // hide <menu>\r\n            hideMenu: function (e, data) {\r\n                var root = $(this).data('contextMenuRoot');\r\n                op.hide.call(root.$trigger, root, data && data.force);\r\n            },\r\n            // focus <command>\r\n            focusItem: function (e) {\r\n                e.stopPropagation();\r\n                var $this = $(this),\r\n                    data = $this.data(),\r\n                    opt = data.contextMenu,\r\n                    root = data.contextMenuRoot;\r\n\r\n                if ($this.hasClass(root.classNames.disabled) || $this.hasClass(root.classNames.notSelectable)) {\r\n                    return;\r\n                }\r\n\r\n                $this\r\n                    .addClass([root.classNames.hover, root.classNames.visible].join(' '))\r\n                    // select other items and included items\r\n                    .parent().find('.context-menu-item').not($this)\r\n                    .removeClass(root.classNames.visible)\r\n                    .filter('.' + root.classNames.hover)\r\n                    .trigger('contextmenu:blur');\r\n\r\n                // remember selected\r\n                opt.$selected = root.$selected = $this;\r\n\r\n\r\n                if(opt && opt.$node && opt.$node.hasClass('context-menu-submenu')){\r\n                    opt.$node.addClass(root.classNames.hover);\r\n                }\r\n\r\n                // position sub-menu - do after show so dumb $.ui.position can keep up\r\n                if (opt.$node) {\r\n                    root.positionSubmenu.call(opt.$node, opt.$menu);\r\n                }\r\n            },\r\n            // blur <command>\r\n            blurItem: function (e) {\r\n                e.stopPropagation();\r\n                var $this = $(this),\r\n                    data = $this.data(),\r\n                    opt = data.contextMenu,\r\n                    root = data.contextMenuRoot;\r\n\r\n                if (opt.autoHide) { // for tablets and touch screens this needs to remain\r\n                    $this.removeClass(root.classNames.visible);\r\n                }\r\n                $this.removeClass(root.classNames.hover);\r\n                opt.$selected = null;\r\n            }\r\n        },\r\n        // operations\r\n        op = {\r\n            show: function (opt, x, y) {\r\n                var $trigger = $(this),\r\n                    css = {};\r\n\r\n                // hide any open menus\r\n                $('#context-menu-layer').trigger('mousedown');\r\n\r\n                // backreference for callbacks\r\n                opt.$trigger = $trigger;\r\n\r\n                // show event\r\n                if (opt.events.show.call($trigger, opt) === false) {\r\n                    $currentTrigger = null;\r\n                    return;\r\n                }\r\n\r\n                // create or update context menu\r\n                var hasVisibleItems = op.update.call($trigger, opt);\r\n                if (hasVisibleItems === false) {\r\n                    $currentTrigger = null;\r\n                    return;\r\n                }\r\n\r\n                // position menu\r\n                opt.position.call($trigger, opt, x, y);\r\n\r\n                // make sure we're in front\r\n                if (opt.zIndex) {\r\n                    var additionalZValue = opt.zIndex;\r\n                    // If opt.zIndex is a function, call the function to get the right zIndex.\r\n                    if (typeof opt.zIndex === 'function') {\r\n                        additionalZValue = opt.zIndex.call($trigger, opt);\r\n                    }\r\n                    css.zIndex = zindex($trigger) + additionalZValue;\r\n                }\r\n\r\n                // add layer\r\n                op.layer.call(opt.$menu, opt, css.zIndex);\r\n\r\n                // adjust sub-menu zIndexes\r\n                opt.$menu.find('ul').css('zIndex', css.zIndex + 1);\r\n\r\n                // position and show context menu\r\n                opt.$menu.css(css)[opt.animation.show](opt.animation.duration, function () {\r\n                    $trigger.trigger('contextmenu:visible');\r\n\r\n                    op.activated(opt);\r\n                    opt.events.activated(opt);\r\n                });\r\n                // make options available and set state\r\n                $trigger\r\n                    .data('contextMenu', opt)\r\n                    .addClass('context-menu-active');\r\n\r\n                // register key handler\r\n                $(document).off('keydown.contextMenu').on('keydown.contextMenu', handle.key);\r\n                // register autoHide handler\r\n                if (opt.autoHide) {\r\n                    // mouse position handler\r\n                    $(document).on('mousemove.contextMenuAutoHide', function (e) {\r\n                        // need to capture the offset on mousemove,\r\n                        // since the page might've been scrolled since activation\r\n                        var pos = $trigger.offset();\r\n                        pos.right = pos.left + $trigger.outerWidth();\r\n                        pos.bottom = pos.top + $trigger.outerHeight();\r\n\r\n                        if (opt.$layer && !opt.hovering && (!(e.pageX >= pos.left && e.pageX <= pos.right) || !(e.pageY >= pos.top && e.pageY <= pos.bottom))) {\r\n                            /* Additional hover check after short time, you might just miss the edge of the menu */\r\n                            setTimeout(function () {\r\n                                if (!opt.hovering && opt.$menu !== null && typeof opt.$menu !== 'undefined') {\r\n                                    opt.$menu.trigger('contextmenu:hide');\r\n                                }\r\n                            }, 50);\r\n                        }\r\n                    });\r\n                }\r\n            },\r\n            hide: function (opt, force) {\r\n                var $trigger = $(this);\r\n                if (!opt) {\r\n                    opt = $trigger.data('contextMenu') || {};\r\n                }\r\n\r\n                // hide event\r\n                if (!force && opt.events && opt.events.hide.call($trigger, opt) === false) {\r\n                    return;\r\n                }\r\n\r\n                // remove options and revert state\r\n                $trigger\r\n                    .removeData('contextMenu')\r\n                    .removeClass('context-menu-active');\r\n\r\n                if (opt.$layer) {\r\n                    // keep layer for a bit so the contextmenu event can be aborted properly by opera\r\n                    setTimeout((function ($layer) {\r\n                        return function () {\r\n                            $layer.remove();\r\n                        };\r\n                    })(opt.$layer), 10);\r\n\r\n                    try {\r\n                        delete opt.$layer;\r\n                    } catch (e) {\r\n                        opt.$layer = null;\r\n                    }\r\n                }\r\n\r\n                // remove handle\r\n                $currentTrigger = null;\r\n                // remove selected\r\n                opt.$menu.find('.' + opt.classNames.hover).trigger('contextmenu:blur');\r\n                opt.$selected = null;\r\n                // collapse all submenus\r\n                opt.$menu.find('.' + opt.classNames.visible).removeClass(opt.classNames.visible);\r\n                // unregister key and mouse handlers\r\n                // $(document).off('.contextMenuAutoHide keydown.contextMenu'); // http://bugs.jquery.com/ticket/10705\r\n                $(document).off('.contextMenuAutoHide').off('keydown.contextMenu');\r\n                // hide menu\r\n                if (opt.$menu) {\r\n                    opt.$menu[opt.animation.hide](opt.animation.duration, function () {\r\n                        // tear down dynamically built menu after animation is completed.\r\n                        if (opt.build) {\r\n                            opt.$menu.remove();\r\n                            $.each(opt, function (key) {\r\n                                switch (key) {\r\n                                    case 'ns':\r\n                                    case 'selector':\r\n                                    case 'build':\r\n                                    case 'trigger':\r\n                                        return true;\r\n\r\n                                    default:\r\n                                        opt[key] = undefined;\r\n                                        try {\r\n                                            delete opt[key];\r\n                                        } catch (e) {\r\n                                        }\r\n                                        return true;\r\n                                }\r\n                            });\r\n                        }\r\n\r\n                        setTimeout(function () {\r\n                            $trigger.trigger('contextmenu:hidden');\r\n                        }, 10);\r\n                    });\r\n                }\r\n            },\r\n            create: function (opt, root) {\r\n                if (typeof root === 'undefined') {\r\n                    root = opt;\r\n                }\r\n\r\n                // create contextMenu\r\n                opt.$menu = $('<ul class=\"context-menu-list\"></ul>').addClass(opt.className || '').data({\r\n                    'contextMenu': opt,\r\n                    'contextMenuRoot': root\r\n                });\r\n                if(opt.dataAttr){\r\n                    $.each(opt.dataAttr, function (key, item) {\r\n                        opt.$menu.attr('data-' + opt.key, item);\r\n                    });\r\n                }\r\n\r\n                $.each(['callbacks', 'commands', 'inputs'], function (i, k) {\r\n                    opt[k] = {};\r\n                    if (!root[k]) {\r\n                        root[k] = {};\r\n                    }\r\n                });\r\n\r\n                if (!root.accesskeys) {\r\n                    root.accesskeys = {};\r\n                }\r\n\r\n                function createNameNode(item) {\r\n                    var $name = $('<span></span>');\r\n                    if (item._accesskey) {\r\n                        if (item._beforeAccesskey) {\r\n                            $name.append(document.createTextNode(item._beforeAccesskey));\r\n                        }\r\n                        $('<span></span>')\r\n                            .addClass('context-menu-accesskey')\r\n                            .text(item._accesskey)\r\n                            .appendTo($name);\r\n                        if (item._afterAccesskey) {\r\n                            $name.append(document.createTextNode(item._afterAccesskey));\r\n                        }\r\n                    } else {\r\n                        if (item.isHtmlName) {\r\n                            // restrict use with access keys\r\n                            if (typeof item.accesskey !== 'undefined') {\r\n                                throw new Error('accesskeys are not compatible with HTML names and cannot be used together in the same item');\r\n                            }\r\n                            $name.html(item.name);\r\n                        } else {\r\n                            $name.text(item.name);\r\n                        }\r\n                    }\r\n                    return $name;\r\n                }\r\n\r\n                // create contextMenu items\r\n                $.each(opt.items, function (key, item) {\r\n                    var $t = $('<li class=\"context-menu-item\"></li>').addClass(item.className || ''),\r\n                        $label = null,\r\n                        $input = null;\r\n\r\n                    // iOS needs to see a click-event bound to an element to actually\r\n                    // have the TouchEvents infrastructure trigger the click event\r\n                    $t.on('click', $.noop);\r\n\r\n                    // Make old school string seperator a real item so checks wont be\r\n                    // akward later.\r\n                    // And normalize 'cm_separator' into 'cm_seperator'.\r\n                    if (typeof item === 'string' || item.type === 'cm_separator') {\r\n                        item = {type: 'cm_seperator'};\r\n                    }\r\n\r\n                    item.$node = $t.data({\r\n                        'contextMenu': opt,\r\n                        'contextMenuRoot': root,\r\n                        'contextMenuKey': key\r\n                    });\r\n\r\n                    // register accesskey\r\n                    // NOTE: the accesskey attribute should be applicable to any element, but Safari5 and Chrome13 still can't do that\r\n                    if (typeof item.accesskey !== 'undefined') {\r\n                        var aks = splitAccesskey(item.accesskey);\r\n                        for (var i = 0, ak; ak = aks[i]; i++) {\r\n                            if (!root.accesskeys[ak]) {\r\n                                root.accesskeys[ak] = item;\r\n                                var matched = item.name.match(new RegExp('^(.*?)(' + ak + ')(.*)$', 'i'));\r\n                                if (matched) {\r\n                                    item._beforeAccesskey = matched[1];\r\n                                    item._accesskey = matched[2];\r\n                                    item._afterAccesskey = matched[3];\r\n                                }\r\n                                break;\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    if (item.type && types[item.type]) {\r\n                        // run custom type handler\r\n                        types[item.type].call($t, item, opt, root);\r\n                        // register commands\r\n                        $.each([opt, root], function (i, k) {\r\n                            k.commands[key] = item;\r\n                            // Overwrite only if undefined or the item is appended to the root. This so it\r\n                            // doesn't overwrite callbacks of root elements if the name is the same.\r\n                            if ($.isFunction(item.callback) && (typeof k.callbacks[key] === 'undefined' || typeof opt.type === 'undefined')) {\r\n                                k.callbacks[key] = item.callback;\r\n                            }\r\n                        });\r\n                    } else {\r\n                        // add label for input\r\n                        if (item.type === 'cm_seperator') {\r\n                            $t.addClass('context-menu-separator ' + root.classNames.notSelectable);\r\n                        } else if (item.type === 'html') {\r\n                            $t.addClass('context-menu-html ' + root.classNames.notSelectable);\r\n                        } else if (item.type !== 'sub' && item.type) {\r\n                            $label = $('<label></label>').appendTo($t);\r\n                            createNameNode(item).appendTo($label);\r\n\r\n                            $t.addClass('context-menu-input');\r\n                            opt.hasTypes = true;\r\n                            $.each([opt, root], function (i, k) {\r\n                                k.commands[key] = item;\r\n                                k.inputs[key] = item;\r\n                            });\r\n                        } else if (item.items) {\r\n                            item.type = 'sub';\r\n                        }\r\n\r\n                        switch (item.type) {\r\n                            case 'cm_seperator':\r\n                                break;\r\n\r\n                            case 'text':\r\n                                $input = $('<input type=\"text\" value=\"1\" name=\"\" />')\r\n                                    .attr('name', 'context-menu-input-' + key)\r\n                                    .val(item.value || '')\r\n                                    .appendTo($label);\r\n                                break;\r\n\r\n                            case 'textarea':\r\n                                $input = $('<textarea name=\"\"></textarea>')\r\n                                    .attr('name', 'context-menu-input-' + key)\r\n                                    .val(item.value || '')\r\n                                    .appendTo($label);\r\n\r\n                                if (item.height) {\r\n                                    $input.height(item.height);\r\n                                }\r\n                                break;\r\n\r\n                            case 'checkbox':\r\n                                $input = $('<input type=\"checkbox\" value=\"1\" name=\"\" />')\r\n                                    .attr('name', 'context-menu-input-' + key)\r\n                                    .val(item.value || '')\r\n                                    .prop('checked', !!item.selected)\r\n                                    .prependTo($label);\r\n                                break;\r\n\r\n                            case 'radio':\r\n                                $input = $('<input type=\"radio\" value=\"1\" name=\"\" />')\r\n                                    .attr('name', 'context-menu-input-' + item.radio)\r\n                                    .val(item.value || '')\r\n                                    .prop('checked', !!item.selected)\r\n                                    .prependTo($label);\r\n                                break;\r\n\r\n                            case 'select':\r\n                                $input = $('<select name=\"\"></select>')\r\n                                    .attr('name', 'context-menu-input-' + key)\r\n                                    .appendTo($label);\r\n                                if (item.options) {\r\n                                    $.each(item.options, function (value, text) {\r\n                                        $('<option></option>').val(value).text(text).appendTo($input);\r\n                                    });\r\n                                    $input.val(item.selected);\r\n                                }\r\n                                break;\r\n\r\n                            case 'sub':\r\n                                createNameNode(item).appendTo($t);\r\n                                item.appendTo = item.$node;\r\n                                $t.data('contextMenu', item).addClass('context-menu-submenu');\r\n                                item.callback = null;\r\n\r\n                                // If item contains items, and this is a promise, we should create it later\r\n                                // check if subitems is of type promise. If it is a promise we need to create\r\n                                // it later, after promise has been resolved.\r\n                                if ('function' === typeof item.items.then) {\r\n                                    // probably a promise, process it, when completed it will create the sub menu's.\r\n                                    op.processPromises(item, root, item.items);\r\n                                } else {\r\n                                    // normal submenu.\r\n                                    op.create(item, root);\r\n                                }\r\n                                break;\r\n\r\n                            case 'html':\r\n                                $(item.html).appendTo($t);\r\n                                break;\r\n\r\n                            default:\r\n                                $.each([opt, root], function (i, k) {\r\n                                    k.commands[key] = item;\r\n                                    // Overwrite only if undefined or the item is appended to the root. This so it\r\n                                    // doesn't overwrite callbacks of root elements if the name is the same.\r\n                                    if ($.isFunction(item.callback) && (typeof k.callbacks[key] === 'undefined' || typeof opt.type === 'undefined')) {\r\n                                        k.callbacks[key] = item.callback;\r\n                                    }\r\n                                });\r\n                                createNameNode(item).appendTo($t);\r\n                                break;\r\n                        }\r\n\r\n                        // disable key listener in <input>\r\n                        if (item.type && item.type !== 'sub' && item.type !== 'html' && item.type !== 'cm_seperator') {\r\n                            $input\r\n                                .on('focus', handle.focusInput)\r\n                                .on('blur', handle.blurInput);\r\n\r\n                            if (item.events) {\r\n                                $input.on(item.events, opt);\r\n                            }\r\n                        }\r\n\r\n                        // add icons\r\n                        if (item.icon) {\r\n                            if ($.isFunction(item.icon)) {\r\n                                item._icon = item.icon.call(this, this, $t, key, item);\r\n                            } else {\r\n                                if (typeof(item.icon) === 'string' && (\r\n                                    item.icon.substring(0, 4) === 'fab '\r\n                                    || item.icon.substring(0, 4) === 'fas '\r\n                                    || item.icon.substring(0, 4) === 'fad '\r\n                                    || item.icon.substring(0, 4) === 'far '\r\n                                    || item.icon.substring(0, 4) === 'fal ')\r\n                                ) {\r\n                                    // to enable font awesome\r\n                                    $t.addClass(root.classNames.icon + ' ' + root.classNames.icon + '--fa5');\r\n                                    item._icon = $('<i class=\"' + item.icon + '\"></i>');\r\n                                } else if (typeof(item.icon) === 'string' && item.icon.substring(0, 3) === 'fa-') {\r\n                                    item._icon = root.classNames.icon + ' ' + root.classNames.icon + '--fa fa ' + item.icon;\r\n                                } else {\r\n                                    item._icon = root.classNames.icon + ' ' + root.classNames.icon + '-' + item.icon;\r\n                                }\r\n                            }\r\n\r\n                            if(typeof(item._icon) === \"string\"){\r\n                                $t.addClass(item._icon);\r\n                            } else {\r\n                                $t.prepend(item._icon);\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    // cache contained elements\r\n                    item.$input = $input;\r\n                    item.$label = $label;\r\n\r\n                    // attach item to menu\r\n                    $t.appendTo(opt.$menu);\r\n\r\n                    // Disable text selection\r\n                    if (!opt.hasTypes && $.support.eventSelectstart) {\r\n                        // browsers support user-select: none,\r\n                        // IE has a special event for text-selection\r\n                        // browsers supporting neither will not be preventing text-selection\r\n                        $t.on('selectstart.disableTextSelect', handle.abortevent);\r\n                    }\r\n                });\r\n                // attach contextMenu to <body> (to bypass any possible overflow:hidden issues on parents of the trigger element)\r\n                if (!opt.$node) {\r\n                    opt.$menu.css('display', 'none').addClass('context-menu-root');\r\n                }\r\n                opt.$menu.appendTo(opt.appendTo || document.body);\r\n            },\r\n            resize: function ($menu, nested) {\r\n                var domMenu;\r\n                // determine widths of submenus, as CSS won't grow them automatically\r\n                // position:absolute within position:absolute; min-width:100; max-width:200; results in width: 100;\r\n                // kinda sucks hard...\r\n\r\n                // determine width of absolutely positioned element\r\n                $menu.css({position: 'absolute', display: 'block'});\r\n                // don't apply yet, because that would break nested elements' widths\r\n                $menu.data('width',\r\n                    (domMenu = $menu.get(0)).getBoundingClientRect ?\r\n                        Math.ceil(domMenu.getBoundingClientRect().width) :\r\n                        $menu.outerWidth() + 1); // outerWidth() returns rounded pixels\r\n                // reset styles so they allow nested elements to grow/shrink naturally\r\n                $menu.css({\r\n                    position: 'static',\r\n                    minWidth: '0px',\r\n                    maxWidth: '100000px'\r\n                });\r\n                // identify width of nested menus\r\n                $menu.find('> li > ul').each(function () {\r\n                    op.resize($(this), true);\r\n                });\r\n                // reset and apply changes in the end because nested\r\n                // elements' widths wouldn't be calculatable otherwise\r\n                if (!nested) {\r\n                    $menu.find('ul').addBack().css({\r\n                        position: '',\r\n                        display: '',\r\n                        minWidth: '',\r\n                        maxWidth: ''\r\n                    }).outerWidth(function () {\r\n                        return $(this).data('width');\r\n                    });\r\n                }\r\n            },\r\n            update: function (opt, root) {\r\n                var $trigger = this;\r\n                if (typeof root === 'undefined') {\r\n                    root = opt;\r\n                    op.resize(opt.$menu);\r\n                }\r\n\r\n                var hasVisibleItems = false;\r\n\r\n                // re-check disabled for each item\r\n                opt.$menu.children().each(function () {\r\n                    var $item = $(this),\r\n                        key = $item.data('contextMenuKey'),\r\n                        item = opt.items[key],\r\n                        disabled = ($.isFunction(item.disabled) && item.disabled.call($trigger, key, root)) || item.disabled === true,\r\n                        visible;\r\n                    if ($.isFunction(item.visible)) {\r\n                        visible = item.visible.call($trigger, key, root);\r\n                    } else if (typeof item.visible !== 'undefined') {\r\n                        visible = item.visible === true;\r\n                    } else {\r\n                        visible = true;\r\n                    }\r\n\r\n                    if (visible) {\r\n                        hasVisibleItems = true;\r\n                    }\r\n\r\n                    $item[visible ? 'show' : 'hide']();\r\n\r\n                    // dis- / enable item\r\n                    $item[disabled ? 'addClass' : 'removeClass'](root.classNames.disabled);\r\n\r\n                    if ($.isFunction(item.icon)) {\r\n                        $item.removeClass(item._icon);\r\n                        var iconResult = item.icon.call(this, $trigger, $item, key, item);\r\n                        if(typeof(iconResult) === \"string\"){\r\n                            $item.addClass(iconResult);\r\n                        } else {\r\n                            $item.prepend(iconResult);\r\n                        }\r\n                    }\r\n\r\n                    if (item.type) {\r\n                        // dis- / enable input elements\r\n                        $item.find('input, select, textarea').prop('disabled', disabled);\r\n\r\n                        // update input states\r\n                        switch (item.type) {\r\n                            case 'text':\r\n                            case 'textarea':\r\n                                item.$input.val(item.value || '');\r\n                                break;\r\n\r\n                            case 'checkbox':\r\n                            case 'radio':\r\n                                item.$input.val(item.value || '').prop('checked', !!item.selected);\r\n                                break;\r\n\r\n                            case 'select':\r\n                                item.$input.val((item.selected === 0 ? \"0\" : item.selected) || '');\r\n                                break;\r\n                        }\r\n                    }\r\n\r\n                    if (item.$menu) {\r\n                        // update sub-menu\r\n                        var subMenuHasVisibleItems = op.update.call($trigger, item, root);\r\n                        if (subMenuHasVisibleItems) {\r\n                            hasVisibleItems = true;\r\n                        }\r\n                    }\r\n                });\r\n                return hasVisibleItems;\r\n            },\r\n            layer: function (opt, zIndex) {\r\n                // add transparent layer for click area\r\n                // filter and background for Internet Explorer, Issue #23\r\n                var $layer = opt.$layer = $('<div id=\"context-menu-layer\"></div>')\r\n                    .css({\r\n                        height: $win.height(),\r\n                        width: $win.width(),\r\n                        display: 'block',\r\n                        position: 'fixed',\r\n                        'z-index': zIndex - 1,\r\n                        top: 0,\r\n                        left: 0,\r\n                        opacity: 0,\r\n                        filter: 'alpha(opacity=0)',\r\n                        'background-color': '#000'\r\n                    })\r\n                    .data('contextMenuRoot', opt)\r\n                    .appendTo(document.body)\r\n                    .on('contextmenu', handle.abortevent)\r\n                    .on('mousedown', handle.layerClick);\r\n\r\n                // IE6 doesn't know position:fixed;\r\n                if (typeof document.body.style.maxWidth === 'undefined') { // IE6 doesn't support maxWidth\r\n                    $layer.css({\r\n                        'position': 'absolute',\r\n                        'height': $(document).height()\r\n                    });\r\n                }\r\n\r\n                return $layer;\r\n            },\r\n            processPromises: function (opt, root, promise) {\r\n                // Start\r\n                opt.$node.addClass(root.classNames.iconLoadingClass);\r\n\r\n                function completedPromise(opt, root, items) {\r\n                    // Completed promise (dev called promise.resolve). We now have a list of items which can\r\n                    // be used to create the rest of the context menu.\r\n                    if (typeof items === 'undefined') {\r\n                        // Null result, dev should have checked\r\n                        errorPromise(undefined);//own error object\r\n                    }\r\n                    finishPromiseProcess(opt, root, items);\r\n                }\r\n\r\n                function errorPromise(opt, root, errorItem) {\r\n                    // User called promise.reject() with an error item, if not, provide own error item.\r\n                    if (typeof errorItem === 'undefined') {\r\n                        errorItem = {\r\n                            \"error\": {\r\n                                name: \"No items and no error item\",\r\n                                icon: \"context-menu-icon context-menu-icon-quit\"\r\n                            }\r\n                        };\r\n                        if (window.console) {\r\n                            (console.error || console.log).call(console, 'When you reject a promise, provide an \"items\" object, equal to normal sub-menu items');\r\n                        }\r\n                    } else if (typeof errorItem === 'string') {\r\n                        errorItem = {\"error\": {name: errorItem}};\r\n                    }\r\n                    finishPromiseProcess(opt, root, errorItem);\r\n                }\r\n\r\n                function finishPromiseProcess(opt, root, items) {\r\n                    if (typeof root.$menu === 'undefined' || !root.$menu.is(':visible')) {\r\n                        return;\r\n                    }\r\n                    opt.$node.removeClass(root.classNames.iconLoadingClass);\r\n                    opt.items = items;\r\n                    op.create(opt, root, true); // Create submenu\r\n                    op.update(opt, root); // Correctly update position if user is already hovered over menu item\r\n                    root.positionSubmenu.call(opt.$node, opt.$menu); // positionSubmenu, will only do anything if user already hovered over menu item that just got new subitems.\r\n                }\r\n\r\n                // Wait for promise completion. .then(success, error, notify) (we don't track notify). Bind the opt\r\n                // and root to avoid scope problems\r\n                promise.then(completedPromise.bind(this, opt, root), errorPromise.bind(this, opt, root));\r\n            },\r\n            // operation that will run after contextMenu showed on screen\r\n            activated: function(opt){\r\n                var $menu = opt.$menu;\r\n                var $menuOffset = $menu.offset();\r\n                var winHeight = $(window).height();\r\n                var winScrollTop = $(window).scrollTop();\r\n                var menuHeight = $menu.height();\r\n                if(menuHeight > winHeight){\r\n                    $menu.css({\r\n                        'height' : winHeight + 'px',\r\n                        'overflow-x': 'hidden',\r\n                        'overflow-y': 'auto',\r\n                        'top': winScrollTop + 'px'\r\n                    });\r\n                } else if(($menuOffset.top < winScrollTop) || ($menuOffset.top + menuHeight > winScrollTop + winHeight)){\r\n                    $menu.css({\r\n                        'top': winScrollTop + 'px'\r\n                    });\r\n                }\r\n            }\r\n        };\r\n\r\n    // split accesskey according to http://www.whatwg.org/specs/web-apps/current-work/multipage/editing.html#assigned-access-key\r\n    function splitAccesskey(val) {\r\n        var t = val.split(/\\s+/);\r\n        var keys = [];\r\n\r\n        for (var i = 0, k; k = t[i]; i++) {\r\n            k = k.charAt(0).toUpperCase(); // first character only\r\n            // theoretically non-accessible characters should be ignored, but different systems, different keyboard layouts, ... screw it.\r\n            // a map to look up already used access keys would be nice\r\n            keys.push(k);\r\n        }\r\n\r\n        return keys;\r\n    }\r\n\r\n// handle contextMenu triggers\r\n    $.fn.contextMenu = function (operation) {\r\n        var $t = this, $o = operation;\r\n        if (this.length > 0) {  // this is not a build on demand menu\r\n            if (typeof operation === 'undefined') {\r\n                this.first().trigger('contextmenu');\r\n            } else if (typeof operation.x !== 'undefined' && typeof operation.y !== 'undefined') {\r\n                this.first().trigger($.Event('contextmenu', {\r\n                    pageX: operation.x,\r\n                    pageY: operation.y,\r\n                    mouseButton: operation.button\r\n                }));\r\n            } else if (operation === 'hide') {\r\n                var $menu = this.first().data('contextMenu') ? this.first().data('contextMenu').$menu : null;\r\n                if ($menu) {\r\n                    $menu.trigger('contextmenu:hide');\r\n                }\r\n            } else if (operation === 'destroy') {\r\n                $.contextMenu('destroy', {context: this});\r\n            } else if ($.isPlainObject(operation)) {\r\n                operation.context = this;\r\n                $.contextMenu('create', operation);\r\n            } else if (operation) {\r\n                this.removeClass('context-menu-disabled');\r\n            } else if (!operation) {\r\n                this.addClass('context-menu-disabled');\r\n            }\r\n        } else {\r\n            $.each(menus, function () {\r\n                if (this.selector === $t.selector) {\r\n                    $o.data = this;\r\n\r\n                    $.extend($o.data, {trigger: 'demand'});\r\n                }\r\n            });\r\n\r\n            handle.contextmenu.call($o.target, $o);\r\n        }\r\n\r\n        return this;\r\n    };\r\n\r\n    // manage contextMenu instances\r\n    $.contextMenu = function (operation, options) {\r\n        if (typeof operation !== 'string') {\r\n            options = operation;\r\n            operation = 'create';\r\n        }\r\n\r\n        if (typeof options === 'string') {\r\n            options = {selector: options};\r\n        } else if (typeof options === 'undefined') {\r\n            options = {};\r\n        }\r\n\r\n        // merge with default options\r\n        var o = $.extend(true, {}, defaults, options || {});\r\n        var $document = $(document);\r\n        var $context = $document;\r\n        var _hasContext = false;\r\n\r\n        if (!o.context || !o.context.length) {\r\n            o.context = document;\r\n        } else {\r\n            // you never know what they throw at you...\r\n            $context = $(o.context).first();\r\n            o.context = $context.get(0);\r\n            _hasContext = !$(o.context).is(document);\r\n        }\r\n\r\n        switch (operation) {\r\n\r\n            case 'update':\r\n                // Updates visibility and such\r\n                if(_hasContext){\r\n                    op.update($context);\r\n                } else {\r\n                    for(var menu in menus){\r\n                        if(menus.hasOwnProperty(menu)){\r\n                            op.update(menus[menu]);\r\n                        }\r\n                    }\r\n                }\r\n                break;\r\n\r\n            case 'create':\r\n                // no selector no joy\r\n                if (!o.selector) {\r\n                    throw new Error('No selector specified');\r\n                }\r\n                // make sure internal classes are not bound to\r\n                if (o.selector.match(/.context-menu-(list|item|input)($|\\s)/)) {\r\n                    throw new Error('Cannot bind to selector \"' + o.selector + '\" as it contains a reserved className');\r\n                }\r\n                if (!o.build && (!o.items || $.isEmptyObject(o.items))) {\r\n                    throw new Error('No Items specified');\r\n                }\r\n                counter++;\r\n                o.ns = '.contextMenu' + counter;\r\n                if (!_hasContext) {\r\n                    namespaces[o.selector] = o.ns;\r\n                }\r\n                menus[o.ns] = o;\r\n\r\n                // default to right click\r\n                if (!o.trigger) {\r\n                    o.trigger = 'right';\r\n                }\r\n\r\n                if (!initialized) {\r\n                    var itemClick = o.itemClickEvent === 'click' ? 'click.contextMenu' : 'mouseup.contextMenu';\r\n                    var contextMenuItemObj = {\r\n                        // 'mouseup.contextMenu': handle.itemClick,\r\n                        // 'click.contextMenu': handle.itemClick,\r\n                        'contextmenu:focus.contextMenu': handle.focusItem,\r\n                        'contextmenu:blur.contextMenu': handle.blurItem,\r\n                        'contextmenu.contextMenu': handle.abortevent,\r\n                        'mouseenter.contextMenu': handle.itemMouseenter,\r\n                        'mouseleave.contextMenu': handle.itemMouseleave\r\n                    };\r\n                    contextMenuItemObj[itemClick] = handle.itemClick;\r\n                    // make sure item click is registered first\r\n                    $document\r\n                        .on({\r\n                            'contextmenu:hide.contextMenu': handle.hideMenu,\r\n                            'prevcommand.contextMenu': handle.prevItem,\r\n                            'nextcommand.contextMenu': handle.nextItem,\r\n                            'contextmenu.contextMenu': handle.abortevent,\r\n                            'mouseenter.contextMenu': handle.menuMouseenter,\r\n                            'mouseleave.contextMenu': handle.menuMouseleave\r\n                        }, '.context-menu-list')\r\n                        .on('mouseup.contextMenu', '.context-menu-input', handle.inputClick)\r\n                        .on(contextMenuItemObj, '.context-menu-item');\r\n\r\n                    initialized = true;\r\n                }\r\n\r\n                // engage native contextmenu event\r\n                $context\r\n                    .on('contextmenu' + o.ns, o.selector, o, handle.contextmenu);\r\n\r\n                if (_hasContext) {\r\n                    // add remove hook, just in case\r\n                    $context.on('remove' + o.ns, function () {\r\n                        $(this).contextMenu('destroy');\r\n                    });\r\n                }\r\n\r\n                switch (o.trigger) {\r\n                    case 'hover':\r\n                        $context\r\n                            .on('mouseenter' + o.ns, o.selector, o, handle.mouseenter)\r\n                            .on('mouseleave' + o.ns, o.selector, o, handle.mouseleave);\r\n                        break;\r\n\r\n                    case 'left':\r\n                        $context.on('click' + o.ns, o.selector, o, handle.click);\r\n                        break;\r\n\t\t\t\t    case 'touchstart':\r\n                        $context.on('touchstart' + o.ns, o.selector, o, handle.click);\r\n                        break;\r\n                    /*\r\n                     default:\r\n                     // http://www.quirksmode.org/dom/events/contextmenu.html\r\n                     $document\r\n                     .on('mousedown' + o.ns, o.selector, o, handle.mousedown)\r\n                     .on('mouseup' + o.ns, o.selector, o, handle.mouseup);\r\n                     break;\r\n                     */\r\n                }\r\n\r\n                // create menu\r\n                if (!o.build) {\r\n                    op.create(o);\r\n                }\r\n                break;\r\n\r\n            case 'destroy':\r\n                var $visibleMenu;\r\n                if (_hasContext) {\r\n                    // get proper options\r\n                    var context = o.context;\r\n                    $.each(menus, function (ns, o) {\r\n\r\n                        if (!o) {\r\n                            return true;\r\n                        }\r\n\r\n                        // Is this menu equest to the context called from\r\n                        if (!$(context).is(o.selector)) {\r\n                            return true;\r\n                        }\r\n\r\n                        $visibleMenu = $('.context-menu-list').filter(':visible');\r\n                        if ($visibleMenu.length && $visibleMenu.data().contextMenuRoot.$trigger.is($(o.context).find(o.selector))) {\r\n                            $visibleMenu.trigger('contextmenu:hide', {force: true});\r\n                        }\r\n\r\n                        try {\r\n                            if (menus[o.ns].$menu) {\r\n                                menus[o.ns].$menu.remove();\r\n                            }\r\n\r\n                            delete menus[o.ns];\r\n                        } catch (e) {\r\n                            menus[o.ns] = null;\r\n                        }\r\n\r\n                        $(o.context).off(o.ns);\r\n\r\n                        return true;\r\n                    });\r\n                } else if (!o.selector) {\r\n                    $document.off('.contextMenu .contextMenuAutoHide');\r\n                    $.each(menus, function (ns, o) {\r\n                        $(o.context).off(o.ns);\r\n                    });\r\n\r\n                    namespaces = {};\r\n                    menus = {};\r\n                    counter = 0;\r\n                    initialized = false;\r\n\r\n                    $('#context-menu-layer, .context-menu-list').remove();\r\n                } else if (namespaces[o.selector]) {\r\n                    $visibleMenu = $('.context-menu-list').filter(':visible');\r\n                    if ($visibleMenu.length && $visibleMenu.data().contextMenuRoot.$trigger.is(o.selector)) {\r\n                        $visibleMenu.trigger('contextmenu:hide', {force: true});\r\n                    }\r\n\r\n                    try {\r\n                        if (menus[namespaces[o.selector]].$menu) {\r\n                            menus[namespaces[o.selector]].$menu.remove();\r\n                        }\r\n\r\n                        delete menus[namespaces[o.selector]];\r\n                    } catch (e) {\r\n                        menus[namespaces[o.selector]] = null;\r\n                    }\r\n\r\n                    $document.off(namespaces[o.selector]);\r\n                }\r\n                break;\r\n\r\n            case 'html5':\r\n                // if <command> and <menuitem> are not handled by the browser,\r\n                // or options was a bool true,\r\n                // initialize $.contextMenu for them\r\n                if ((!$.support.htmlCommand && !$.support.htmlMenuitem) || (typeof options === 'boolean' && options)) {\r\n                    $('menu[type=\"context\"]').each(function () {\r\n                        if (this.id) {\r\n                            $.contextMenu({\r\n                                selector: '[contextmenu=' + this.id + ']',\r\n                                items: $.contextMenu.fromMenu(this)\r\n                            });\r\n                        }\r\n                    }).css('display', 'none');\r\n                }\r\n                break;\r\n\r\n            default:\r\n                throw new Error('Unknown operation \"' + operation + '\"');\r\n        }\r\n\r\n        return this;\r\n    };\r\n\r\n// import values into <input> commands\r\n    $.contextMenu.setInputValues = function (opt, data) {\r\n        if (typeof data === 'undefined') {\r\n            data = {};\r\n        }\r\n\r\n        $.each(opt.inputs, function (key, item) {\r\n            switch (item.type) {\r\n                case 'text':\r\n                case 'textarea':\r\n                    item.value = data[key] || '';\r\n                    break;\r\n\r\n                case 'checkbox':\r\n                    item.selected = data[key] ? true : false;\r\n                    break;\r\n\r\n                case 'radio':\r\n                    item.selected = (data[item.radio] || '') === item.value;\r\n                    break;\r\n\r\n                case 'select':\r\n                    item.selected = data[key] || '';\r\n                    break;\r\n            }\r\n        });\r\n    };\r\n\r\n// export values from <input> commands\r\n    $.contextMenu.getInputValues = function (opt, data) {\r\n        if (typeof data === 'undefined') {\r\n            data = {};\r\n        }\r\n\r\n        $.each(opt.inputs, function (key, item) {\r\n            switch (item.type) {\r\n                case 'text':\r\n                case 'textarea':\r\n                case 'select':\r\n                    data[key] = item.$input.val();\r\n                    break;\r\n\r\n                case 'checkbox':\r\n                    data[key] = item.$input.prop('checked');\r\n                    break;\r\n\r\n                case 'radio':\r\n                    if (item.$input.prop('checked')) {\r\n                        data[item.radio] = item.value;\r\n                    }\r\n                    break;\r\n            }\r\n        });\r\n\r\n        return data;\r\n    };\r\n\r\n// find <label for=\"xyz\">\r\n    function inputLabel(node) {\r\n        return (node.id && $('label[for=\"' + node.id + '\"]').val()) || node.name;\r\n    }\r\n\r\n// convert <menu> to items object\r\n    function menuChildren(items, $children, counter) {\r\n        if (!counter) {\r\n            counter = 0;\r\n        }\r\n\r\n        $children.each(function () {\r\n            var $node = $(this),\r\n                node = this,\r\n                nodeName = this.nodeName.toLowerCase(),\r\n                label,\r\n                item;\r\n\r\n            // extract <label><input>\r\n            if (nodeName === 'label' && $node.find('input, textarea, select').length) {\r\n                label = $node.text();\r\n                $node = $node.children().first();\r\n                node = $node.get(0);\r\n                nodeName = node.nodeName.toLowerCase();\r\n            }\r\n\r\n            /*\r\n             * <menu> accepts flow-content as children. that means <embed>, <canvas> and such are valid menu items.\r\n             * Not being the sadistic kind, $.contextMenu only accepts:\r\n             * <command>, <menuitem>, <hr>, <span>, <p> <input [text, radio, checkbox]>, <textarea>, <select> and of course <menu>.\r\n             * Everything else will be imported as an html node, which is not interfaced with contextMenu.\r\n             */\r\n\r\n            // http://www.whatwg.org/specs/web-apps/current-work/multipage/commands.html#concept-command\r\n            switch (nodeName) {\r\n                // http://www.whatwg.org/specs/web-apps/current-work/multipage/interactive-elements.html#the-menu-element\r\n                case 'menu':\r\n                    item = {name: $node.attr('label'), items: {}};\r\n                    counter = menuChildren(item.items, $node.children(), counter);\r\n                    break;\r\n\r\n                // http://www.whatwg.org/specs/web-apps/current-work/multipage/commands.html#using-the-a-element-to-define-a-command\r\n                case 'a':\r\n                // http://www.whatwg.org/specs/web-apps/current-work/multipage/commands.html#using-the-button-element-to-define-a-command\r\n                case 'button':\r\n                    item = {\r\n                        name: $node.text(),\r\n                        disabled: !!$node.attr('disabled'),\r\n                        callback: (function () {\r\n                            return function () {\r\n                                $node.get(0).click();\r\n                            };\r\n                        })()\r\n                    };\r\n                    break;\r\n\r\n                // http://www.whatwg.org/specs/web-apps/current-work/multipage/commands.html#using-the-command-element-to-define-a-command\r\n                case 'menuitem':\r\n                case 'command':\r\n                    switch ($node.attr('type')) {\r\n                        case undefined:\r\n                        case 'command':\r\n                        case 'menuitem':\r\n                            item = {\r\n                                name: $node.attr('label'),\r\n                                disabled: !!$node.attr('disabled'),\r\n                                icon: $node.attr('icon'),\r\n                                callback: (function () {\r\n                                    return function () {\r\n                                        $node.get(0).click();\r\n                                    };\r\n                                })()\r\n                            };\r\n                            break;\r\n\r\n                        case 'checkbox':\r\n                            item = {\r\n                                type: 'checkbox',\r\n                                disabled: !!$node.attr('disabled'),\r\n                                name: $node.attr('label'),\r\n                                selected: !!$node.attr('checked')\r\n                            };\r\n                            break;\r\n                        case 'radio':\r\n                            item = {\r\n                                type: 'radio',\r\n                                disabled: !!$node.attr('disabled'),\r\n                                name: $node.attr('label'),\r\n                                radio: $node.attr('radiogroup'),\r\n                                value: $node.attr('id'),\r\n                                selected: !!$node.attr('checked')\r\n                            };\r\n                            break;\r\n\r\n                        default:\r\n                            item = undefined;\r\n                    }\r\n                    break;\r\n\r\n                case 'hr':\r\n                    item = '-------';\r\n                    break;\r\n\r\n                case 'input':\r\n                    switch ($node.attr('type')) {\r\n                        case 'text':\r\n                            item = {\r\n                                type: 'text',\r\n                                name: label || inputLabel(node),\r\n                                disabled: !!$node.attr('disabled'),\r\n                                value: $node.val()\r\n                            };\r\n                            break;\r\n\r\n                        case 'checkbox':\r\n                            item = {\r\n                                type: 'checkbox',\r\n                                name: label || inputLabel(node),\r\n                                disabled: !!$node.attr('disabled'),\r\n                                selected: !!$node.attr('checked')\r\n                            };\r\n                            break;\r\n\r\n                        case 'radio':\r\n                            item = {\r\n                                type: 'radio',\r\n                                name: label || inputLabel(node),\r\n                                disabled: !!$node.attr('disabled'),\r\n                                radio: !!$node.attr('name'),\r\n                                value: $node.val(),\r\n                                selected: !!$node.attr('checked')\r\n                            };\r\n                            break;\r\n\r\n                        default:\r\n                            item = undefined;\r\n                            break;\r\n                    }\r\n                    break;\r\n\r\n                case 'select':\r\n                    item = {\r\n                        type: 'select',\r\n                        name: label || inputLabel(node),\r\n                        disabled: !!$node.attr('disabled'),\r\n                        selected: $node.val(),\r\n                        options: {}\r\n                    };\r\n                    $node.children().each(function () {\r\n                        item.options[this.value] = $(this).text();\r\n                    });\r\n                    break;\r\n\r\n                case 'textarea':\r\n                    item = {\r\n                        type: 'textarea',\r\n                        name: label || inputLabel(node),\r\n                        disabled: !!$node.attr('disabled'),\r\n                        value: $node.val()\r\n                    };\r\n                    break;\r\n\r\n                case 'label':\r\n                    break;\r\n\r\n                default:\r\n                    item = {type: 'html', html: $node.clone(true)};\r\n                    break;\r\n            }\r\n\r\n            if (item) {\r\n                counter++;\r\n                items['key' + counter] = item;\r\n            }\r\n        });\r\n\r\n        return counter;\r\n    }\r\n\r\n// convert html5 menu\r\n    $.contextMenu.fromMenu = function (element) {\r\n        var $this = $(element),\r\n            items = {};\r\n\r\n        menuChildren(items, $this.children());\r\n\r\n        return items;\r\n    };\r\n\r\n// make defaults accessible\r\n    $.contextMenu.defaults = defaults;\r\n    $.contextMenu.types = types;\r\n// export internal functions - undocumented, for hacking only!\r\n    $.contextMenu.handle = handle;\r\n    $.contextMenu.op = op;\r\n    $.contextMenu.menus = menus;\r\n});\r\n"], "names": ["factory", "define", "amd", "exports", "require", "j<PERSON><PERSON><PERSON>", "$", "orig", "support", "htmlMenuitem", "window", "htmlCommand", "eventSelectstart", "document", "documentElement", "ui", "widget", "cleanData", "elems", "events", "elem", "i", "_data", "remove", "<PERSON><PERSON><PERSON><PERSON>", "e", "$currentTrigger", "initialized", "$win", "counter", "namespaces", "menus", "types", "defaults", "selector", "appendTo", "trigger", "autoHide", "delay", "reposition", "hideOnSecondTrigger", "selectableSubMenu", "classNames", "hover", "disabled", "visible", "notSelectable", "icon", "iconEdit", "iconCut", "iconCopy", "iconPaste", "iconDelete", "iconAdd", "iconQuit", "iconLoadingClass", "determinePosition", "$menu", "position", "css", "my", "at", "of", "this", "offset", "collision", "top", "outerHeight", "left", "outerWidth", "opt", "x", "y", "offsetParentOffset", "offsetParent", "bottom", "scrollTop", "height", "right", "scrollLeft", "width", "call", "positionSubmenu", "zIndex", "animation", "duration", "show", "hide", "preShow", "noop", "activated", "callback", "items", "hoveract", "timer", "pageX", "pageY", "handle", "abortevent", "preventDefault", "stopImmediatePropagation", "contextmenu", "$this", "data", "originalEvent", "mouseButton", "hasClass", "build", "built", "extend", "isEmptyObject", "console", "error", "log", "Error", "$trigger", "op", "create", "click", "Event", "mousedown", "length", "is", "button", "mouseup", "removeData", "mouseenter", "$related", "relatedTarget", "$document", "closest", "on", "mousemove", "setTimeout", "off", "mouseleave", "clearTimeout", "layerClick", "target", "root", "fakeClick", "undefined", "$window", "triggerAction", "elementFromPoint", "$layer", "isContentEditable", "range", "createRange", "sel", "getSelection", "selectNode", "collapse", "removeAllRanges", "addRange", "one", "contextMenu", "keyStop", "isInput", "stopPropagation", "key", "targetZIndex", "getZIndexOfTriggerTarget", "style", "parentElement", "parseInt", "keyCode", "shift<PERSON>ey", "$selected", "find", "blur", "prop", "parent", "$parent", "itemdata", "children", "k", "String", "fromCharCode", "toUpperCase", "accesskeys", "$node", "prevItem", "$s", "$children", "$prev", "prev", "last", "$round", "itemMouseleave", "get", "itemMouseenter", "$input", "focus", "nextItem", "$next", "next", "first", "focusInput", "contextMenuRoot", "blurInput", "menuMouseenter", "hovering", "menuMouseleave", "itemClick", "contextMenuKey", "isFunction", "callbacks", "Object", "prototype", "hasOwnProperty", "update", "inputClick", "hideMenu", "force", "focusItem", "addClass", "join", "not", "removeClass", "filter", "blurItem", "additionalZValue", "$t", "zin", "$tt", "Math", "max", "indexOf", "toLowerCase", "zindex", "layer", "pos", "each", "createNameNode", "item", "$name", "_accesskey", "_beforeAccesskey", "append", "createTextNode", "text", "_afterAccesskey", "isHtmlName", "accesskey", "html", "name", "className", "dataAttr", "attr", "$label", "type", "ak", "aks", "val", "t", "split", "keys", "char<PERSON>t", "push", "splitAccesskey", "matched", "match", "RegExp", "commands", "hasTypes", "inputs", "value", "selected", "prependTo", "radio", "options", "then", "processPromises", "_icon", "substring", "prepend", "body", "resize", "nested", "domMenu", "display", "getBoundingClientRect", "ceil", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "addBack", "hasVisibleItems", "$item", "iconResult", "z-index", "opacity", "background-color", "promise", "errorPromise", "errorItem", "finishPromiseProcess", "bind", "$menuOffset", "winHeight", "winScrollTop", "menuHeight", "overflow-x", "overflow-y", "inputLabel", "node", "id", "fn", "operation", "$o", "context", "isPlainObject", "o", "$context", "_hasContext", "menu", "ns", "itemClickEvent", "contextMenuItemObj", "contextmenu:focus.contextMenu", "contextmenu:blur.contextMenu", "contextmenu.contextMenu", "mouseenter.contextMenu", "mouseleave.contextMenu", "contextmenu:hide.contextMenu", "prevcommand.contextMenu", "nextcommand.contextMenu", "$visibleMenu", "fromMenu", "setInputValues", "getInputValues", "element", "menu<PERSON><PERSON><PERSON>n", "label", "nodeName", "clone"], "mappings": "CAkBA,SAAWA,GACe,mBAAXC,QAAyBA,OAAOC,IAEvCD,OAAO,CAAC,UAAWD,GACO,iBAAZG,QAEdH,EAAQI,QAAQ,WAGhBJ,EAAQK,QAThB,CAWG,SAAUC,GAET,aAmCkB,IAAWC,EA5B7BD,EAAEE,QAAQC,aAAgB,wBAAyBC,OACnDJ,EAAEE,QAAQG,YAAe,uBAAwBD,OACjDJ,EAAEE,QAAQI,iBAAoB,kBAAmBC,SAASC,gBAwBrDR,EAAES,IAAOT,EAAEU,SAEZV,EAAEW,WAAuBV,EAkBtBD,EAAEW,UAjBM,SAAUC,GACb,IAAIC,EAAQC,EAAMC,EAClB,IAAKA,EAAI,EAAe,MAAZH,EAAMG,GAAYA,IAAK,CAC/BD,EAAOF,EAAMG,GACb,KAEIF,EAASb,EAAEgB,MAAMF,EAAM,YACTD,EAAOI,QACjBjB,EAAEc,GAAMI,eAAe,UAI7B,MAAOC,KAGblB,EAAKW,MAOjB,IACIQ,EAAkB,KAElBC,GAAc,EAEdC,EAAOtB,EAAEI,QAETmB,EAAU,EAEVC,EAAa,GAEbC,EAAQ,GAERC,EAAQ,GAERC,EAAW,CAEPC,SAAU,KAEVC,SAAU,KAEVC,QAAS,QAETC,UAAU,EAEVC,MAAO,IAGPC,YAAY,EAIZC,qBAAqB,EAGrBC,mBAAmB,EAGnBC,WAAY,CACRC,MAAO,qBACPC,SAAU,wBACVC,QAAS,uBACTC,cAAe,8BAEfC,KAAM,oBACNC,SAAU,yBACVC,QAAS,wBACTC,SAAU,yBACVC,UAAW,0BACXC,WAAY,2BACZC,QAAS,wBACTC,SAAU,yBACVC,iBAAkB,6BAItBC,kBAAmB,SAAUC,GAEzB,GAAInD,EAAES,IAAMT,EAAES,GAAG2C,SAGbD,EAAME,IAAI,UAAW,SAASD,SAAS,CACnCE,GAAI,aACJC,GAAI,gBACJC,GAAIC,KACJC,OAAQ,MACRC,UAAW,QACZN,IAAI,UAAW,YACf,CAEH,IAAIK,EAASD,KAAKC,SAClBA,EAAOE,KAAOH,KAAKI,cACnBH,EAAOI,MAAQL,KAAKM,aAAe,EAAIZ,EAAMY,aAAe,EAC5DZ,EAAME,IAAIK,KAIlBN,SAAU,SAAUY,EAAKC,EAAGC,GACxB,IAAIR,EAEJ,GAAKO,GAAMC,EAAX,CAGO,GAAU,aAAND,GAA0B,aAANC,EAE3BR,EAASM,EAAIb,MAAMC,eAChB,CAEH,IAAIe,EAAqBH,EAAIb,MAAMiB,eAAeV,SAClDA,EAAS,CAACE,IAAKM,EAAIC,EAAmBP,IAAKE,KAAMG,EAAGE,EAAmBL,MAI3E,IAAIO,EAAS/C,EAAKgD,YAAchD,EAAKiD,SACjCC,EAAQlD,EAAKmD,aAAenD,EAAKoD,QACjCH,EAASP,EAAIb,MAAMU,cACnBa,EAAQV,EAAIb,MAAMY,aAElBL,EAAOE,IAAMW,EAASF,IACtBX,EAAOE,KAAOW,GAGdb,EAAOE,IAAM,IACbF,EAAOE,IAAM,GAGbF,EAAOI,KAAOY,EAAQF,IACtBd,EAAOI,MAAQY,GAGfhB,EAAOI,KAAO,IACdJ,EAAOI,KAAO,GAGlBE,EAAIb,MAAME,IAAIK,QAjCVM,EAAId,kBAAkByB,KAAKlB,KAAMO,EAAIb,QAoC7CyB,gBAAiB,SAAUzB,GACvB,QAAqB,IAAVA,EAMX,GAAInD,EAAES,IAAMT,EAAES,GAAG2C,SAGbD,EAAME,IAAI,UAAW,SAASD,SAAS,CACnCE,GAAI,aACJC,GAAI,YACJC,GAAIC,KACJE,UAAW,gBACZN,IAAI,UAAW,QACf,CAEH,IAAIK,EAAS,CACTE,KAAM,EACNE,KAAML,KAAKM,aAAe,GAE9BZ,EAAME,IAAIK,KAIlBmB,OAAQ,EAERC,UAAW,CACPC,SAAU,GACVC,KAAM,YACNC,KAAM,WAGVpE,OAAQ,CACJqE,QAASlF,EAAEmF,KACXH,KAAMhF,EAAEmF,KACRF,KAAMjF,EAAEmF,KACRC,UAAWpF,EAAEmF,MAGjBE,SAAU,KAEVC,MAAO,IAGXC,EAAW,CACPC,MAAO,KACPC,MAAO,KACPC,MAAO,MAiBXC,EAAS,CAELC,WAAY,SAAUzE,GAClBA,EAAE0E,iBACF1E,EAAE2E,4BAGNC,YAAa,SAAU5E,GACnB,IAAI6E,EAAQhG,EAAEyD,MAGd,IAAuC,IAAnCtC,EAAE8E,KAAKpF,OAAOqE,QAAQc,EAAM7E,KAKT,UAAnBA,EAAE8E,KAAKnE,UACPX,EAAE0E,iBACF1E,EAAE2E,8BAIkB,UAAnB3E,EAAE8E,KAAKnE,SAA0C,WAAnBX,EAAE8E,KAAKnE,SAAyBX,EAAE+E,sBAKxC,IAAlB/E,EAAEgF,cAA+BhF,EAAE8E,MACjB,SAAnB9E,EAAE8E,KAAKnE,SAAwC,IAAlBX,EAAEgF,aAA2C,UAAnBhF,EAAE8E,KAAKnE,SAAyC,IAAlBX,EAAEgF,cAO7FH,EAAMI,SAAS,wBAIdJ,EAAMI,SAAS,2BAA0B,CAO1C,GADAhF,EAAkB4E,EACd7E,EAAE8E,KAAKI,MAAO,CACd,IAAIC,EAAQnF,EAAE8E,KAAKI,MAAMjF,EAAiBD,GAE1C,IAAc,IAAVmF,EACA,OAOJ,GAHAnF,EAAE8E,KAAOjG,EAAEuG,QAAO,EAAM,GAAI5E,EAAUR,EAAE8E,KAAMK,GAAS,KAGlDnF,EAAE8E,KAAKX,OAAStF,EAAEwG,cAAcrF,EAAE8E,KAAKX,OAMxC,MAJIlF,OAAOqG,UACNA,QAAQC,OAASD,QAAQE,KAAKhC,KAAK8B,QAAS,6CAG3C,IAAIG,MAAM,sBAIpBzF,EAAE8E,KAAKY,SAAWzF,EAElB0F,EAAGC,OAAO5F,EAAE8E,MAEhBa,EAAG9B,KAAKL,KAAKqB,EAAO7E,EAAE8E,KAAM9E,EAAEsE,MAAOtE,EAAEuE,SAI/CsB,MAAO,SAAU7F,GACbA,EAAE0E,iBACF1E,EAAE2E,2BACF9F,EAAEyD,MAAM3B,QAAQ9B,EAAEiH,MAAM,cAAe,CAAChB,KAAM9E,EAAE8E,KAAMR,MAAOtE,EAAEsE,MAAOC,MAAOvE,EAAEuE,UAGnFwB,UAAW,SAAU/F,GAEjB,IAAI6E,EAAQhG,EAAEyD,MAGVrC,GAAmBA,EAAgB+F,SAAW/F,EAAgBgG,GAAGpB,IACjE5E,EAAgB6E,KAAK,eAAe9C,MAAMrB,QAAQ,oBAIrC,IAAbX,EAAEkG,SACFjG,EAAkB4E,EAAMC,KAAK,qBAAqB,KAI1DqB,QAAS,SAAUnG,GAEf,IAAI6E,EAAQhG,EAAEyD,MACVuC,EAAMC,KAAK,sBAAwB7E,GAAmBA,EAAgB+F,QAAU/F,EAAgBgG,GAAGpB,KAAWA,EAAMI,SAAS,2BAC7HjF,EAAE0E,iBACF1E,EAAE2E,4BACF1E,EAAkB4E,GACZlE,QAAQ9B,EAAEiH,MAAM,cAAe,CAAChB,KAAM9E,EAAE8E,KAAMR,MAAOtE,EAAEsE,MAAOC,MAAOvE,EAAEuE,UAGjFM,EAAMuB,WAAW,sBAGrBC,WAAY,SAAUrG,GAClB,IAAI6E,EAAQhG,EAAEyD,MACVgE,EAAWzH,EAAEmB,EAAEuG,eACfC,EAAY3H,EAAEO,UAGdkH,EAASL,GAAG,uBAAyBK,EAASG,QAAQ,sBAAsBT,QAK5E/F,GAAmBA,EAAgB+F,SAIvC5B,EAASE,MAAQtE,EAAEsE,MACnBF,EAASG,MAAQvE,EAAEuE,MACnBH,EAASU,KAAO9E,EAAE8E,KAClB0B,EAAUE,GAAG,4BAA6BlC,EAAOmC,WACjDvC,EAASC,MAAQuC,WAAW,WACxBxC,EAASC,MAAQ,KACjBmC,EAAUK,IAAI,8BACd5G,EAAkB4E,GACZlE,QAAQ9B,EAAEiH,MAAM,cAAe,CACjChB,KAAMV,EAASU,KACfR,MAAOF,EAASE,MAChBC,MAAOH,EAASG,UAErBvE,EAAE8E,KAAKjE,SAGd8F,UAAW,SAAU3G,GACjBoE,EAASE,MAAQtE,EAAEsE,MACnBF,EAASG,MAAQvE,EAAEuE,OAGvBuC,WAAY,SAAU9G,GAElB,IAAIsG,EAAWzH,EAAEmB,EAAEuG,eACnB,IAAID,EAASL,GAAG,wBAAyBK,EAASG,QAAQ,sBAAsBT,OAAhF,CAIA,IACIe,aAAa3C,EAASC,OACxB,MAAOrE,IAGToE,EAASC,MAAQ,OAGrB2C,WAAY,SAAUhH,GAClB,IAMIiH,EACA1E,EANA2E,EADQrI,EAAEyD,MACGwC,KAAK,mBAClBoB,EAASlG,EAAEkG,OACXpD,EAAI9C,EAAEsE,MACNvB,EAAI/C,EAAEuE,MACN4C,OAAkBC,IAANtE,EAIhB9C,EAAE0E,iBAEFkC,WAAW,WAEP,GAAGO,EACKD,MAAAA,GAA+D,OAAfA,EAAKlF,YAAyC,IAAfkF,EAAKlF,OACpFkF,EAAKlF,MAAMrB,QAAQ,wBAF3B,CAOA,IAAI0G,EACAC,EAAmC,SAAjBJ,EAAKvG,SAAiC,IAAXuF,GAAmC,UAAjBgB,EAAKvG,SAAkC,IAAXuF,EAG/F,GAAI9G,SAASmI,kBAAoBL,EAAKM,OAAQ,CAM1C,GALAN,EAAKM,OAAO1D,OAKG,QAJfmD,EAAS7H,SAASmI,iBAAiBzE,EAAI3C,EAAKmD,aAAcP,EAAI5C,EAAKgD,eAI5C8D,EAAOQ,kBAAmB,CAC7C,IAAIC,EAAQtI,SAASuI,cACjBC,EAAM3I,OAAO4I,eACjBH,EAAMI,WAAWb,GACjBS,EAAMK,UAAS,GACfH,EAAII,kBACJJ,EAAIK,SAASP,GAEjB7I,EAAEoI,GAAQtG,QAAQX,GAClBkH,EAAKM,OAAO3D,OAGhB,GAAIqD,EAAKnG,qBAAuBuG,GAAgC,OAAfJ,EAAKlF,YAAwC,IAAfkF,EAAKlF,MAClFkF,EAAKlF,MAAMrB,QAAQ,wBADrB,CAKA,GAAIuG,EAAKpG,YAAcwG,EACnB,GAAIlI,SAASmI,kBACT,GAAIL,EAAKxB,SAASO,GAAGgB,GAEjB,YADAC,EAAKjF,SAASuB,KAAK0D,EAAKxB,SAAUwB,EAAMpE,EAAGC,QAS/C,GALAR,EAAS2E,EAAKxB,SAASnD,SACvB8E,EAAUxI,EAAEI,QAGZsD,EAAOE,KAAO4E,EAAQlE,YAClBZ,EAAOE,KAAOzC,EAAEuE,QAChBhC,EAAOI,MAAQ0E,EAAQ/D,aACnBf,EAAOI,MAAQ3C,EAAEsE,QACjB/B,EAAOW,OAASX,EAAOE,IAAMyE,EAAKxB,SAAShD,cACvCH,EAAOW,QAAUlD,EAAEuE,QACnBhC,EAAOc,MAAQd,EAAOI,KAAOuE,EAAKxB,SAAS9C,aACvCL,EAAOc,OAASrD,EAAEsE,SAGlB,YADA4C,EAAKjF,SAASuB,KAAK0D,EAAKxB,SAAUwB,EAAMpE,EAAGC,GAS/DkE,GAAUK,GACVJ,EAAKxB,SAASwC,IAAI,qBAAsB,WACpCrJ,EAAEoI,GAAQkB,YAAY,CAACrF,EAAGA,EAAGC,EAAGA,EAAGmD,OAAQA,MAI/CgB,MAAAA,GAA+D,OAAfA,EAAKlF,YAAyC,IAAfkF,EAAKlF,OACpFkF,EAAKlF,MAAMrB,QAAQ,uBAExB,KAGPyH,QAAS,SAAUpI,EAAG6C,GACbA,EAAIwF,SACLrI,EAAE0E,iBAGN1E,EAAEsI,mBAENC,IAAK,SAAUvI,GAEX,IAAI6C,EAAM,GAGN5C,IACA4C,EAAM5C,EAAgB6E,KAAK,gBAAkB,SAGvB,IAAfjC,EAAIa,SACXb,EAAIa,OAAS,GAEjB,IAAI8E,EAAe,EACfC,EAA2B,SAAUxB,GACT,KAAxBA,EAAOyB,MAAMhF,OACb8E,EAAevB,EAAOyB,MAAMhF,OAEA,OAAxBuD,EAAOhE,mBAAwD,IAAxBgE,EAAOhE,aAC9CwF,EAAyBxB,EAAOhE,cAEF,OAAzBgE,EAAO0B,oBAA0D,IAAzB1B,EAAO0B,eACpDF,EAAyBxB,EAAO0B,gBAQ5C,GAJAF,EAAyBzI,EAAEiH,UAIvBpE,EAAIb,OAAS4G,SAASJ,EAAa,IAAMI,SAAS/F,EAAIb,MAAME,IAAI,UAAU,KAA9E,CAGA,OAAQlC,EAAE6I,SACN,KAAK,EACL,KAAK,GAGD,GAFArE,EAAO4D,QAAQpI,EAAG6C,GAEdA,EAAIwF,QAAS,CACb,GAAkB,IAAdrI,EAAE6I,SAAiB7I,EAAE8I,SAQrB,OAPA9I,EAAE0E,iBACE7B,EAAIkG,WACJlG,EAAIkG,UAAUC,KAAK,2BAA2BC,YAEhC,OAAdpG,EAAIb,YAAuC,IAAda,EAAIb,OACjCa,EAAIb,MAAMrB,QAAQ,gBAGnB,GAAkB,KAAdX,EAAE6I,SAAiF,aAA/DhG,EAAIkG,UAAUC,KAAK,2BAA2BE,KAAK,QAG9E,YADAlJ,EAAE0E,sBAGH,GAAkB,IAAd1E,EAAE6I,SAAiB7I,EAAE8I,SAI5B,YAHkB,OAAdjG,EAAIb,YAAuC,IAAda,EAAIb,OACjCa,EAAIb,MAAMrB,QAAQ,gBAI1B,MAGJ,KAAK,GAED,GADA6D,EAAO4D,QAAQpI,EAAG6C,IACdA,EAAIwF,QAmBJ,YAHkB,OAAdxF,EAAIb,YAAuC,IAAda,EAAIb,OACjCa,EAAIb,MAAMrB,QAAQ,gBAhBtB,GAAkB,IAAdX,EAAE6I,QAQF,OAPA7I,EAAE0E,iBACE7B,EAAIkG,WACJlG,EAAIkG,UAAUC,KAAK,2BAA2BC,YAEhC,OAAdpG,EAAIb,YAAuC,IAAda,EAAIb,OACjCa,EAAIb,MAAMrB,QAAQ,gBAGnB,GAAkB,KAAdX,EAAE6I,SAAiF,aAA/DhG,EAAIkG,UAAUC,KAAK,2BAA2BE,KAAK,QAG9E,YADAlJ,EAAE0E,iBASV,MAEJ,KAAK,GAED,GADAF,EAAO4D,QAAQpI,EAAG6C,GACdA,EAAIwF,UAAYxF,EAAIkG,YAAclG,EAAIkG,UAAU/C,OAChD,MAGJ,GAAKnD,EAAIkG,UAAUI,SAASlE,SAAS,qBAMrC,MALI,IAAImE,EAAUvG,EAAIkG,UAAUI,SAASA,SAGrC,OAFAtG,EAAIkG,UAAUpI,QAAQ,yBACtBkC,EAAIkG,UAAYK,GAKxB,KAAK,GAED,GADA5E,EAAO4D,QAAQpI,EAAG6C,GACdA,EAAIwF,UAAYxF,EAAIkG,YAAclG,EAAIkG,UAAU/C,OAChD,MAGJ,IAAIqD,EAAWxG,EAAIkG,UAAUjE,KAAK,gBAAkB,GACpD,GAAIuE,EAASrH,OAASa,EAAIkG,UAAU9D,SAAS,wBAIzC,OAHApC,EAAIkG,UAAY,KAChBM,EAASN,UAAY,UACrBM,EAASrH,MAAMrB,QAAQ,eAG3B,MAEJ,KAAK,GACL,KAAK,GACD,OAAIkC,EAAIkG,WAAalG,EAAIkG,UAAUC,KAAK,2BAA2BhD,YAC/D,IAECnD,EAAIkG,WAAalG,EAAIkG,UAAUI,UAAYtG,EAAIb,OAC3CsH,SAAS,SAAWzG,EAAI5B,WAAWE,SAAW,MAAQ0B,EAAI5B,WAAWI,cAAgB,KAAmB,KAAdrB,EAAE6I,QAAiB,QAAU,UACvHlI,QAAQ,0BACbX,EAAE0E,kBAKV,KAAK,GAED,GADAF,EAAO4D,QAAQpI,EAAG6C,GACdA,EAAIwF,QAAS,CACb,GAAIxF,EAAIkG,YAAclG,EAAIkG,UAAU9C,GAAG,oBAEnC,YADAjG,EAAE0E,iBAGN,MAKJ,iBAH6B,IAAlB7B,EAAIkG,WAA+C,OAAlBlG,EAAIkG,WAC5ClG,EAAIkG,UAAUpI,QAAQ,YAI9B,KAAK,GACL,KAAK,GACL,KAAK,GAGD,YADA6D,EAAO4D,QAAQpI,EAAG6C,GAGtB,KAAK,GAKD,OAJA2B,EAAO4D,QAAQpI,EAAG6C,QACA,OAAdA,EAAIb,YAAuC,IAAda,EAAIb,OACjCa,EAAIb,MAAMrB,QAAQ,qBAI1B,QACI,IAAI4I,EAAKC,OAAOC,aAAazJ,EAAE6I,SAAUa,cACzC,GAAI7G,EAAI8G,YAAc9G,EAAI8G,WAAWJ,GAGjC,YADA1G,EAAI8G,WAAWJ,GAAGK,MAAMjJ,QAAQkC,EAAI8G,WAAWJ,GAAGvH,MAAQ,oBAAsB,WAO5FhC,EAAEsI,uBAC2B,IAAlBzF,EAAIkG,WAA+C,OAAlBlG,EAAIkG,WAC5ClG,EAAIkG,UAAUpI,QAAQX,KAI9B6J,SAAU,SAAU7J,GAChBA,EAAEsI,kBACF,IAAIzF,EAAMhE,EAAEyD,MAAMwC,KAAK,gBAAkB,GACrCoC,EAAOrI,EAAEyD,MAAMwC,KAAK,oBAAsB,GAG9C,GAAIjC,EAAIkG,UAAW,CACf,IAAIe,EAAKjH,EAAIkG,WACblG,EAAMA,EAAIkG,UAAUI,SAASrE,KAAK,gBAAkB,IAChDiE,UAAYe,EAQpB,IALA,IAAIC,EAAYlH,EAAIb,MAAMsH,WACtBU,EAASnH,EAAIkG,WAAclG,EAAIkG,UAAUkB,OAAOjE,OAA4BnD,EAAIkG,UAAUkB,OAAjCF,EAAUG,OACnEC,EAASH,EAGNA,EAAM/E,SAASiC,EAAKjG,WAAWE,WAAa6I,EAAM/E,SAASiC,EAAKjG,WAAWI,gBAAkB2I,EAAM/D,GAAG,YAMzG,IAJI+D,EADAA,EAAMC,OAAOjE,OACLgE,EAAMC,OAENF,EAAUG,QAEZjE,GAAGkE,GAET,OAKJtH,EAAIkG,WACJvE,EAAO4F,eAAe5G,KAAKX,EAAIkG,UAAUsB,IAAI,GAAIrK,GAIrDwE,EAAO8F,eAAe9G,KAAKwG,EAAMK,IAAI,GAAIrK,GAGzC,IAAIuK,EAASP,EAAMhB,KAAK,2BACpBuB,EAAOvE,QACPuE,EAAOC,SAIfC,SAAU,SAAUzK,GAChBA,EAAEsI,kBACF,IAAIzF,EAAMhE,EAAEyD,MAAMwC,KAAK,gBAAkB,GACrCoC,EAAOrI,EAAEyD,MAAMwC,KAAK,oBAAsB,GAG9C,GAAIjC,EAAIkG,UAAW,CACf,IAAIe,EAAKjH,EAAIkG,WACblG,EAAMA,EAAIkG,UAAUI,SAASrE,KAAK,gBAAkB,IAChDiE,UAAYe,EAQpB,IALA,IAAIC,EAAYlH,EAAIb,MAAMsH,WACtBoB,EAAS7H,EAAIkG,WAAclG,EAAIkG,UAAU4B,OAAO3E,OAA6BnD,EAAIkG,UAAU4B,OAAlCZ,EAAUa,QACnET,EAASO,EAGNA,EAAMzF,SAASiC,EAAKjG,WAAWE,WAAauJ,EAAMzF,SAASiC,EAAKjG,WAAWI,gBAAkBqJ,EAAMzE,GAAG,YAMzG,IAJIyE,EADAA,EAAMC,OAAO3E,OACL0E,EAAMC,OAENZ,EAAUa,SAEZ3E,GAAGkE,GAET,OAKJtH,EAAIkG,WACJvE,EAAO4F,eAAe5G,KAAKX,EAAIkG,UAAUsB,IAAI,GAAIrK,GAIrDwE,EAAO8F,eAAe9G,KAAKkH,EAAML,IAAI,GAAIrK,GAGzC,IAAIuK,EAASG,EAAM1B,KAAK,2BACpBuB,EAAOvE,QACPuE,EAAOC,SAIfK,WAAY,WACR,IAAIhG,EAAQhG,EAAEyD,MAAMmE,QAAQ,sBACxB3B,EAAOD,EAAMC,OACbjC,EAAMiC,EAAKqD,YACXjB,EAAOpC,EAAKgG,gBAEhB5D,EAAK6B,UAAYlG,EAAIkG,UAAYlE,EACjCqC,EAAKmB,QAAUxF,EAAIwF,SAAU,GAGjC0C,UAAW,WACP,IACIjG,EADQjG,EAAEyD,MAAMmE,QAAQ,sBACX3B,OACbjC,EAAMiC,EAAKqD,YACJrD,EAAKgG,gBAEXzC,QAAUxF,EAAIwF,SAAU,GAGjC2C,eAAgB,WACDnM,EAAEyD,MAAMwC,OAAOgG,gBACrBG,UAAW,GAGpBC,eAAgB,SAAUlL,GACtB,IAAIkH,EAAOrI,EAAEyD,MAAMwC,OAAOgG,gBACtB5D,EAAKM,QAAUN,EAAKM,OAAOvB,GAAGjG,EAAEuG,iBAChCW,EAAK+D,UAAW,IAIxBX,eAAgB,SAAUtK,GACtB,IAAI6E,EAAQhG,EAAEyD,MACVwC,EAAOD,EAAMC,OACbjC,EAAMiC,EAAKqD,YACXjB,EAAOpC,EAAKgG,gBAEhB5D,EAAK+D,UAAW,EAGZjL,GAAKkH,EAAKM,QAAUN,EAAKM,OAAOvB,GAAGjG,EAAEuG,iBACrCvG,EAAE0E,iBACF1E,EAAE2E,6BAIL9B,EAAIb,MAAQa,EAAMqE,GAAMlF,MACpBsH,SAAS,IAAMpC,EAAKjG,WAAWC,OAAOP,QAAQ,oBAC9C2I,SAAS,UAAU3I,QAAQ,oBAE5BkE,EAAMI,SAASiC,EAAKjG,WAAWE,WAAa0D,EAAMI,SAASiC,EAAKjG,WAAWI,eAC3EwB,EAAIkG,UAAY,KAKpBlE,EAAMlE,QAAQ,sBAGlByJ,eAAgB,SAAUpK,GACtB,IAAI6E,EAAQhG,EAAEyD,MACVwC,EAAOD,EAAMC,OACbjC,EAAMiC,EAAKqD,YACXjB,EAAOpC,EAAKgG,gBAEhB,GAAI5D,IAASrE,GAAOqE,EAAKM,QAAUN,EAAKM,OAAOvB,GAAGjG,EAAEuG,eAOhD,YAN8B,IAAnBW,EAAK6B,WAAgD,OAAnB7B,EAAK6B,WAC9C7B,EAAK6B,UAAUpI,QAAQ,oBAE3BX,EAAE0E,iBACF1E,EAAE2E,gCACFuC,EAAK6B,UAAYlG,EAAIkG,UAAYlG,EAAI+G,OAItC/G,GAAOA,EAAIb,OAASa,EAAIb,MAAMiD,SAAS,yBAI1CJ,EAAMlE,QAAQ,qBAGlBwK,UAAW,SAAUnL,GACjB,IAKIkE,EALAW,EAAQhG,EAAEyD,MACVwC,EAAOD,EAAMC,OACbjC,EAAMiC,EAAKqD,YACXjB,EAAOpC,EAAKgG,gBACZvC,EAAMzD,EAAKsG,eAIf,MAAKvI,EAAIsB,MAAMoE,IAAQ1D,EAAMoB,GAAG,IAAMiB,EAAKjG,WAAWE,SAAW,+BAAiC+F,EAAKjG,WAAWI,gBAAmBwD,EAAMoB,GAAG,2BAAuD,IAA3BiB,EAAKlG,mBAA/K,CAOA,GAHAhB,EAAE0E,iBACF1E,EAAE2E,2BAEE9F,EAAEwM,WAAWxI,EAAIyI,UAAU/C,KAASgD,OAAOC,UAAUC,eAAejI,KAAKX,EAAIyI,UAAW/C,GAExFrE,EAAWrB,EAAIyI,UAAU/C,OACtB,CAAA,IAAI1J,EAAEwM,WAAWnE,EAAKhD,UAKzB,OAHAA,EAAWgD,EAAKhD,UAO+B,IAA/CA,EAASV,KAAK0D,EAAKxB,SAAU6C,EAAKrB,EAAMlH,GACxCkH,EAAKlF,MAAMrB,QAAQ,oBACZuG,EAAKlF,MAAMmH,SAASnD,QAC3BL,EAAG+F,OAAOlI,KAAK0D,EAAKxB,SAAUwB,KAItCyE,WAAY,SAAU3L,GAClBA,EAAE2E,4BAGNiH,SAAU,SAAU5L,EAAG8E,GACnB,IAAIoC,EAAOrI,EAAEyD,MAAMwC,KAAK,mBACxBa,EAAG7B,KAAKN,KAAK0D,EAAKxB,SAAUwB,EAAMpC,GAAQA,EAAK+G,QAGnDC,UAAW,SAAU9L,GACjBA,EAAEsI,kBACF,IAAIzD,EAAQhG,EAAEyD,MACVwC,EAAOD,EAAMC,OACbjC,EAAMiC,EAAKqD,YACXjB,EAAOpC,EAAKgG,gBAEZjG,EAAMI,SAASiC,EAAKjG,WAAWE,WAAa0D,EAAMI,SAASiC,EAAKjG,WAAWI,iBAI/EwD,EACKkH,SAAS,CAAC7E,EAAKjG,WAAWC,MAAOgG,EAAKjG,WAAWG,SAAS4K,KAAK,MAE/D7C,SAASH,KAAK,sBAAsBiD,IAAIpH,GACxCqH,YAAYhF,EAAKjG,WAAWG,SAC5B+K,OAAO,IAAMjF,EAAKjG,WAAWC,OAC7BP,QAAQ,oBAGbkC,EAAIkG,UAAY7B,EAAK6B,UAAYlE,EAG9BhC,GAAOA,EAAI+G,OAAS/G,EAAI+G,MAAM3E,SAAS,yBACtCpC,EAAI+G,MAAMmC,SAAS7E,EAAKjG,WAAWC,OAInC2B,EAAI+G,OACJ1C,EAAKzD,gBAAgBD,KAAKX,EAAI+G,MAAO/G,EAAIb,SAIjDoK,SAAU,SAAUpM,GAChBA,EAAEsI,kBACF,IAAIzD,EAAQhG,EAAEyD,MACVwC,EAAOD,EAAMC,OACbjC,EAAMiC,EAAKqD,YACXjB,EAAOpC,EAAKgG,gBAEZjI,EAAIjC,UACJiE,EAAMqH,YAAYhF,EAAKjG,WAAWG,SAEtCyD,EAAMqH,YAAYhF,EAAKjG,WAAWC,OAClC2B,EAAIkG,UAAY,OAIxBpD,EAAK,CACD9B,KAAM,SAAUhB,EAAKC,EAAGC,GACpB,IAAI2C,EAAW7G,EAAEyD,MACbJ,EAAM,GASV,GANArD,EAAE,uBAAuB8B,QAAQ,aAGjCkC,EAAI6C,SAAWA,GAG6B,IAAxC7C,EAAInD,OAAOmE,KAAKL,KAAKkC,EAAU7C,GAOnC,IAAwB,IADF8C,EAAG+F,OAAOlI,KAAKkC,EAAU7C,GAC/C,CASA,GAHAA,EAAIZ,SAASuB,KAAKkC,EAAU7C,EAAKC,EAAGC,GAGhCF,EAAIa,OAAQ,CACZ,IAAI2I,EAAmBxJ,EAAIa,OAED,mBAAfb,EAAIa,SACX2I,EAAmBxJ,EAAIa,OAAOF,KAAKkC,EAAU7C,IAEjDX,EAAIwB,OAnuBP,SAAU4I,GAIf,IAHA,IAAIC,EAAM,EACNC,EAAMF,EAGNC,EAAME,KAAKC,IAAIH,EAAK3D,SAAS4D,EAAItK,IAAI,WAAY,KAAO,IACxDsK,EAAMA,EAAIrD,WACGqD,EAAIxG,WAAqE,EAA3D,YAAY2G,QAAQH,EAAItD,KAAK,YAAY0D,kBAIxE,OAAOL,EAwtBcM,CAAOnH,GAAY2G,EAIpC1G,EAAGmH,MAAMtJ,KAAKX,EAAIb,MAAOa,EAAKX,EAAIwB,QAGlCb,EAAIb,MAAMgH,KAAK,MAAM9G,IAAI,SAAUA,EAAIwB,OAAS,GAGhDb,EAAIb,MAAME,IAAIA,GAAKW,EAAIc,UAAUE,MAAMhB,EAAIc,UAAUC,SAAU,WAC3D8B,EAAS/E,QAAQ,uBAEjBgF,EAAG1B,UAAUpB,GACbA,EAAInD,OAAOuE,UAAUpB,KAGzB6C,EACKZ,KAAK,cAAejC,GACpBkJ,SAAS,uBAGdlN,EAAEO,UAAUyH,IAAI,uBAAuBH,GAAG,sBAAuBlC,EAAO+D,KAEpE1F,EAAIjC,UAEJ/B,EAAEO,UAAUsH,GAAG,gCAAiC,SAAU1G,GAGtD,IAAI+M,EAAMrH,EAASnD,SACnBwK,EAAI1J,MAAQ0J,EAAIpK,KAAO+C,EAAS9C,aAChCmK,EAAI7J,OAAS6J,EAAItK,IAAMiD,EAAShD,eAE5BG,EAAI2E,QAAW3E,EAAIoI,UAAejL,EAAEsE,OAASyI,EAAIpK,MAAQ3C,EAAEsE,OAASyI,EAAI1J,OAAYrD,EAAEuE,OAASwI,EAAItK,KAAOzC,EAAEuE,OAASwI,EAAI7J,QAEzH0D,WAAW,WACF/D,EAAIoI,UAA0B,OAAdpI,EAAIb,YAAuC,IAAda,EAAIb,OAClDa,EAAIb,MAAMrB,QAAQ,qBAEvB,WArDXV,EAAkB,UAPlBA,EAAkB,MAiE1B6D,KAAM,SAAUjB,EAAKgJ,GACjB,IAAInG,EAAW7G,EAAEyD,MAMjB,GAJIO,EADCA,IACK6C,EAASZ,KAAK,gBAAkB,IAIrC+G,IAAShJ,EAAInD,SAAkD,IAAxCmD,EAAInD,OAAOoE,KAAKN,KAAKkC,EAAU7C,GAA3D,CASA,GAJA6C,EACKU,WAAW,eACX8F,YAAY,uBAEbrJ,EAAI2E,OAAQ,CAEZZ,YAAsBY,EAInB3E,EAAI2E,OAHI,WACHA,EAAO1H,WAEC,IAEhB,WACW+C,EAAI2E,OACb,MAAOxH,GACL6C,EAAI2E,OAAS,MATN,IAAWA,EAc1BvH,EAAkB,KAElB4C,EAAIb,MAAMgH,KAAK,IAAMnG,EAAI5B,WAAWC,OAAOP,QAAQ,oBACnDkC,EAAIkG,UAAY,KAEhBlG,EAAIb,MAAMgH,KAAK,IAAMnG,EAAI5B,WAAWG,SAAS8K,YAAYrJ,EAAI5B,WAAWG,SAGxEvC,EAAEO,UAAUyH,IAAI,wBAAwBA,IAAI,uBAExChE,EAAIb,OACJa,EAAIb,MAAMa,EAAIc,UAAUG,MAAMjB,EAAIc,UAAUC,SAAU,WAE9Cf,EAAIqC,QACJrC,EAAIb,MAAMlC,SACVjB,EAAEmO,KAAKnK,EAAK,SAAU0F,GAClB,OAAQA,GACJ,IAAK,KACL,IAAK,WACL,IAAK,QACL,IAAK,UACD,OAAO,EAEX,QACI1F,EAAI0F,QAAOnB,EACX,WACWvE,EAAI0F,GACb,MAAOvI,IAET,OAAO,MAKvB4G,WAAW,WACPlB,EAAS/E,QAAQ,uBAClB,QAIfiF,OAAQ,SAAU/C,EAAKqE,GA2BnB,SAAS+F,EAAeC,GACpB,IAAIC,EAAQtO,EAAE,iBACd,GAAIqO,EAAKE,WACDF,EAAKG,kBACLF,EAAMG,OAAOlO,SAASmO,eAAeL,EAAKG,mBAE9CxO,EAAE,iBACGkN,SAAS,0BACTyB,KAAKN,EAAKE,YACV1M,SAASyM,GACVD,EAAKO,iBACLN,EAAMG,OAAOlO,SAASmO,eAAeL,EAAKO,uBAG9C,GAAIP,EAAKQ,WAAY,CAEjB,QAA8B,IAAnBR,EAAKS,UACZ,MAAM,IAAIlI,MAAM,8FAEpB0H,EAAMS,KAAKV,EAAKW,WAEhBV,EAAMK,KAAKN,EAAKW,MAGxB,OAAOV,OAlDS,IAATjG,IACPA,EAAOrE,GAIXA,EAAIb,MAAQnD,EAAE,uCAAuCkN,SAASlJ,EAAIiL,WAAa,IAAIhJ,KAAK,CACpFqD,YAAetF,EACfiI,gBAAmB5D,IAEpBrE,EAAIkL,UACHlP,EAAEmO,KAAKnK,EAAIkL,SAAU,SAAUxF,EAAK2E,GAChCrK,EAAIb,MAAMgM,KAAK,QAAUnL,EAAI0F,IAAK2E,KAI1CrO,EAAEmO,KAAK,CAAC,YAAa,WAAY,UAAW,SAAUpN,EAAG2J,GACrD1G,EAAI0G,GAAK,GACJrC,EAAKqC,KACNrC,EAAKqC,GAAK,MAIbrC,EAAKyC,aACNzC,EAAKyC,WAAa,IA+BtB9K,EAAEmO,KAAKnK,EAAIsB,MAAO,SAAUoE,EAAK2E,GAC7B,IAAIZ,EAAKzN,EAAE,uCAAuCkN,SAASmB,EAAKY,WAAa,IACzEG,EAAS,KACT1D,EAAS,KAqBb,GAjBA+B,EAAG5F,GAAG,QAAS7H,EAAEmF,MAKG,iBAATkJ,GAAmC,iBAAdA,EAAKgB,OACjChB,EAAO,CAACgB,KAAM,iBAGlBhB,EAAKtD,MAAQ0C,EAAGxH,KAAK,CACjBqD,YAAetF,EACfiI,gBAAmB5D,EACnBkE,eAAkB7C,SAKQ,IAAnB2E,EAAKS,UAEZ,IADA,IACgBQ,EADZC,EAsZxB,SAAwBC,GAIpB,IAHA,IAGgB9E,EAHZ+E,EAAID,EAAIE,MAAM,OACdC,EAAO,GAEF5O,EAAI,EAAM2J,EAAI+E,EAAE1O,GAAIA,IACzB2J,EAAIA,EAAEkF,OAAO,GAAG/E,cAGhB8E,EAAKE,KAAKnF,GAGd,OAAOiF,EAjamBG,CAAezB,EAAKS,WACrB/N,EAAI,EAAOuO,EAAKC,EAAIxO,GAAIA,IAC7B,IAAKsH,EAAKyC,WAAWwE,GAAK,CAEtB,IAAIS,GADJ1H,EAAKyC,WAAWwE,GAAMjB,GACHW,KAAKgB,MAAM,IAAIC,OAAO,UAAYX,EAAK,SAAU,MAChES,IACA1B,EAAKG,iBAAmBuB,EAAQ,GAChC1B,EAAKE,WAAawB,EAAQ,GAC1B1B,EAAKO,gBAAkBmB,EAAQ,IAEnC,MAKZ,GAAI1B,EAAKgB,MAAQ3N,EAAM2M,EAAKgB,MAExB3N,EAAM2M,EAAKgB,MAAM1K,KAAK8I,EAAIY,EAAMrK,EAAKqE,GAErCrI,EAAEmO,KAAK,CAACnK,EAAKqE,GAAO,SAAUtH,EAAG2J,GAC7BA,EAAEwF,SAASxG,GAAO2E,GAGdrO,EAAEwM,WAAW6B,EAAKhJ,gBAA0C,IAArBqF,EAAE+B,UAAU/C,SAA4C,IAAb1F,EAAIqL,OACtF3E,EAAE+B,UAAU/C,GAAO2E,EAAKhJ,gBAG7B,CAoBH,OAlBkB,iBAAdgJ,EAAKgB,KACL5B,EAAGP,SAAS,0BAA4B7E,EAAKjG,WAAWI,eACnC,SAAd6L,EAAKgB,KACZ5B,EAAGP,SAAS,qBAAuB7E,EAAKjG,WAAWI,eAC9B,QAAd6L,EAAKgB,MAAkBhB,EAAKgB,MACnCD,EAASpP,EAAE,mBAAmB6B,SAAS4L,GACvCW,EAAeC,GAAMxM,SAASuN,GAE9B3B,EAAGP,SAAS,sBACZlJ,EAAImM,UAAW,EACfnQ,EAAEmO,KAAK,CAACnK,EAAKqE,GAAO,SAAUtH,EAAG2J,GAC7BA,EAAEwF,SAASxG,GAAO2E,EAClB3D,EAAE0F,OAAO1G,GAAO2E,KAEbA,EAAK/I,QACZ+I,EAAKgB,KAAO,OAGRhB,EAAKgB,MACT,IAAK,eACD,MAEJ,IAAK,OACD3D,EAAS1L,EAAE,2CACNmP,KAAK,OAAQ,sBAAwBzF,GACrC8F,IAAInB,EAAKgC,OAAS,IAClBxO,SAASuN,GACd,MAEJ,IAAK,WACD1D,EAAS1L,EAAE,iCACNmP,KAAK,OAAQ,sBAAwBzF,GACrC8F,IAAInB,EAAKgC,OAAS,IAClBxO,SAASuN,GAEVf,EAAK9J,QACLmH,EAAOnH,OAAO8J,EAAK9J,QAEvB,MAEJ,IAAK,WACDmH,EAAS1L,EAAE,+CACNmP,KAAK,OAAQ,sBAAwBzF,GACrC8F,IAAInB,EAAKgC,OAAS,IAClBhG,KAAK,YAAagE,EAAKiC,UACvBC,UAAUnB,GACf,MAEJ,IAAK,QACD1D,EAAS1L,EAAE,4CACNmP,KAAK,OAAQ,sBAAwBd,EAAKmC,OAC1ChB,IAAInB,EAAKgC,OAAS,IAClBhG,KAAK,YAAagE,EAAKiC,UACvBC,UAAUnB,GACf,MAEJ,IAAK,SACD1D,EAAS1L,EAAE,6BACNmP,KAAK,OAAQ,sBAAwBzF,GACrC7H,SAASuN,GACVf,EAAKoC,UACLzQ,EAAEmO,KAAKE,EAAKoC,QAAS,SAAUJ,EAAO1B,GAClC3O,EAAE,qBAAqBwP,IAAIa,GAAO1B,KAAKA,GAAM9M,SAAS6J,KAE1DA,EAAO8D,IAAInB,EAAKiC,WAEpB,MAEJ,IAAK,MACDlC,EAAeC,GAAMxM,SAAS4L,GAC9BY,EAAKxM,SAAWwM,EAAKtD,MACrB0C,EAAGxH,KAAK,cAAeoI,GAAMnB,SAAS,wBACtCmB,EAAKhJ,SAAW,KAKZ,mBAAsBgJ,EAAK/I,MAAMoL,KAEjC5J,EAAG6J,gBAAgBtC,EAAMhG,EAAMgG,EAAK/I,OAGpCwB,EAAGC,OAAOsH,EAAMhG,GAEpB,MAEJ,IAAK,OACDrI,EAAEqO,EAAKU,MAAMlN,SAAS4L,GACtB,MAEJ,QACIzN,EAAEmO,KAAK,CAACnK,EAAKqE,GAAO,SAAUtH,EAAG2J,GAC7BA,EAAEwF,SAASxG,GAAO2E,GAGdrO,EAAEwM,WAAW6B,EAAKhJ,gBAA0C,IAArBqF,EAAE+B,UAAU/C,SAA4C,IAAb1F,EAAIqL,OACtF3E,EAAE+B,UAAU/C,GAAO2E,EAAKhJ,YAGhC+I,EAAeC,GAAMxM,SAAS4L,GAKlCY,EAAKgB,MAAsB,QAAdhB,EAAKgB,MAAgC,SAAdhB,EAAKgB,MAAiC,iBAAdhB,EAAKgB,OACjE3D,EACK7D,GAAG,QAASlC,EAAOqG,YACnBnE,GAAG,OAAQlC,EAAOuG,WAEnBmC,EAAKxN,QACL6K,EAAO7D,GAAGwG,EAAKxN,OAAQmD,IAK3BqK,EAAK5L,OACDzC,EAAEwM,WAAW6B,EAAK5L,MAClB4L,EAAKuC,MAAQvC,EAAK5L,KAAKkC,KAAKlB,KAAMA,KAAMgK,EAAI/D,EAAK2E,GAEvB,iBAAfA,EAAS,MACc,SAA9BA,EAAK5L,KAAKoO,UAAU,EAAG,IACU,SAA9BxC,EAAK5L,KAAKoO,UAAU,EAAG,IACO,SAA9BxC,EAAK5L,KAAKoO,UAAU,EAAG,IACO,SAA9BxC,EAAK5L,KAAKoO,UAAU,EAAG,IACO,SAA9BxC,EAAK5L,KAAKoO,UAAU,EAAG,GAKG,iBAAfxC,EAAS,MAAgD,QAA9BA,EAAK5L,KAAKoO,UAAU,EAAG,GAChExC,EAAKuC,MAAQvI,EAAKjG,WAAWK,KAAO,IAAM4F,EAAKjG,WAAWK,KAAO,WAAa4L,EAAK5L,KAEnF4L,EAAKuC,MAAQvI,EAAKjG,WAAWK,KAAO,IAAM4F,EAAKjG,WAAWK,KAAO,IAAM4L,EAAK5L,MAL5EgL,EAAGP,SAAS7E,EAAKjG,WAAWK,KAAO,IAAM4F,EAAKjG,WAAWK,KAAO,SAChE4L,EAAKuC,MAAQ5Q,EAAE,aAAeqO,EAAK5L,KAAO,WAQxB,iBAAhB4L,EAAU,MAChBZ,EAAGP,SAASmB,EAAKuC,OAEjBnD,EAAGqD,QAAQzC,EAAKuC,QAM5BvC,EAAK3C,OAASA,EACd2C,EAAKe,OAASA,EAGd3B,EAAG5L,SAASmC,EAAIb,QAGXa,EAAImM,UAAYnQ,EAAEE,QAAQI,kBAI3BmN,EAAG5F,GAAG,gCAAiClC,EAAOC,cAIjD5B,EAAI+G,OACL/G,EAAIb,MAAME,IAAI,UAAW,QAAQ6J,SAAS,qBAE9ClJ,EAAIb,MAAMtB,SAASmC,EAAInC,UAAYtB,SAASwQ,OAEhDC,OAAQ,SAAU7N,EAAO8N,GACrB,IAAIC,EAMJ/N,EAAME,IAAI,CAACD,SAAU,WAAY+N,QAAS,UAE1ChO,EAAM8C,KAAK,SACNiL,EAAU/N,EAAMqI,IAAI,IAAI4F,sBACrBxD,KAAKyD,KAAKH,EAAQE,wBAAwB1M,OAC1CvB,EAAMY,aAAe,GAE7BZ,EAAME,IAAI,CACND,SAAU,SACVkO,SAAU,MACVC,SAAU,aAGdpO,EAAMgH,KAAK,aAAagE,KAAK,WACzBrH,EAAGkK,OAAOhR,EAAEyD,OAAO,KAIlBwN,GACD9N,EAAMgH,KAAK,MAAMqH,UAAUnO,IAAI,CAC3BD,SAAU,GACV+N,QAAS,GACTG,SAAU,GACVC,SAAU,KACXxN,WAAW,WACV,OAAO/D,EAAEyD,MAAMwC,KAAK,YAIhC4G,OAAQ,SAAU7I,EAAKqE,GACnB,IAAIxB,EAAWpD,UACK,IAAT4E,IACPA,EAAOrE,EACP8C,EAAGkK,OAAOhN,EAAIb,QAGlB,IAAIsO,GAAkB,EAkEtB,OA/DAzN,EAAIb,MAAMsH,WAAW0D,KAAK,WACtB,IAII5L,EAJAmP,EAAQ1R,EAAEyD,MACViG,EAAMgI,EAAMzL,KAAK,kBACjBoI,EAAOrK,EAAIsB,MAAMoE,GACjBpH,EAAYtC,EAAEwM,WAAW6B,EAAK/L,WAAa+L,EAAK/L,SAASqC,KAAKkC,EAAU6C,EAAKrB,KAA4B,IAAlBgG,EAAK/L,SAmBhG,IAhBIC,EADAvC,EAAEwM,WAAW6B,EAAK9L,SACR8L,EAAK9L,QAAQoC,KAAKkC,EAAU6C,EAAKrB,QACZ,IAAjBgG,EAAK9L,UACQ,IAAjB8L,EAAK9L,WAMfkP,GAAkB,GAGtBC,EAAMnP,EAAU,OAAS,UAGzBmP,EAAMpP,EAAW,WAAa,eAAe+F,EAAKjG,WAAWE,UAEzDtC,EAAEwM,WAAW6B,EAAK5L,MAAO,CACzBiP,EAAMrE,YAAYgB,EAAKuC,OACvB,IAAIe,EAAatD,EAAK5L,KAAKkC,KAAKlB,KAAMoD,EAAU6K,EAAOhI,EAAK2E,GAClC,iBAAjB,EACLqD,EAAMxE,SAASyE,GAEfD,EAAMZ,QAAQa,GAItB,GAAItD,EAAKgB,KAKL,OAHAqC,EAAMvH,KAAK,2BAA2BE,KAAK,WAAY/H,GAG/C+L,EAAKgB,MACT,IAAK,OACL,IAAK,WACDhB,EAAK3C,OAAO8D,IAAInB,EAAKgC,OAAS,IAC9B,MAEJ,IAAK,WACL,IAAK,QACDhC,EAAK3C,OAAO8D,IAAInB,EAAKgC,OAAS,IAAIhG,KAAK,YAAagE,EAAKiC,UACzD,MAEJ,IAAK,SACDjC,EAAK3C,OAAO8D,KAAuB,IAAlBnB,EAAKiC,SAAiB,IAAMjC,EAAKiC,WAAa,IAKvEjC,EAAKlL,OAEwB2D,EAAG+F,OAAOlI,KAAKkC,EAAUwH,EAAMhG,KAExDoJ,GAAkB,KAIvBA,GAEXxD,MAAO,SAAUjK,EAAKa,GAGlB,IAAI8D,EAAS3E,EAAI2E,OAAS3I,EAAE,uCACvBqD,IAAI,CACDkB,OAAQjD,EAAKiD,SACbG,MAAOpD,EAAKoD,QACZyM,QAAS,QACT/N,SAAU,QACVwO,UAAW/M,EAAS,EACpBjB,IAAK,EACLE,KAAM,EACN+N,QAAS,EACTvE,OAAQ,mBACRwE,mBAAoB,SAEvB7L,KAAK,kBAAmBjC,GACxBnC,SAAStB,SAASwQ,MAClBlJ,GAAG,cAAelC,EAAOC,YACzBiC,GAAG,YAAalC,EAAOwC,YAU5B,YAP4C,IAAjC5H,SAASwQ,KAAKlH,MAAM0H,UAC3B5I,EAAOtF,IAAI,CACPD,SAAY,WACZmB,OAAUvE,EAAEO,UAAUgE,WAIvBoE,GAEXgI,gBAAiB,SAAU3M,EAAKqE,EAAM0J,GAclC,SAASC,EAAahO,EAAKqE,EAAM4J,QAEJ,IAAdA,GACPA,EAAY,CACRvL,MAAS,CACLsI,KAAM,6BACNvM,KAAM,6CAGVrC,OAAOqG,UACNA,QAAQC,OAASD,QAAQE,KAAKhC,KAAK8B,QAAS,yFAErB,iBAAdwL,IACdA,EAAY,CAACvL,MAAS,CAACsI,KAAMiD,KAEjCC,EAAqBlO,EAAKqE,EAAM4J,GAGpC,SAASC,EAAqBlO,EAAKqE,EAAM/C,QACX,IAAf+C,EAAKlF,OAA0BkF,EAAKlF,MAAMiE,GAAG,cAGxDpD,EAAI+G,MAAMsC,YAAYhF,EAAKjG,WAAWa,kBACtCe,EAAIsB,MAAQA,EACZwB,EAAGC,OAAO/C,EAAKqE,GAAM,GACrBvB,EAAG+F,OAAO7I,EAAKqE,GACfA,EAAKzD,gBAAgBD,KAAKX,EAAI+G,MAAO/G,EAAIb,QAtC7Ca,EAAI+G,MAAMmC,SAAS7E,EAAKjG,WAAWa,kBA2CnC8O,EAAQrB,KAzCR,SAA0B1M,EAAKqE,EAAM/C,QAGZ,IAAVA,GAEP0M,OAAazJ,GAEjB2J,EAAqBlO,EAAKqE,EAAM/C,IAkCN6M,KAAK1O,KAAMO,EAAKqE,GAAO2J,EAAaG,KAAK1O,KAAMO,EAAKqE,KAGtFjD,UAAW,SAASpB,GAChB,IAAIb,EAAQa,EAAIb,MACZiP,EAAcjP,EAAMO,SACpB2O,EAAYrS,EAAEI,QAAQmE,SACtB+N,EAAetS,EAAEI,QAAQkE,YACzBiO,EAAapP,EAAMoB,SACP8N,EAAbE,EACCpP,EAAME,IAAI,CACNkB,OAAW8N,EAAY,KACvBG,aAAc,SACdC,aAAc,OACd7O,IAAO0O,EAAe,QAEnBF,EAAYxO,IAAM0O,GAAkBF,EAAYxO,IAAM2O,EAAaD,EAAeD,IACzFlP,EAAME,IAAI,CACNO,IAAO0O,EAAe,SA0V1C,SAASI,EAAWC,GAChB,OAAQA,EAAKC,IAAM5S,EAAE,cAAgB2S,EAAKC,GAAK,MAAMpD,OAAUmD,EAAK3D,KArUxEhP,EAAE6S,GAAGvJ,YAAc,SAAUwJ,GACzB,IAAIrF,EAAKhK,KAAMsP,EAAKD,EACpB,GAAkB,EAAdrP,KAAK0D,OACL,QAAyB,IAAd2L,EACPrP,KAAKsI,QAAQjK,QAAQ,oBAClB,QAA2B,IAAhBgR,EAAU7O,QAA4C,IAAhB6O,EAAU5O,EAC9DT,KAAKsI,QAAQjK,QAAQ9B,EAAEiH,MAAM,cAAe,CACxCxB,MAAOqN,EAAU7O,EACjByB,MAAOoN,EAAU5O,EACjBiC,YAAa2M,EAAUzL,eAExB,GAAkB,SAAdyL,EAAsB,CAC7B,IAAI3P,EAAQM,KAAKsI,QAAQ9F,KAAK,eAAiBxC,KAAKsI,QAAQ9F,KAAK,eAAe9C,MAAQ,KACpFA,GACAA,EAAMrB,QAAQ,wBAEG,YAAdgR,EACP9S,EAAEsJ,YAAY,UAAW,CAAC0J,QAASvP,OAC5BzD,EAAEiT,cAAcH,IACvBA,EAAUE,QAAUvP,KACpBzD,EAAEsJ,YAAY,SAAUwJ,IACjBA,EACPrP,KAAK4J,YAAY,yBACTyF,GACRrP,KAAKyJ,SAAS,8BAGlBlN,EAAEmO,KAAK1M,EAAO,WACNgC,KAAK7B,WAAa6L,EAAG7L,WACrBmR,EAAG9M,KAAOxC,KAEVzD,EAAEuG,OAAOwM,EAAG9M,KAAM,CAACnE,QAAS,cAIpC6D,EAAOI,YAAYpB,KAAKoO,EAAG3K,OAAQ2K,GAGvC,OAAOtP,MAIXzD,EAAEsJ,YAAc,SAAUwJ,EAAWrC,GACR,iBAAdqC,IACPrC,EAAUqC,EACVA,EAAY,UAGO,iBAAZrC,EACPA,EAAU,CAAC7O,SAAU6O,QACK,IAAZA,IACdA,EAAU,IAId,IAAIyC,EAAIlT,EAAEuG,QAAO,EAAM,GAAI5E,EAAU8O,GAAW,IAC5C9I,EAAY3H,EAAEO,UACd4S,EAAWxL,EACXyL,GAAc,EAWlB,OATKF,EAAEF,SAAYE,EAAEF,QAAQ7L,QAIzBgM,EAAWnT,EAAEkT,EAAEF,SAASjH,QACxBmH,EAAEF,QAAUG,EAAS3H,IAAI,GACzB4H,GAAepT,EAAEkT,EAAEF,SAAS5L,GAAG7G,WAL/B2S,EAAEF,QAAUzS,SAQRuS,GAEJ,IAAK,SAED,GAAGM,EACCtM,EAAG+F,OAAOsG,QAEV,IAAI,IAAIE,KAAQ5R,EACTA,EAAMmL,eAAeyG,IACpBvM,EAAG+F,OAAOpL,EAAM4R,IAI5B,MAEJ,IAAK,SAED,IAAKH,EAAEtR,SACH,MAAM,IAAIgF,MAAM,yBAGpB,GAAIsM,EAAEtR,SAASoO,MAAM,yCACjB,MAAM,IAAIpJ,MAAM,4BAA8BsM,EAAEtR,SAAW,yCAE/D,IAAKsR,EAAE7M,SAAW6M,EAAE5N,OAAStF,EAAEwG,cAAc0M,EAAE5N,QAC3C,MAAM,IAAIsB,MAAM,sBAcpB,GAZArF,IACA2R,EAAEI,GAAK,eAAiB/R,EACnB6R,IACD5R,EAAW0R,EAAEtR,UAAYsR,EAAEI,KAE/B7R,EAAMyR,EAAEI,IAAMJ,GAGPpR,UACHoR,EAAEpR,QAAU,UAGXT,EAAa,CACd,IAAIiL,EAAiC,UAArB4G,EAAEK,eAA6B,oBAAsB,sBACjEC,EAAqB,CAGrBC,gCAAiC9N,EAAOsH,UACxCyG,+BAAgC/N,EAAO4H,SACvCoG,0BAA2BhO,EAAOC,WAClCgO,yBAA0BjO,EAAO8F,eACjCoI,yBAA0BlO,EAAO4F,gBAErCiI,EAAmBlH,GAAa3G,EAAO2G,UAEvC3E,EACKE,GAAG,CACAiM,+BAAgCnO,EAAOoH,SACvCgH,0BAA2BpO,EAAOqF,SAClCgJ,0BAA2BrO,EAAOiG,SAClC+H,0BAA2BhO,EAAOC,WAClCgO,yBAA0BjO,EAAOwG,eACjC0H,yBAA0BlO,EAAO0G,gBAClC,sBACFxE,GAAG,sBAAuB,sBAAuBlC,EAAOmH,YACxDjF,GAAG2L,EAAoB,sBAE5BnS,GAAc,EAclB,OAVA8R,EACKtL,GAAG,cAAgBqL,EAAEI,GAAIJ,EAAEtR,SAAUsR,EAAGvN,EAAOI,aAEhDqN,GAEAD,EAAStL,GAAG,SAAWqL,EAAEI,GAAI,WACzBtT,EAAEyD,MAAM6F,YAAY,aAIpB4J,EAAEpR,SACN,IAAK,QACDqR,EACKtL,GAAG,aAAeqL,EAAEI,GAAIJ,EAAEtR,SAAUsR,EAAGvN,EAAO6B,YAC9CK,GAAG,aAAeqL,EAAEI,GAAIJ,EAAEtR,SAAUsR,EAAGvN,EAAOsC,YACnD,MAEJ,IAAK,OACDkL,EAAStL,GAAG,QAAUqL,EAAEI,GAAIJ,EAAEtR,SAAUsR,EAAGvN,EAAOqB,OAClD,MAChB,IAAK,aACWmM,EAAStL,GAAG,aAAeqL,EAAEI,GAAIJ,EAAEtR,SAAUsR,EAAGvN,EAAOqB,OAa1DkM,EAAE7M,OACHS,EAAGC,OAAOmM,GAEd,MAEJ,IAAK,UACD,IAAIe,EACJ,GAAIb,EAAa,CAEb,IAAIJ,EAAUE,EAAEF,QAChBhT,EAAEmO,KAAK1M,EAAO,SAAU6R,EAAIJ,GAExB,IAAKA,EACD,OAAO,EAIX,IAAKlT,EAAEgT,GAAS5L,GAAG8L,EAAEtR,UACjB,OAAO,GAGXqS,EAAejU,EAAE,sBAAsBsN,OAAO,aAC7BnG,QAAU8M,EAAahO,OAAOgG,gBAAgBpF,SAASO,GAAGpH,EAAEkT,EAAEF,SAAS7I,KAAK+I,EAAEtR,YAC3FqS,EAAanS,QAAQ,mBAAoB,CAACkL,OAAO,IAGrD,IACQvL,EAAMyR,EAAEI,IAAInQ,OACZ1B,EAAMyR,EAAEI,IAAInQ,MAAMlC,gBAGfQ,EAAMyR,EAAEI,IACjB,MAAOnS,GACLM,EAAMyR,EAAEI,IAAM,KAKlB,OAFAtT,EAAEkT,EAAEF,SAAShL,IAAIkL,EAAEI,KAEZ,SAER,GAAKJ,EAAEtR,UAYP,GAAIJ,EAAW0R,EAAEtR,UAAW,EAC/BqS,EAAejU,EAAE,sBAAsBsN,OAAO,aAC7BnG,QAAU8M,EAAahO,OAAOgG,gBAAgBpF,SAASO,GAAG8L,EAAEtR,WACzEqS,EAAanS,QAAQ,mBAAoB,CAACkL,OAAO,IAGrD,IACQvL,EAAMD,EAAW0R,EAAEtR,WAAWuB,OAC9B1B,EAAMD,EAAW0R,EAAEtR,WAAWuB,MAAMlC,gBAGjCQ,EAAMD,EAAW0R,EAAEtR,WAC5B,MAAOT,GACLM,EAAMD,EAAW0R,EAAEtR,WAAa,KAGpC+F,EAAUK,IAAIxG,EAAW0R,EAAEtR,iBA3B3B+F,EAAUK,IAAI,qCACdhI,EAAEmO,KAAK1M,EAAO,SAAU6R,EAAIJ,GACxBlT,EAAEkT,EAAEF,SAAShL,IAAIkL,EAAEI,MAGvB9R,EAAa,GAEbD,EAAU,EACVF,IAFAI,EAAQ,IAIRzB,EAAE,2CAA2CiB,SAmBjD,MAEJ,IAAK,UAIKjB,EAAEE,QAAQG,cAAgBL,EAAEE,QAAQC,cAAqC,kBAAZsQ,GAAyBA,IACxFzQ,EAAE,wBAAwBmO,KAAK,WACvB1K,KAAKmP,IACL5S,EAAEsJ,YAAY,CACV1H,SAAU,gBAAkB6B,KAAKmP,GAAK,IACtCtN,MAAOtF,EAAEsJ,YAAY4K,SAASzQ,UAGvCJ,IAAI,UAAW,QAEtB,MAEJ,QACI,MAAM,IAAIuD,MAAM,sBAAwBkM,EAAY,KAG5D,OAAOrP,MAIXzD,EAAEsJ,YAAY6K,eAAiB,SAAUnQ,EAAKiC,QACtB,IAATA,IACPA,EAAO,IAGXjG,EAAEmO,KAAKnK,EAAIoM,OAAQ,SAAU1G,EAAK2E,GAC9B,OAAQA,EAAKgB,MACT,IAAK,OACL,IAAK,WACDhB,EAAKgC,MAAQpK,EAAKyD,IAAQ,GAC1B,MAEJ,IAAK,WACD2E,EAAKiC,WAAWrK,EAAKyD,GACrB,MAEJ,IAAK,QACD2E,EAAKiC,UAAYrK,EAAKoI,EAAKmC,QAAU,MAAQnC,EAAKgC,MAClD,MAEJ,IAAK,SACDhC,EAAKiC,SAAWrK,EAAKyD,IAAQ,OAO7C1J,EAAEsJ,YAAY8K,eAAiB,SAAUpQ,EAAKiC,GAyB1C,YAxBoB,IAATA,IACPA,EAAO,IAGXjG,EAAEmO,KAAKnK,EAAIoM,OAAQ,SAAU1G,EAAK2E,GAC9B,OAAQA,EAAKgB,MACT,IAAK,OACL,IAAK,WACL,IAAK,SACDpJ,EAAKyD,GAAO2E,EAAK3C,OAAO8D,MACxB,MAEJ,IAAK,WACDvJ,EAAKyD,GAAO2E,EAAK3C,OAAOrB,KAAK,WAC7B,MAEJ,IAAK,QACGgE,EAAK3C,OAAOrB,KAAK,aACjBpE,EAAKoI,EAAKmC,OAASnC,EAAKgC,UAMjCpK,GAuLXjG,EAAEsJ,YAAY4K,SAAW,SAAUG,GAC/B,IACI/O,EAAQ,GAIZ,OApLJ,SAASgP,EAAahP,EAAO4F,EAAW3J,GA0KpC,OAxKIA,EADCA,GACS,EAGd2J,EAAUiD,KAAK,WACX,IAGIoG,EACAlG,EAJAtD,EAAQ/K,EAAEyD,MACVkP,EAAOlP,KACP+Q,EAAW/Q,KAAK+Q,SAASzG,cAoB7B,OAfiB,UAAbyG,GAAwBzJ,EAAMZ,KAAK,2BAA2BhD,SAC9DoN,EAAQxJ,EAAM4D,OAGd6F,GADA7B,GADA5H,EAAQA,EAAMN,WAAWsB,SACZP,IAAI,IACDgJ,SAASzG,eAWrByG,GAEJ,IAAK,OACDnG,EAAO,CAACW,KAAMjE,EAAMoE,KAAK,SAAU7J,MAAO,IAC1C/D,EAAU+S,EAAajG,EAAK/I,MAAOyF,EAAMN,WAAYlJ,GACrD,MAGJ,IAAK,IAEL,IAAK,SACD8M,EAAO,CACHW,KAAMjE,EAAM4D,OACZrM,WAAYyI,EAAMoE,KAAK,YACvB9J,SACW,WACH0F,EAAMS,IAAI,GAAGxE,UAIzB,MAGJ,IAAK,WACL,IAAK,UACD,OAAQ+D,EAAMoE,KAAK,SACf,UAAK5G,EACL,IAAK,UACL,IAAK,WACD8F,EAAO,CACHW,KAAMjE,EAAMoE,KAAK,SACjB7M,WAAYyI,EAAMoE,KAAK,YACvB1M,KAAMsI,EAAMoE,KAAK,QACjB9J,SACW,WACH0F,EAAMS,IAAI,GAAGxE,UAIzB,MAEJ,IAAK,WACDqH,EAAO,CACHgB,KAAM,WACN/M,WAAYyI,EAAMoE,KAAK,YACvBH,KAAMjE,EAAMoE,KAAK,SACjBmB,WAAYvF,EAAMoE,KAAK,YAE3B,MACJ,IAAK,QACDd,EAAO,CACHgB,KAAM,QACN/M,WAAYyI,EAAMoE,KAAK,YACvBH,KAAMjE,EAAMoE,KAAK,SACjBqB,MAAOzF,EAAMoE,KAAK,cAClBkB,MAAOtF,EAAMoE,KAAK,MAClBmB,WAAYvF,EAAMoE,KAAK,YAE3B,MAEJ,QACId,OAAO9F,EAEf,MAEJ,IAAK,KACD8F,EAAO,UACP,MAEJ,IAAK,QACD,OAAQtD,EAAMoE,KAAK,SACf,IAAK,OACDd,EAAO,CACHgB,KAAM,OACNL,KAAMuF,GAAS7B,EAAWC,GAC1BrQ,WAAYyI,EAAMoE,KAAK,YACvBkB,MAAOtF,EAAMyE,OAEjB,MAEJ,IAAK,WACDnB,EAAO,CACHgB,KAAM,WACNL,KAAMuF,GAAS7B,EAAWC,GAC1BrQ,WAAYyI,EAAMoE,KAAK,YACvBmB,WAAYvF,EAAMoE,KAAK,YAE3B,MAEJ,IAAK,QACDd,EAAO,CACHgB,KAAM,QACNL,KAAMuF,GAAS7B,EAAWC,GAC1BrQ,WAAYyI,EAAMoE,KAAK,YACvBqB,QAASzF,EAAMoE,KAAK,QACpBkB,MAAOtF,EAAMyE,MACbc,WAAYvF,EAAMoE,KAAK,YAE3B,MAEJ,QACId,OAAO9F,EAGf,MAEJ,IAAK,SACD8F,EAAO,CACHgB,KAAM,SACNL,KAAMuF,GAAS7B,EAAWC,GAC1BrQ,WAAYyI,EAAMoE,KAAK,YACvBmB,SAAUvF,EAAMyE,MAChBiB,QAAS,IAEb1F,EAAMN,WAAW0D,KAAK,WAClBE,EAAKoC,QAAQhN,KAAK4M,OAASrQ,EAAEyD,MAAMkL,SAEvC,MAEJ,IAAK,WACDN,EAAO,CACHgB,KAAM,WACNL,KAAMuF,GAAS7B,EAAWC,GAC1BrQ,WAAYyI,EAAMoE,KAAK,YACvBkB,MAAOtF,EAAMyE,OAEjB,MAEJ,IAAK,QACD,MAEJ,QACInB,EAAO,CAACgB,KAAM,OAAQN,KAAMhE,EAAM0J,OAAM,IAI5CpG,IAEA/I,EAAM,SADN/D,GACyB8M,KAI1B9M,EAQP+S,CAAahP,EAHDtF,EAAEqU,GAGY5J,YAEnBnF,GAIXtF,EAAEsJ,YAAY3H,SAAWA,EACzB3B,EAAEsJ,YAAY5H,MAAQA,EAEtB1B,EAAEsJ,YAAY3D,OAASA,EACvB3F,EAAEsJ,YAAYxC,GAAKA,EACnB9G,EAAEsJ,YAAY7H,MAAQA"}