<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SlrmCenterBhawan extends Model
{
    use HasFactory;
    public function ulb()
    {
        return $this->belongsTo(User::class, 'ulb_id');
    }
    public function division()
    {
        return $this->belongsTo(MasterDivision::class, 'division_id');
    }
    protected $fillable = [
        'dateInput',
        'slrmcenter_type',   // <-- Add this line
        'slrmcenter_location',
        'slrmcenter_ward_no',
        'slrmcenter_ward_name',
        'slrmcenter_total_area',
        'slrmcenter_longitude',
        'slrmcenter_latitude',
        'slrmcenter_remark',
    ];
}
