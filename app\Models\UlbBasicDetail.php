<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UlbBasicDetail extends Model
{
    use HasFactory;

    protected $fillable = [
        'ulb_id',
        'year_of_establishment',
        'area',
        'population',
        'number_of_wards',
        'official_website',
        'contact_details',
        'attempts'
    ];

    public function ulb()
    {
        return $this->belongsTo(User::class, 'ulb_id');
    }
} 