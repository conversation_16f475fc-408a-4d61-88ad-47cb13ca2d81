<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Toilet extends Model
{
    use HasFactory;
    public function ulb()
    {
        return $this->belongsTo(User::class, 'ulb_id');
    }
    public function division()
    {
        return $this->belongsTo(MasterDivision::class, 'division_id');
    }
    protected $fillable = [
        'dateInput',
        'toilets_type',
        'toilets_location',
        'toilets_ward_no',
        'toilets_ward_name',
        'toilets_total_seat',
        'toilets_working_seat',
        'toilets_latitude',
        'toilets_longitude',
        'toilets_remark',
    ];
}
