<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class GouthanGovdhamKanjiHouse extends Model
{
   use HasFactory;
    public function ulb()
    {
        return $this->belongsTo(User::class,'ulb_id');
    }
    public function division()
    {
        return $this->belongsTo(MasterDivision::class, 'division_id');
    }
     protected $fillable = [
        'dateInput',
        'ggk_type',                 // <-- Add this line
        'ggk_total_capacity',
        'ggk_total_area',
        'total_maveshi_at_present',
        'ggk_longitude',
        'ggk_latitude',
        'ggk_remark',
    ];
}
