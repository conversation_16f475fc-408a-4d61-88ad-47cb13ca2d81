<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TalabInformation extends Model
{
    use HasFactory;
    public function ulb()
    {
        return $this->belongsTo(User::class,'ulb_id');
    }
    public function division()
    {
        return $this->belongsTo(MasterDivision::class, 'division_id');
    }
    protected $fillable = [
        'ulb_id',
        'dateInput',
        'ward_no',
        'ward_name',
        'talab_address',
        'longitude',
        'latitude',
        'area',
        'unique_id', // optional if you are allowing edits on this
    ];
    
}
