<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UlbInfrastructureService extends Model
{
    use HasFactory;

    protected $fillable = [
        'ulb_id',
        'has_water_supply',
        'water_supply_coverage_percentage',
        'has_sewage_system',
        'sewage_system_coverage_percentage',
        'solid_waste_management_details',
        'public_toilets_count',
        'public_toilets_locations',
        'roads_status',
        'drainage_status',
        'street_lighting_coverage_percentage',
        'public_transport_facilities',
        'attempts',
    ];

    protected $casts = [
        'has_water_supply' => 'boolean',
        'has_sewage_system' => 'boolean',
        'water_supply_coverage_percentage' => 'decimal:2',
        'sewage_system_coverage_percentage' => 'decimal:2',
        'street_lighting_coverage_percentage' => 'decimal:2',
    ];

    /**
     * Get the user that owns the record.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'ulb_id');
    }
}
