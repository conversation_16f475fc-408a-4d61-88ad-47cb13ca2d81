<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class GardenInfo extends Model
{
    use HasFactory;
    public function ulb()
    {
        return $this->belongsTo(User::class,'ulb_id');
    }
    public function division()
    {
        return $this->belongsTo(MasterDivision::class, 'division_id');
    }
    protected $fillable = [
        'ward_no',
        'ward_name',
        'garden_address',
        'area',
        'longitude',
        'latitude',
        'ulb_id',
        'dateInput',
        'unique_id',
    ];
}
