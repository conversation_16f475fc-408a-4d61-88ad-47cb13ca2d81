<?php

use App\Http\Controllers\AdminAuthController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Controller;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\DepatmentController;
use App\Http\Controllers\UlbController;
use App\Http\Controllers\JdController;

use Faker\Guesser\Name;
use Illuminate\Support\Facades\Alert;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/


Route::get('admin/logout', [AdminAuthController::class, 'logout'])->name('admin.logout');
Route::get('ulb/logout', [AdminAuthController::class, 'ulbLogout'])->name('ulb.logout');

Route::middleware(['guest:web'])->group(function () {
    Route::get('/', function () {
        return view('login');
    });
    Route::get('admin/login', [AdminAuthController::class, 'adminLogin'])->name('admin.loginpage');
    Route::post('admin-login', [AdminAuthController::class, 'doLogin'])->name('admin.login');

    Route::post('ulb-login', [AdminAuthController::class, 'ulbLogin'])->name('ulb.login');
    Route::get('login', [Controller::class, 'login'])->name('ulb.loginpage');
    Route::get('electionresult', [Controller::class, 'electionresult'])->name('election.result');
    Route::get('export-excelmayor', [Controller::class, 'exportexcelmayor'])->name('exportexcel.mayor');
    Route::get('export-excelchairman', [Controller::class, 'exportexcelchairman'])->name('exportexcel.chairman');
});

Route::middleware(['auth:admin', 'checkRole:1'])->group(function () {
    Route::get('admindashboard', [AdminController::class, 'admindashboard'])->name('admin.dashboard');
});

Route::middleware(['auth:admin', 'checkRole:2'])->group(function () {
    Route::get('/update-status/{status}', [DepatmentController::class, 'updateStatus'])->name('status.update');
    Route::get('departmentdashboard', [DepatmentController::class, 'departmentdashboard'])->name('department.dashboard');
    Route::get('deptrajasvasuli', [DepatmentController::class, 'deptrajasvasuli'])->name('dept.rajasvasuli');
    Route::get('deptemployeedetail', [DepatmentController::class, 'deptemployeedetail'])->name('dept.employeedetail');
    Route::post('addulbemployee', [DepatmentController::class, 'addulbemployee'])->name('addulb.employee');
    Route::get('deptjdemployeedetail', [DepatmentController::class, 'deptjdemployeedetail'])->name('jd.employeedetail');
    Route::post('addjdemployee', [DepatmentController::class, 'addjdemployee'])->name('addjd.employee');
    Route::get('deptleavedetail', [DepatmentController::class, 'deptleavedetail'])->name('dept.leavedetail');
    Route::get('deptsalarydetail', [DepatmentController::class, 'deptsalarydetail'])->name('dept.salarydetail');
    Route::get('deptSuspensionReport', [DepatmentController::class, 'deptSuspensionReport'])->name('deptSuspension.Report');
    Route::get('deptelectricityyearlydata', [DepatmentController::class, 'deptelectricityyearlydata'])->name('deptelectricityyearly.data');
    Route::get('incomeexpenditurereport', [DepatmentController::class, 'incomeexpenditurereport'])->name('incomeexpenditure.report');
    // Route::get('deptincomeexpenditure', [DepatmentController::class, 'deptincomeexpenditurereport'])->name('deptincomeexpenditure.report');
    // Route::get('/deptincomeexpenditure/{ulbId}/details', [DepatmentController::class, 'inconeexpendituredetail'])->name('inconeexpenditure.detail');

    Route::get('depteletricitybilldetail', [DepatmentController::class, 'depteletricitybilldetail'])->name('dept.eletricitybilldetail');
    Route::get('deptnewmeterdetail', [DepatmentController::class, 'deptnewmeterdetail'])->name('dept.newmeterdetail');
    Route::get('ulbwiserajasvasulli', [DepatmentController::class, 'ulbwiserajasvasulli'])->name('ulbwise.rajasvasulli');
    Route::get('monthwiserajasvasulli', [DepatmentController::class, 'monthwiserajasvasulli'])->name('monthwise.rajasvasulli');
    Route::get('divisionwiserajasvasulli', [DepatmentController::class, 'divisionwiserajasvasulli'])->name('division.wiserajasvasulli');
    Route::get('rajasvasulliform', [DepatmentController::class, 'rajasvasulliform'])->name('rajasvasulli.form');
    Route::post('rajasvasulliformsubmit', [DepatmentController::class, 'rajasvasulliformsubmit'])->name('rajasvasulliform.submit');
    Route::get('addmasterdata', [DepatmentController::class, 'addmasterdata'])->name('addmaster.data');
    Route::get('addemptypedata', [DepatmentController::class, 'addemptypedata'])->name('addemptype.data');
    Route::post('addemptypedatasubmit', [DepatmentController::class, 'addemptypedatasubmit'])->name('addemptype.datasubmit');
    Route::post('addemptypeupdate/{id}', [DepatmentController::class, 'addemptypeupdate'])->name('addemptype.update');
    Route::get('addemppostdata', [DepatmentController::class, 'addemppostdata'])->name('addemppost.data');
    Route::post('addemppostdatasubmit', [DepatmentController::class, 'addemppostdatasubmit'])->name('addemppost.datasubmit');
    Route::post('addemppostupdate/{id}', [DepatmentController::class, 'addemppostupdate'])->name('addemppost.update');
    Route::get('addemppayscaledata', [DepatmentController::class, 'addemppayscaledata'])->name('addemppayscale.data');
    Route::post('addemppayscaledatasubmit', [DepatmentController::class, 'addemppayscaledatasubmit'])->name('addemppayscale.datasubmit');
    Route::post('addpayscaleupdate/{id}', [DepatmentController::class, 'addpayscaleupdate'])->name('addpayscale.update');
    Route::get('adddistrictdata', [DepatmentController::class, 'adddistrictdata'])->name('adddistrict.data');
    Route::post('adddistrictdatasubmit', [DepatmentController::class, 'adddistrictdatasubmit'])->name('adddistrict.datasubmit');
    Route::post('adddistrictupdate/{id}', [DepatmentController::class, 'adddistrictupdate'])->name('adddistrict.update');
    Route::get('addulb', [DepatmentController::class, 'addulb'])->name('add.ulb');
    Route::post('addulbsubmit', [DepatmentController::class, 'addulbsubmit'])->name('addulb.submit');
    Route::get('electricitymaindata', [DepatmentController::class, 'electricitymaindata']);

    Route::get('verifieddata', [DepatmentController::class, 'verifieddata'])->name('verified.data');
     Route::get('electricityseptdata', [DepatmentController::class, 'electricityseptdata'])->name('electricity.septdata');
     Route::get('overalldata', [DepatmentController::class, 'overalldata'])->name('overall.data');
     Route::get('newconnection', [DepatmentController::class, 'newconnection'])->name('new.connection');



    Route::get('deptrajasvasulireport', [DepatmentController::class, 'deptrajasvasulireport'])->name('dept.rajasvasulireport');
    Route::get('deptrajasvasuliedited/{ulb_id}', [DepatmentController::class, 'deptrajasvasuliedited'])->name('dept.rajasvasuliedited');
    Route::put('deptrevenueupdate/{id}', [DepatmentController::class, 'deptrevenueupdate'])->name('deptrevenue.update');

    // ulb allotments route
    Route::get('deptulballotmantdetail', [DepatmentController::class, 'deptulballotmantdetail'])->name('dept.ulballotmantdetail');
    Route::get('ulballotmentform', [DepatmentController::class, 'ulballotmentform'])->name('ulballotment.form');
    Route::post('ulballotmentformsubmit', [DepatmentController::class, 'ulballotmentformsubmit'])->name('ulballotmentform.submit');
    Route::post('appatnidhisubmit', [DepatmentController::class, 'appatnidhisubmit'])->name('appatnidhi.submit');
    Route::post('mahapaurnidhisubmit', [DepatmentController::class, 'mahapaurnidhisubmit'])->name('mahapaurnidhi.submit');
    Route::post('parshadnidhisubmit', [DepatmentController::class, 'parshadnidhisubmit'])->name('parshadnidhi.submit');
    Route::post('swachcommandosubmit', [DepatmentController::class, 'swachcommandosubmit'])->name('swachcommando.submit');
    Route::post('cleancitysubmit', [DepatmentController::class, 'cleancitysubmit'])->name('cleancity.submit');
    Route::post('lambitvetansubmit', [DepatmentController::class, 'lambitvetansubmit'])->name('lambitvetan.submit');
    Route::post('anyakaryasubmit', [DepatmentController::class, 'anyakaryasubmit'])->name('anyakarya.submit');
    // Route::post('jalnivaransubmit', [DepatmentController::class, 'parshadnidhisubmit'])->name('parshadnidhi.submit');
    Route::post('peyjalnivaransubmit', [DepatmentController::class, 'peyjalnivaransubmit'])->name('peyjalnivaran.submit');
    Route::post('jalpradaygirhsubmit', [DepatmentController::class, 'jalpradaygirhsubmit'])->name('jalpradaygirh.submit');
    Route::post('mudranksulaksubmit', [DepatmentController::class, 'mudranksulaksubmit'])->name('mudranksulak.submit');
    Route::post('barsubmit', [DepatmentController::class, 'barsubmit'])->name('bar.submit');
    Route::post('yatrikarubmit', [DepatmentController::class, 'yatrikarsubmit'])->name('yatrikar.submit');
    Route::post('utpadkarubmit', [DepatmentController::class, 'utpadkarsubmit'])->name('utpadkar.submit');
    Route::post('uploadexcel', [DepatmentController::class, 'uploadexcel'])->name('uploadexcel.submit');
    Route::post('uploadexcelappatnidhi', [DepatmentController::class, 'uploadexcelappatnidhi'])->name('uploadexcel.appatnidhi');
    Route::post('uploadexcelMahapaur', [DepatmentController::class, 'uploadexcelMahapaur'])->name('uploadexcel.Mahapaur');
    Route::post('uploadexcelParshadnidhi', [DepatmentController::class, 'uploadexcelParshadnidhi'])->name('uploadexcel.Parshadnidhi');
    Route::post('uploadexcelSwachcommando', [DepatmentController::class, 'uploadexcelSwachcommando'])->name('uploadexcel.Swachcommando');
    Route::post('uploadexcelMissioncleancity', [DepatmentController::class, 'uploadexcelMissioncleancity'])->name('uploadexcel.Missioncleancity');
    Route::post('uploadexcelLambitVetan', [DepatmentController::class, 'uploadexcelLambitVetan'])->name('uploadexcel.LambitVetan');
    Route::post('uploadexcelAnyaKarya', [DepatmentController::class, 'uploadexcelAnyaKarya'])->name('uploadexcel.AnyaKarya');
    Route::post('uploadexcelPeyjalkast', [DepatmentController::class, 'uploadexcelPeyjalkast'])->name('uploadexcel.Peyjalkast');
    Route::post('uploadexcelJalPradaygirh', [DepatmentController::class, 'uploadexcelJalPradaygirh'])->name('uploadexcel.JalPradaygirh');
    Route::post('uploadexcelMudranksulk', [DepatmentController::class, 'uploadexcelMudranksulk'])->name('uploadexcel.Mudranksulk');
    Route::post('uploadexcelBar', [DepatmentController::class, 'uploadexcelBar'])->name('uploadexcel.Bar');
    Route::post('uploadexcelYatrikar', [DepatmentController::class, 'uploadexcelYatrikar'])->name('uploadexcel.Yatrikar');
    Route::post('uploadexcelUtapadkar', [DepatmentController::class, 'uploadexcelUtapadkar'])->name('uploadexcel.Utapadkar');
    Route::get('ulb-list', [DepatmentController::class, 'ulbList'])->name('ulb.list');
    // Route::post('updatepassword', [DepatmentController::class, 'updatepassword'])->name('update.password');
    Route::post('admin-profile-update/{id}', [DepatmentController::class, 'adminUlbPasswordUpdate'])->name('admin.passwordUpdate');

    //routes for excel download
    Route::get('export-excelrajasvasulidetailreport', [DepatmentController::class, 'ExcelRajasvasuliDetailReport'])->name('export.Excel.RajasvasuliDetailReport');
    Route::get('export-excelulbwise', [DepatmentController::class, 'exportexcelulbwise'])->name('export.excelulbwise');
    Route::get('export-excelmonthwiserajasvasulli', [DepatmentController::class, 'exportexcelmonthwiserajasvasulli'])->name('export.excelmonthwiserajasvasulli');
    Route::get('export-excelEmployee', [DepatmentController::class, 'exportexcelEmployee'])->name('export.excelEmployee');
    Route::get('export-excelJdEmployee', [DepatmentController::class, 'exportexcelJdEmployee'])->name('export.excelJdEmployee');
    Route::get('export-exceldeptLeaveDetail', [DepatmentController::class, 'exportdeptexcelLeaveDetail'])->name('export.deptexcelLeaveDetail');
    Route::get('export-depyexcelElectricityBill', [DepatmentController::class, 'deptexcelElectricityBill'])->name('export.deptexcelElectricityBill');
    Route::get('export-deptexcelNewMeter', [DepatmentController::class, 'deptexportexcelNewMeter'])->name('export.deptexcelNewMeter');
    Route::get('export-exceldeptRegularSalary', [DepatmentController::class, 'exportexceldeptRegularSalary'])->name('export.exceldeptRegularSalary');
    Route::get('export-exceldeptPlacementSalary', [DepatmentController::class, 'exportexceldeptPlacementSalary'])->name('export.exceldeptPlacementSalary');
    Route::get('export-excelreinstateReport', [DepatmentController::class, 'exportexcelreinstateReport'])->name('export.excelreinstateReport');
    Route::get('export-excelsuspensionReport', [DepatmentController::class, 'exportexcelsuspensionReport'])->name('export.excelsuspensionReport');

    // ulb allotment excel export
    Route::get('export-excelchungimadh', [DepatmentController::class, 'exportexcelchungimadh'])->name('export.excelchungimadh');
    Route::get('export-excelappatnidhi', [DepatmentController::class, 'exportexcelappatnidhi'])->name('export.excelappatnidhi');
    Route::get('export-excelmahapaurnidhi', [DepatmentController::class, 'exportexcelmahapaurnidhi'])->name('export.excelmahapaurnidhi');
    Route::get('export-excelparshadnidhi', [DepatmentController::class, 'exportexcelparshadnidhi'])->name('export.excelparshadnidhi');
    Route::get('export-excelswachcommando', [DepatmentController::class, 'exportexcelswachcommando'])->name('export.excelswachcommando');
    Route::get('export-excelcleancity', [DepatmentController::class, 'exportexcelcleancity'])->name('export.excelcleancity');
    Route::get('export-excellambitvetan', [DepatmentController::class, 'exportexcellambitvetan'])->name('export.excellambitvetan');
    Route::get('export-excelanyakarya', [DepatmentController::class, 'exportexcelanyakarya'])->name('export.excelanyakarya');
    Route::get('export-excelpeyjalnivaran', [DepatmentController::class, 'exportexcelpeyjalnivaran'])->name('export.excelpeyjalnivaran');
    Route::get('export-exceljalpradaygirh', [DepatmentController::class, 'exportexceljalpradaygirh'])->name('export.exceljalpradaygirh');
    Route::get('export-excelmudranksulak', [DepatmentController::class, 'exportexcelmudranksulak'])->name('export.excelmudranksulak');
    Route::get('export-excelbar', [DepatmentController::class, 'exportexcelbar'])->name('export.excelbar');
    Route::get('export-excelyatrikar', [DepatmentController::class, 'exportexcelyatrikar'])->name('export.excelyatrikar');
    Route::get('export-excelutpadkar', [DepatmentController::class, 'exportexcelutpadkar'])->name('export.excelutpadkar');

    Route::get('deptotherinformation', [DepatmentController::class, 'deptotherinformation']);
    Route::get('deptmuktidham', [DepatmentController::class, 'deptmuktidham']);
    Route::get('depttalabinformation', [DepatmentController::class, 'depttalabinformation']);
    Route::get('deptgardeninformation', [DepatmentController::class, 'deptgardeninformation']);
    Route::get('deptcomplexinformation', [DepatmentController::class, 'deptcomplexinformation']);
    Route::get('deptstreetlightinformation', [DepatmentController::class, 'deptstreetlightinformation']);
    Route::get('deptplaygroundinformation', [DepatmentController::class, 'deptplaygroundinformation']);
    Route::get('depthandpumpinformation', [DepatmentController::class, 'depthandpumpinformation']);
    Route::get('deptpanitankiinformation', [DepatmentController::class, 'deptpanitankiinformation']);
    Route::get('deptpayjalinformation', [DepatmentController::class, 'deptpayjalinformation']);
    Route::get('deptjaljanitbimari', [DepatmentController::class, 'deptjaljanitbimari']);
    Route::get('depttrafficlight', [DepatmentController::class, 'depttrafficlight']);
    Route::get('deptgovtbuilding', [DepatmentController::class, 'deptgovtbuilding']);
    Route::get('deptrainwaterharvesting', [DepatmentController::class, 'deptrainwaterharvesting']);
    Route::get('deptslrmbhawans', [DepatmentController::class, 'deptslrmbhawans']);
    Route::get('deptgouthangovdhamkanjihouse', [DepatmentController::class, 'deptgouthangovdhamkanjihouse']);
    Route::get('deptstpinfo', [DepatmentController::class, 'deptstpinfo']);
    Route::get('deptwtpinfo', [DepatmentController::class, 'deptwtpinfo']);
    Route::get('deptgyminfo', [DepatmentController::class, 'deptgyminfo']);
    Route::get('depttoiletinfo', [DepatmentController::class, 'depttoiletinfo']);

    Route::get('retirementlist', [DepatmentController::class, 'retirementlist']);


    Route::get('export-excelmuktidham', [DepatmentController::class, 'exportExcelMuktidham'])->name('export.excelMuktidham');
    Route::get('export-exceltalabinformation', [DepatmentController::class, 'exportExcelTalabInformation'])->name('export.excelTalabInformation');
    Route::get('export-excelgardeninformation', [DepatmentController::class, 'exportExcelGardenInformation'])->name('export.excelGardenInformation');
    Route::get('export-excelcomplexinformation', [DepatmentController::class, 'exportExcelComplexInformation'])->name('export.excelComplexInformation');
    Route::get('export-excelstreetlightinformation', [DepatmentController::class, 'exportExcelStreetlightInformation'])->name('export.excelStreetlightInformation');
    Route::get('export-excelplaygroundinformation', [DepatmentController::class, 'exportExcelPlaygroundInformation'])->name('export.excelPlaygroundInformation');
    Route::get('export-excelhandpumpinformation', [DepatmentController::class, 'exportExcelHandpumpInformation'])->name('export.excelHandpumpInformation');
    Route::get('export-excelpanitankiinformation', [DepatmentController::class, 'exportExcelPanitankiInformation'])->name('export.excelPanitankiInformation');
    Route::get('export-excelpayjalinformation', [DepatmentController::class, 'exportExcelPayjalInformation'])->name('export.excelPayjalInformation');
    Route::get('export-exceljaljanitbimari', [DepatmentController::class, 'exportExceljaljanitbimari'])->name('export.exceljaljanitbimari');

    Route::get('censusreport', [DepatmentController::class, 'censusreport'])->name('census.report');
    Route::get('censusreportsubmit', [DepatmentController::class, 'censusreportsubmit'])->name('census_report');
});

Route::middleware(['auth:admin', 'checkRole:3'])->group(function () {
    Route::get('jddashboard', [JdController::class, 'jddashboard'])->name('jddashboard');
    Route::get('jdprofile', [JdController::class, 'jdprofile']);
    Route::post('jd-profile-update/{id}', [JdController::class, 'JdPasswordUpdate'])->name('jd.passwordUpdate');
    Route::get('jdempregistration', [JdController::class, 'jdempregistration'])->name('jdemp.registration');
    Route::post('jdempregistrationsubmit', [JdController::class, 'jdempregistrationsubmit'])->name('jdempregistration.submit');
    Route::get('jdempreport', [JdController::class, 'jdempreport'])->name('jdemp.report');
    Route::get('jdempregistration/{id}/edit', [JdController::class, 'jdempedit'])->name('jdemp.edit');
    Route::post('jdempregistration/{id}/update', [JdController::class, 'jdempupdate'])->name('jdemp.update');
    Route::get('export-excelempdetail', [JdController::class, 'exportexcelempdetail'])->name('export.excelempdetail');

    Route::get('rajasvasulidetail', [JdController::class, 'rajasvasulidetail'])->name('rajasvasuli.detail');
    Route::get('rajasvasuliulbwise', [JdController::class, 'rajasvasuliulbwise'])->name('rajasvasuli.ulbwise');
    Route::post('verifyData', [JdController::class, 'verifyData'])->name('verify.data');
    Route::post('verifyallRajasvasuliData', [JdController::class, 'verifyallRajasvasuliData'])->name('verifyallRajasvasuli.Data');
    Route::get('jdemployeedetail', [JdController::class, 'jdemployeedetail'])->name('jdemployee.detail');
    Route::post('verifyEmpData', [JdController::class, 'verifyEmpData'])->name('verify.EmpData');
    Route::post('verifyallEmpData', [JdController::class, 'verifyallEmpData'])->name('verifyallEmp.Data');
    Route::get('jdleavedetail', [JdController::class, 'jdleavedetail'])->name('jdleave.detail');
    Route::post('verifyleavedata', [JdController::class, 'verifyleavedata'])->name('verifyleave.data');
    Route::post('verifyAllleavedata', [JdController::class, 'verifyAllleavedata'])->name('verifyAllleave.data');
    Route::get('jdsalarydetails', [JdController::class, 'jdsalarydetails'])->name('jdsalary.details');
    Route::post('verifysalaryData', [JdController::class, 'verifysalaryData'])->name('verifysalary.Data');
    Route::post('verifyallsalaryData', [JdController::class, 'verifyallsalaryData'])->name('verifyallsalary.Data');
    Route::post('verifyplacementsalaryData', [JdController::class, 'verifyplacementsalaryData'])->name('verifyplacementsalary.Data');
    Route::post('verifyallplacementsalaryData', [JdController::class, 'verifyallplacementsalaryData'])->name('verifyallplacementsalary.Data');
    Route::get('jdelectricitybill', [JdController::class, 'jdelectricitybill'])->name('jdelectricity.bill');
    Route::post('verifyelectricitybill', [JdController::class, 'verifyelectricitybill'])->name('verifyelectricity.bill');
    Route::post('verifyAllelectricitybill', [JdController::class, 'verifyAllelectricitybill'])->name('verifyAllelectricity.bill');
    Route::get('jdnewmeter', [JdController::class, 'jdnewmeter'])->name('jdnew.meter');
    Route::post('verifynewmeter', [JdController::class, 'verifynewmeter'])->name('verifynew.meter');
    Route::post('verifyAllnewmeter', [JdController::class, 'verifyAllnewmeter'])->name('verifyAllnew.meter');
    Route::get('jdulballotment', [JdController::class, 'jdulballotment'])->name('jd.ulballotment');
    Route::post('verifychungimadh', [JdController::class, 'verifychungimadh'])->name('verify.chungimadh');
    Route::post('verifyAllchungimadh', [JdController::class, 'verifyAllchungimadh'])->name('verifyAll.chungimadh');
    Route::post('verifyappatnidhi', [JdController::class, 'verifyappatnidhi'])->name('verify.appatnidhi');
    Route::post('verifyAllappatnidhi', [JdController::class, 'verifyAllappatnidhi'])->name('verifyAll.appatnidhi');
    Route::post('verifymahapaurnidhi', [JdController::class, 'verifymahapaurnidhi'])->name('verify.mahapaurnidhi');
    Route::post('verifyAllmahapaurnidhi', [JdController::class, 'verifyAllmahapaurnidhi'])->name('verifyAll.mahapaurnidhi');
    Route::post('verifyparshadnidhi', [JdController::class, 'verifyparshadnidhi'])->name('verify.parshadnidhi');
    Route::post('verifyAllparshadnidhi', [JdController::class, 'verifyAllparshadnidhi'])->name('verifyAll.parshadnidhi');
    Route::post('verifyswachcommando', [JdController::class, 'verifyswachcommando'])->name('verify.swachcommando');
    Route::post('verifyAllswachcommando', [JdController::class, 'verifyAllswachcommando'])->name('verifyAll.swachcommando');
    Route::post('verifycleancity', [JdController::class, 'verifycleancity'])->name('verify.cleancity');
    Route::post('verifyAllcleancity', [JdController::class, 'verifyAllcleancity'])->name('verifyAll.cleancity');
    Route::post('verifylambitvetan', [JdController::class, 'verifylambitvetan'])->name('verify.lambitvetan');
    Route::post('verifyAlllambitvetan', [JdController::class, 'verifyAlllambitvetan'])->name('verifyAll.lambitvetan');
    Route::post('verifyanyakarya', [JdController::class, 'verifyanyakarya'])->name('verify.anyakarya');
    Route::post('verifyAllanyakarya', [JdController::class, 'verifyAllanyakarya'])->name('verifyAll.anyakarya');
    Route::post('verifypeyjalnivaran', [JdController::class, 'verifypeyjalnivaran'])->name('verify.peyjalnivaran');
    Route::post('verifyAllpeyjalnivaran', [JdController::class, 'verifyAllpeyjalnivaran'])->name('verifyAll.peyjalnivaran');
    Route::post('verifyjalpradaygirh', [JdController::class, 'verifyjalpradaygirh'])->name('verify.jalpradaygirh');
    Route::post('verifyAlljalpradaygirh', [JdController::class, 'verifyAlljalpradaygirh'])->name('verifyAll.jalpradaygirh');
    Route::post('verifymudranksulak', [JdController::class, 'verifymudranksulak'])->name('verify.mudranksulak');
    Route::post('verifyAllmudranksulak', [JdController::class, 'verifyAllmudranksulak'])->name('verifyAll.mudranksulak');
    Route::post('verifybar', [JdController::class, 'verifybar'])->name('verify.bar');
    Route::post('verifyAllbar', [JdController::class, 'verifyAllbar'])->name('verifyAll.bar');
    Route::post('verifyyatrikar', [JdController::class, 'verifyyatrikar'])->name('verify.yatrikar');
    Route::post('verifyAllyatrikar', [JdController::class, 'verifyAllyatrikar'])->name('verifyAll.yatrikar');
    Route::post('verifyutpadkar', [JdController::class, 'verifyutpadkar'])->name('verify.utpadkar');
    Route::post('verifyAllutpadkar', [JdController::class, 'verifyAllutpadkar'])->name('verifyAll.utpadkar');
    Route::get('ulblist', [JdController::class, 'ulblist'])->name('ulb.list');
    // Route::post('jd-profile-update/{id}', [UlbController::class, 'jdPasswordUpdate'])->name('jd.passwordUpdate');
    Route::get('jdsuspensionreport', [JdController::class, 'jdsuspensionreport'])->name('jdsuspension.report');

    Route::get('jdincomeexpenditurereport', [JdController::class, 'jdincomeexpenditurereport'])->name('jdincomeexpenditure.report');
    Route::post('verifyincomeexpData', [JdController::class, 'verifyincomeexpData'])->name('verifyincomeexp.Data');
    Route::post('verifyallincomeexpData', [JdController::class, 'verifyallincomeexpData'])->name('verifyallincomeexp.Data');

    Route::get('jdotherinformation', [JdController::class, 'jdotherinformation']);
    Route::get('jdmuktidham', [JdController::class, 'jdmuktidham']);
    Route::get('jdtalabinformation', [JdController::class, 'jdtalabinformation']);
    Route::get('jdgardeninformation', [JdController::class, 'jdgardeninformation']);
    Route::get('jdcomplexinformation', [JdController::class, 'jdcomplexinformation']);
    Route::get('jdstreetlightinformation', [JdController::class, 'jdstreetlightinformation']);
    Route::get('jdplaygroundinformation', [JdController::class, 'jdplaygroundinformation']);
    Route::get('jdhandpumpinformation', [JdController::class, 'jdhandpumpinformation']);
    Route::get('jdpanitankiinformation', [JdController::class, 'jdpanitankiinformation']);
    Route::get('jdpayjalinformation', [JdController::class, 'jdpayjalinformation']);
    Route::get('jdjaljanitbimari', [JdController::class, 'jdjaljanitbimari']);
    Route::get('jdtrafficlight', [JdController::class, 'jdtrafficlight']);
    Route::get('jdgovtbuilding', [JdController::class, 'jdgovtbuilding']);
    Route::get('jdrainwaterharvesting', [JdController::class, 'jdrainwaterharvesting']);


    Route::get('/jdregularsalary/{ulb_id}', [JDController::class, 'jdregularsalary'])->name('jd.regularsalary');

    Route::get('/jdplacementsalary/{ulb_id}', [JDController::class, 'jdplacementsalary'])->name('jd.placementsalary');

    Route::get('/jdelectricitydetails/{ulb_id}', [JDController::class, 'jdelectricitydetails'])->name('jdelectricity.details');
});

Route::middleware(['auth:web'])->group(function () {
    Route::get('Ulbdashboard', [UlbController::class, 'Ulbdashboard'])->name('ulb.dashboard');

    Route::get('leaveapplication', [UlbController::class, 'leaveapplication']);
    Route::get('ulbprofile', [UlbController::class, 'ulbprofile']);
    Route::post('ulb-profile-update/{id}', [UlbController::class, 'UlbPasswordUpdate'])->name('ulb.passwordUpdate');
    Route::post('leaveapplicationsubmit', [UlbController::class, 'leaveapplicationsubmit'])->name('leaveapplication.submit');
    Route::get('employementregister', [UlbController::class, 'employementregister'])->name('employementregister');
    Route::post('employementregistersubmit', [UlbController::class, 'employementregistersubmit'])->name('employementregister.submit');
    Route::get('employeereport', [UlbController::class, 'employeereport'])->name('employeereport');
    Route::get('/employee/edit/{id}', [UlbController::class, 'employeeedit'])->name('employee.edit');
    Route::post('/employee/update/{id}', [UlbController::class, 'employeeupdate'])->name('employee.update');
    Route::get('salarydetails', [UlbController::class, 'salarydetails']);
    Route::post('salarydetailsubmit', [UlbController::class, 'salarydetailsubmit'])->name('salarydetail.submit');
    Route::post('/salarydetailsupdate', [UlbController::class, 'salarydetailupdate'])->name('salarydetail.update');

    Route::post('salarydetailsplacementsubmit', [UlbController::class, 'salarydetailsplacementsubmit'])->name('salarydetailsplacement.submit');
    Route::get('electricitybill', [UlbController::class, 'electricitybill']);
    Route::post('electricitybillsubmit', [UlbController::class, 'electricitybillsubmit'])->name('electricitybill.submit');
    Route::post('/electricitybillupdate', [UlbController::class, 'electricitybillupdate'])->name('electricitybill.update');


    Route::get('newmeter', [UlbController::class, 'newmeter'])->name('newmeter');
    Route::post('newmetersubmit', [UlbController::class, 'newmetersubmit'])->name('newmeter.submit');
    Route::get('suspensiondetail', [UlbController::class, 'suspensiondetail'])->name('suspension.detail');
    Route::post('suspensiondetailsubmit', [UlbController::class, 'suspensiondetailsubmit'])->name('suspensiondetail.submit');
    // Route::get('bahaldetail', [UlbController::class, 'bahaldetail'])->name('bahal.detail');
    Route::post('bahaldetailsubmit', [UlbController::class, 'bahaldetailsubmit'])->name('bahaldetails.submit');
    Route::get('SuspensionReport', [UlbController::class, 'SuspensionReport'])->name('Suspension.Report');

    Route::post('electricityyearlysubmit', [UlbController::class, 'electricityyearlysubmit'])->name('electricityyearly.submit');


    Route::get('otherinformation', [UlbController::class, 'otherinformation']);
    Route::get('updatereports', [UlbController::class, 'updatereports']);
    Route::get('basicdetails', [UlbController::class, 'basicdetails']);
    Route::post('basicdetailssubmit', [UlbController::class, 'basicdetailssubmit'])->name('basicdetailssubmit');
    Route::put('basicdetailsupdate', [UlbController::class, 'basicdetailsupdate'])->name('basicdetails.update');

    Route::get('administrativedetail', [UlbController::class, 'administrativedetail']);
    Route::post('administrativedetailsubmit', [UlbController::class, 'administrativedetailsubmit'])->name('administrativedetail.submit');
    Route::put('administrativedetailupdate', [UlbController::class, 'administrativedetailupdate'])->name('administrativedetail.update');

    Route::get('financialdetails', [UlbController::class, 'financialdetails']);
    Route::post('financialdetailssubmit', [UlbController::class, 'financialdetailssubmit'])->name('financialdetails.submit');
    Route::put('financialdetailsupdate', [UlbController::class, 'financialdetailsupdate'])->name('financialdetails.update');

    Route::get('infrastructureservices', [UlbController::class, 'infrastructureservices']);
    Route::post('infrastructureservicessubmit', [UlbController::class, 'infrastructureservicessubmit'])->name('infrastructureservicessubmit');
    Route::put('infrastructureservicesupdate', [UlbController::class, 'infrastructureservicesupdate'])->name('infrastructureservices.update');

    // Infrastructure Works Routes
    Route::get('infrastructureworks', [UlbController::class, 'infrastructureworks'])->name('infrastructureworks');
    Route::post('infrastructureworkssubmit', [UlbController::class, 'infrastructureworkssubmit'])->name('infrastructureworkssubmit');
    Route::put('infrastructureworksupdate', [UlbController::class, 'infrastructureworksupdate'])->name('infrastructureworks.update');

    // Digital Smart Features Routes
    Route::get('digitalsmartfeatures', [UlbController::class, 'digitalsmartfeatures'])->name('digitalsmartfeatures');
    Route::post('digitalsmartfeatures/submit', [UlbController::class, 'digitalsmartfeaturessubmit'])->name('digitalsmartfeatures.submit');
    Route::put('digitalsmartfeatures/update', [UlbController::class, 'digitalsmartfeaturesupdate'])->name('digitalsmartfeatures.update');

    Route::get('developmentprojects', [UlbController::class, 'developmentprojects']);
    Route::post('developmentprojectssubmit', [UlbController::class, 'developmentprojectssubmit'])->name('developmentprojectssubmit');
    Route::put('developmentprojectsupdate', [UlbController::class, 'developmentprojectsupdate'])->name('developmentprojects.update');

    Route::get('digitalgovernance', [UlbController::class, 'digitalgovernance']);
    Route::post('digitalgovernancesubmit', [UlbController::class, 'digitalgovernancesubmit'])->name('digitalgovernancesubmit');
    Route::put('digitalgovernanceupdate', [UlbController::class, 'digitalgovernanceupdate'])->name('digitalgovernance.update');

    // New routes for Citizen Participation
    Route::get('citizenparticipation', [UlbController::class, 'citizenparticipation']);
    Route::post('citizenparticipationsubmit', [UlbController::class, 'citizenparticipationsubmit'])->name('citizenparticipationsubmit');
    Route::put('citizenparticipationupdate', [UlbController::class, 'citizenparticipationupdate'])->name('citizenparticipation.update');

    // New routes for Environmental Initiatives
    Route::get('environmentalinitiatives', [UlbController::class, 'environmentalinitiatives']);
    Route::post('environmentalinitiativessubmit', [UlbController::class, 'environmentalinitiativessubmit'])->name('environmentalinitiativessubmit');
    Route::put('environmentalinitiativesupdate', [UlbController::class, 'environmentalinitiativesupdate'])->name('environmentalinitiatives.update');

    // New routes for Recreational Facilities
    Route::get('recreationalfacilities', [UlbController::class, 'recreationalfacilities']);
    Route::post('recreationalfacilities/submit', [UlbController::class, 'recreationalfacilitiessubmit'])->name('recreationalfacilitiessubmit');
    Route::put('recreationalfacilities/update', [UlbController::class, 'recreationalfacilitiesupdate'])->name('recreationalfacilities.update');

    Route::get('muktidham', [UlbController::class, 'muktidham']);
    Route::get('talabinformation', [UlbController::class, 'talabinformation']);
    Route::get('gardeninformation', [UlbController::class, 'gardeninformation']);
    Route::get('complexinformation', [UlbController::class, 'complexinformation']);
    Route::get('streetlightinformation', [UlbController::class, 'streetlightinformation']);
    Route::get('playgroundinformation', [UlbController::class, 'playgroundinformation']);
    Route::get('handpumpinformation', [UlbController::class, 'handpumpinformation']);
    Route::get('panitankiinformation', [UlbController::class, 'panitankiinformation']);
    Route::get('payjalinformation', [UlbController::class, 'payjalinformation']);
    Route::get('jaljanitbimari', [UlbController::class, 'jaljanitbimari']);

    Route::post('muktidhamsubmit', [UlbController::class, 'muktidhamsubmit'])->name('muktidham.submit');
    Route::post('talabinformationsubmit', [UlbController::class, 'talabinformationsubmit'])->name('talabinformation.submit');
    Route::post('gardeninformationsubmit', [UlbController::class, 'gardeninformationsubmit'])->name('gardeninformation.submit');
    Route::post('complexinformationsubmit', [UlbController::class, 'complexinformationsubmit'])->name('complexinformation.submit');
    Route::post('streetlightinformationsubmit', [UlbController::class, 'streetlightinformationsubmit'])->name('streetlightinformation.submit');
    Route::post('playgroundinformationsubmit', [UlbController::class, 'playgroundinformationsubmit'])->name('playgroundinformation.submit');
    Route::post('handpumpinformationsubmit', [UlbController::class, 'handpumpinformationsubmit'])->name('handpumpinformation.submit');
    Route::post('panitankiinformationsubmit', [UlbController::class, 'panitankiinformationsubmit'])->name('panitankiinformation.submit');
    Route::post('payjalinformationsubmit', [UlbController::class, 'payjalinformationsubmit'])->name('payjalinformation.submit');
    Route::post('jaljanitbimarisubmit', [UlbController::class, 'jaljanitbimarisubmit'])->name('jaljanitbimari.submit');

    Route::post('muktidhamupdate', [UlbController::class, 'muktidhamupdate'])->name('muktidham.update');
    Route::post('talabinfoupdate', [UlbController::class, 'talabinfoupdate'])->name('talabinfo.update');
    Route::post('gardeninfoupdate', [UlbController::class, 'gardeninfoupdate'])->name('gardeninfo.update');
    Route::post('complexinfoupdate', [UlbController::class, 'complexinfoupdate'])->name('complexinfo.update');
    Route::post('streetlightinfoupdate', [UlbController::class, 'streetlightinfoupdate'])->name('streetlightinfo.update');
    Route::post('playgroundinfoupdate', [UlbController::class, 'playgroundinfoupdate'])->name('playgroundinfo.update');
    Route::post('handpumpinfoupdate', [UlbController::class, 'handpumpinfoupdate'])->name('handpumpinfo.update');
    Route::post('panitankiinfoupdate', [UlbController::class, 'panitankiinfoupdate'])->name('panitankiinfo.update');
    Route::post('payjalininfoupdate', [UlbController::class, 'payjalininfoupdate'])->name('payjalininfo.update');

    Route::get('trafficlight', [UlbController::class, 'trafficlight']);
    Route::post('trafficlightsubmit', [UlbController::class, 'trafficlightsubmit'])->name('trafficlight.submit');
    Route::post('trafficlightupdate', [UlbController::class, 'trafficlightupdate'])->name('trafficlight.update');

    Route::get('govtbuildinginfomation', [UlbController::class, 'govtbuildinginfomation']);
    Route::post('govtbuildingsubmit', [UlbController::class, 'govtbuildingsubmit'])->name('govtbuilding.submit');
    Route::post('govtbuildingupdate', [UlbController::class, 'govtbuildingupdate'])->name('govtbuilding.update');

    Route::get('rainwaterharvestinginfo', [UlbController::class, 'rainwaterharvestinginfo']);
    Route::post('rainwaterharvestingsubmit', [UlbController::class, 'rainwaterharvestingsubmit'])->name('rainwaterharvesting.submit');
    Route::post('rainwaterharvestingupdate', [UlbController::class, 'rainwaterharvestingupdate'])->name('rainwaterharvesting.update');


    Route::get('slrmbhawans', [UlbController::class, 'slrmbhawans']);
    Route::post('slrmbhawansubmit', [UlbController::class, 'slrmbhawansubmit'])->name('slrmbhawan.submit');
    Route::post('slrmbhawansupdate', [UlbController::class, 'slrmbhawansupdate'])->name('slrmbhawans.update');
    Route::get('gouthangovdhamkanjihouse', [UlbController::class, 'gouthangovdhamkanjihouse']);
    Route::post('gouthangovdhamkanjihousesubmit', [UlbController::class, 'gouthangovdhamkanjihousesubmit'])->name('gouthangovdhamkanjihouse.submit');
    Route::post('gouthangovdhamkanjihouseupdate', [UlbController::class, 'gouthangovdhamkanjihouseupdate'])->name('gouthangovdhamkanjihouse.update');
    Route::get('stpinfo', [UlbController::class, 'stpinfo']);
    Route::post('stpsubmit', [UlbController::class, 'stpsubmit'])->name('stp.submit');
    Route::post('stpupdate', [UlbController::class, 'stpupdate'])->name('stp.update');
    Route::get('wtpinfo', [UlbController::class, 'wtpinfo']);
    Route::post('wtpsubmit', [UlbController::class, 'wtpsubmit'])->name('wtp.submit');
    Route::post('wtpupdate', [UlbController::class, 'wtpupdate'])->name('wtp.update');
    Route::get('gyminfo', [UlbController::class, 'gyminfo']);
    Route::post('gymsubmit', [UlbController::class, 'gymsubmit'])->name('gym.submit');
    Route::post('gymupdate', [UlbController::class, 'gymupdate'])->name('gym.update');
    Route::get('toiletinfo', [UlbController::class, 'toiletinfo']);
    Route::post('toiletsubmit', [UlbController::class, 'toiletsubmit'])->name('toilet.submit');
    Route::post('toiletupdate', [UlbController::class, 'toiletupdate'])->name('toilet.update');



    // ulb allotments route
    Route::get('ulballotment', [UlbController::class, 'ulballotment']);
    Route::post('chungimadhvyaysubmit', [UlbController::class, 'chungimadhvyaysubmit'])->name('chungimadhvyay.submit');
    Route::post('appatnidhimarammat', [UlbController::class, 'appatnidhimarammat'])->name('appatnidhi.marammat');
    Route::post('mahapauradhyakshnidhis', [UlbController::class, 'mahapauradhyakshnidhis'])->name('mahapauradhyaksh.nidhis');
    Route::post('parshadnidhis', [UlbController::class, 'parshadnidhis'])->name('parshad.nidhis');
    Route::post('swachcommando', [UlbController::class, 'swachcommando'])->name('swach.commando');
    Route::post('missioncleancities', [UlbController::class, 'missioncleancities'])->name('missionclean.cities');
    Route::post('lambitvetans', [UlbController::class, 'lambitvetans'])->name('lambit.vetans');
    Route::post('anyakaryas', [UlbController::class, 'anyakaryas'])->name('anya.karyas');
    Route::post('peyjalkastnivarans', [UlbController::class, 'peyjalkastnivarans'])->name('peyjalkast.nivarans');
    Route::post('jalpradaygirhs', [UlbController::class, 'jalpradaygirhs'])->name('jalpraday.girhs');
    Route::post('mudranksulaks', [UlbController::class, 'mudranksulaks'])->name('mudrank.sulaks');
    Route::post('bars', [UlbController::class, 'bars'])->name('bars');
    Route::post('yatrikars', [UlbController::class, 'yatrikars'])->name('yatri.kars');
    Route::post('utpadkars', [UlbController::class, 'utpadkars'])->name('utpad.kars');

    // rajasvasuli route

    Route::post('rajasvasullidemandsubmit', [UlbController::class, 'rajasvasullidemandsubmit'])->name('rajasvasullidemand.submit');
    Route::get('rajasvvasuliform', [UlbController::class, 'rajasvvasuliform'])->name('rajasvvasuliform');
    Route::post('revenuecollection', [UlbController::class, 'revenuecollection'])->name('revenuecollection.submit');
    Route::get('revenuecollectionreport', [UlbController::class, 'revenuecollectionreport'])->name('revenuecollection.report');
    Route::get('ulbdetailreport', [UlbController::class, 'ulbdetailreport'])->name('ulbdetail.report');
    Route::get('revenuecollectionedit/{id}', [UlbController::class, 'revenuecollectionedit'])->name('revenuecollection.edit');
    Route::post('revenuecollectionupdate/{id}', [UlbController::class, 'revenuecollectionupdate'])->name('revenuecollection.update');
    Route::put('/revenue/update/{id}', [UlbController::class, 'revenueupdate'])->name('revenue.update');

    Route::get('revenuedemanddetail', [UlbController::class, 'revenuedemanddetail'])->name('revenuedemand.detail');
    Route::get('/revenuedemandview/{year}', [UlbController::class, 'revenuedemandview'])->name('revenuedemand.view');
    Route::post('/revenuedemandupdate/{id}', [UlbController::class, 'revenuedemandupdate'])->name('revenuedemand.update');


    Route::post('jalkarrevenue', [UlbController::class, 'jalkarrevenue'])->name('jalkar.revenue');
    Route::post('niryatkarrevenue', [UlbController::class, 'niryatkarrevenue'])->name('niryatkar.revenue');
    Route::post('dukankirayarevenue', [UlbController::class, 'dukankirayarevenue'])->name('dukankiraya.revenue');
    Route::post('bhavananugyarevenue', [UlbController::class, 'bhavananugyarevenue'])->name('bhavananugya.revenue');
    Route::post('dukansalerevenue', [UlbController::class, 'dukansalerevenue'])->name('dukansale.revenue');
    Route::post('colonyanugyarevenue', [UlbController::class, 'colonyanugyarevenue'])->name('colonyanugya.revenue');
    Route::post('vigyapanhordingrevenue', [UlbController::class, 'vigyapanhordingrevenue'])->name('vigyapanhording.revenue');
    Route::post('parkingpashurevenue', [UlbController::class, 'parkingpashurevenue'])->name('parkingpashu.revenue');
    Route::post('userchargerevenue', [UlbController::class, 'userchargerevenue'])->name('usercharge.revenue');
    Route::post('othertaxrevenue', [UlbController::class, 'othertaxrevenue'])->name('othertax.revenue');
    Route::post('othersourcerevenue', [UlbController::class, 'othersourcerevenue'])->name('othersource.revenue');


    Route::get('/income-expenditure', [UlbController::class, 'incomeexpenditure'])->name('income.expenditure');
    Route::post('submitincomeexpenditure', [UlbController::class, 'submitincomeexpenditure'])->name('submitincome.expenditure');
    Route::post('updateincomeexpenditure', [UlbController::class, 'updateincomeexpenditure'])->name('update.incomeexpenditure');

    Route::get('export-ExcelTaxTypewise', [UlbController::class, 'exportExcelTaxTypewise'])->name('export.ExcelTaxTypewise');
    Route::get('export-ExcelOverall', [UlbController::class, 'exportExcelExcelOverall'])->name('export.ExcelOverall');

    Route::get('export-ExcelEmployee', [UlbController::class, 'exportExcelEmployee'])->name('export.ExcelEmployee');
    Route::get('export-excelNewMeter', [UlbController::class, 'exportexcelNewMeter'])->name('export.excelNewMeter');
    Route::get('export-excelElectricityBill', [UlbController::class, 'excelElectricityBill'])->name('export.excelElectricityBill');
    Route::get('export-excelLeaveDetail', [UlbController::class, 'exportexcelLeaveDetail'])->name('export.excelLeaveDetail');
    Route::get('export-excelRegularSalary', [UlbController::class, 'exportexcelRegularSalary'])->name('export.excelRegularSalary');
    Route::get('export-excelPlacementSalary', [UlbController::class, 'exportexcelPlacementSalary'])->name('export.excelPlacementSalary');

    Route::get('ulbbpnumber', [UlbController::class, 'ulbbpnumber']);
    Route::post('verify-bp-number', [UlbController::class, 'verifyBpNumber'])->name('verify.bp.number');
    Route::post('search-bp-number', [UlbController::class, 'searchBpNumber'])->name('search.bp.number');
});
