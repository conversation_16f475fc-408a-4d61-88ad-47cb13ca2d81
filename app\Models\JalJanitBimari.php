<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Jaljanitbimari extends Model
{
    use HasFactory;

    protected $table = 'jaljanitbimaris';

    public function ulb()
    {

        return $this->belongsTo(User::class,'ulb_id');
    }
    public function division()
    {
        return $this->belongsTo(MasterDivision::class, 'division_id');
    }
    protected $fillable = [
        'ulb_id',
        'dateInput',
        'kultankiya',
        'cleantanki',
        'notcleantanki',
        'totalsamvel',
        'cleansamvel',
        'notcleansamvel',
        'totalpump',
        'pumpnation',
        'notpumpnation',
        'totalcheck',
        'payjalupukt',
        'notpayjalupukt',
        'totalpipelinelikage',
        'pipelinelikage',
        'unique_id',
        // add any other fields you're updating via mass assignment
    ];

}
