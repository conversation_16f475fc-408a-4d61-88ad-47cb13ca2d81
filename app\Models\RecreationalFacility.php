<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RecreationalFacility extends Model
{
    use HasFactory;

    protected $fillable = [
        'ulb_id',
        'facility_type',
        'facility_name',
        'location',
        'year_established',
        'condition',
        'ownership',
        'entry_fee',
        'monthly_footfall',
        'remarks',
        'attempts'
    ];

    public function ulb()
    {
        return $this->belongsTo(User::class, 'ulb_id');
    }
} 