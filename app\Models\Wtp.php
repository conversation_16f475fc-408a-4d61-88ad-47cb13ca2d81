<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Wtp extends Model
{
    use HasFactory;
    public function ulb()
    {
        return $this->belongsTo(User::class, 'ulb_id');
    }
    public function division()
    {
        return $this->belongsTo(MasterDivision::class, 'division_id');
    }
    protected $fillable = [
        'unique_id',
        'dateInput',
        'wtps_location',
        'wtps_ward_no',
        'wtps_ward_name',
        'wtps_latitude',
        'wtps_longitude',
        'wtps_remark',
    ];
}
