<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UlbDigitalGovernance extends Model
{
    use HasFactory;

    protected $table = 'ulb_digital_governance';

    protected $fillable = [
        'ulb_id',
        'online_services_provided',
        'uses_gis_tools',
        'gis_mis_tools_details',
        'e_governance_initiatives',
        'attempts',
    ];

    protected $casts = [
        'uses_gis_tools' => 'boolean',
    ];

    /**
     * Get the user that owns the record.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'ulb_id');
    }
}
