{"version": 3, "sources": ["jquery-asGradient.es.js"], "names": ["global", "factory", "define", "amd", "exports", "require", "mod", "j<PERSON><PERSON><PERSON>", "AsColor", "AsGrad<PERSON>", "this", "_j<PERSON>y", "_jqueryAsColor", "_interopRequireDefault", "obj", "__esModule", "default", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "getPrefix", "ua", "window", "navigator", "userAgent", "prefix", "test", "reverseDirection", "direction", "mapping", "top", "right", "bottom", "left", "right top", "top right", "bottom right", "right bottom", "left bottom", "bottom left", "top left", "left top", "hasOwnProperty", "isDirection", "n", "Object", "defineProperty", "value", "_jquery2", "_jqueryAsColor2", "_typeof", "Symbol", "iterator", "constructor", "prototype", "_createClass", "defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "key", "protoProps", "staticProps", "DEFAULTS", "prefixes", "forceStandard", "angleUseKeyword", "emptyString", "degradationFormat", "cleanPosition", "color", "format", "hexUseName", "reduceAlpha", "shortenHex", "zeroAlphaAsTransparent", "invalidV<PERSON>ue", "r", "g", "b", "a", "String", "includes", "search", "start", "indexOf", "keywordAngleMap", "to top", "to right", "to bottom", "to left", "to right top", "to top right", "to bottom right", "to right bottom", "to left bottom", "to bottom left", "to top left", "to left top", "angleKeywordMap", "o", "flipped", "flip", "RegExpStrings", "position", "angle", "stop", "RegExp", "source", "stops", "parameters", "FULL", "ANGLE", "COLOR", "POSITION", "STOP", "STOPS", "PARAMETERS", "GradientString", "matchString", "string", "matched", "parseString", "trim", "exec", "parseParameters", "type", "parseStops", "_this", "result", "match", "each", "item", "parseStop", "push", "formatStops", "output", "positions", "colors", "undefined", "toString", "data", "average", "_i", "isNaN", "j", "x", "formatPosition", "join", "parsePosition", "substr", "parseFloat", "slice", "parseInt", "parseAngle", "notStandard", "replace", "fixOldAngle", "directions", "split", "filtered", "toLowerCase", "keyword", "Math", "abs", "toFixed", "formatAngle", "useKeyword", "ColorStop", "gradient", "options", "id", "_stopIdCount", "reorder", "fromString", "removeById", "GradientTypes", "LINEAR", "parse", "to", "standard", "_prefix", "inArray", "extend", "_type", "current", "init", "insert", "sort", "index", "splice", "getIndexById", "remove", "_angle", "empty", "toUpperCase", "_this2", "reset"], "mappings": ";;;;;;;CAAA,SAAAA,EAAAC,GACA,GAAA,mBAAAC,QAAAA,OAAAC,IACAD,OAAA,cAAA,UAAA,SAAA,kBAAAD,QACA,GAAA,oBAAAG,QACAH,EAAAG,QAAAC,QAAA,UAAAA,QAAA,uBACA,CACA,IAAAC,GACAF,YAEAH,EAAAK,EAAAF,QAAAJ,EAAAO,OAAAP,EAAAQ,SACAR,EAAAS,WAAAH,EAAAF,SAVA,CAYAM,KAAA,SAAAN,EAAAO,EAAAC,GACA,aAUA,SAAAC,EAAAC,GACA,OAAAA,GAAAA,EAAAC,WACAD,GAEAE,QAAAF,GAkBA,SAAAG,EAAAC,EAAAC,GACA,KAAAD,aAAAC,GACA,MAAA,IAAAC,UAAA,qCA4DA,SAAAC,IACA,IAAAC,EAAAC,OAAAC,UAAAC,UACAC,EAAA,GAUA,MATA,QAAAC,KAAAL,GACAI,EAAA,OACA,WAAAC,KAAAL,GACAI,EAAA,QACA,YAAAC,KAAAL,GACAI,EAAA,WACA,SAAAC,KAAAL,KACAI,EAAA,OAEAA,EAaA,SAAAE,EAAAC,GACA,IAAAC,GACAC,IAAA,SACAC,MAAA,OACAC,OAAA,MACAC,KAAA,QACAC,YAAA,cACAC,YAAA,cACAC,eAAA,WACAC,eAAA,WACAC,cAAA,YACAC,cAAA,YACAC,WAAA,eACAC,WAAA,gBAEA,OAAAZ,EAAAa,eAAAd,GAAAC,EAAAD,GAAAA,EAGA,SAAAe,EAAAC,GAEA,MADA,6BACAlB,KAAAkB,GAzIAC,OAAAC,eAAA3C,EAAA,cACA4C,OAAA,IAGA,IAAAC,EAAApC,EAAAF,GAEAuC,EAAArC,EAAAD,GAUAuC,EACA,mBAAAC,QAAA,iBAAAA,OAAAC,SACA,SAAAvC,GACA,cAAAA,GAEA,SAAAA,GACA,OAAAA,GACA,mBAAAsC,QACAtC,EAAAwC,cAAAF,QACAtC,IAAAsC,OAAAG,UACA,gBACAzC,GASA0C,EAAA,WACA,SAAAC,EAAAC,EAAAC,GACA,IAAA,IAAAC,EAAA,EAAAA,EAAAD,EAAAE,OAAAD,IAAA,CACA,IAAAE,EAAAH,EAAAC,GACAE,EAAAC,WAAAD,EAAAC,aAAA,EACAD,EAAAE,cAAA,EACA,UAAAF,IAAAA,EAAAG,UAAA,GACAnB,OAAAC,eAAAW,EAAAI,EAAAI,IAAAJ,IAIA,OAAA,SAAA3C,EAAAgD,EAAAC,GAGA,OAFAD,GAAAV,EAAAtC,EAAAoC,UAAAY,GACAC,GAAAX,EAAAtC,EAAAiD,GACAjD,GAdA,GAkBAkD,GACAC,UAAA,WAAA,QAAA,OAAA,OACAC,eAAA,EACAC,iBAAA,EACAC,YAAA,GACAC,mBAAA,EACAC,eAAA,EACAC,OACAC,QAAA,EACAC,YAAA,EACAC,aAAA,EACAC,YAAA,EACAC,wBAAA,EACAC,cACAC,EAAA,EACAC,EAAA,EACAC,EAAA,EACAC,EAAA,KAMAC,OAAAhC,UAAAiC,WACAD,OAAAhC,UAAAiC,SAAA,SAAAC,EAAAC,GAOA,MAJA,iBAAAA,IACAA,EAAA,KAGAA,EAAAD,EAAA5B,OAAAnD,KAAAmD,UAGA,IAAAnD,KAAAiF,QAAAF,EAAAC,KAoDA,IAAAE,GACAC,SAAA,EACAC,WAAA,GACAC,YAAA,IACAC,UAAA,IACAC,eAAA,GACAC,eAAA,GACAC,kBAAA,IACAC,kBAAA,IACAC,iBAAA,IACAC,iBAAA,IACAC,cAAA,IACAC,cAAA,KAGAC,EAhDA,SAAAC,GACA,IAAAC,KACA,IAAA,IAAA/C,KAAA8C,EACAA,EAAA/D,eAAAiB,KACA+C,EAAAD,EAAA9C,IAAAA,GAGA,OAAA+C,EAyCAC,CAAAhB,GAEAiB,EAAA,WACA,IAAAjC,EAAA,iEACAkC,EAAA,YACAC,EAAA,4DACAC,EAAA,IAAAC,OACA,IAAArC,EAAAsC,OAAA,SAAAJ,EAAAI,OAAA,SACA,KAEAC,EAAA,IAAAF,OAAAD,EAAAE,OAAA,MACAE,EAAA,IAAAH,OACA,OAAAF,EAAAG,OAAA,iCACA,KASA,OACAG,KARA,IAAAJ,OACA,uFACAG,EAAAF,OACA,YACA,KAKAI,MAAAP,EACAQ,MAAA3C,EACA4C,SAAAV,EACAW,KAAAT,EACAU,MAAAP,EACAQ,WAAA,IAAAV,OAAA,IAAAG,EAAAF,OAAA,IAAA,MA3BA,GA+BAU,GACAC,YAAA,SAAAC,GACA,IAAAC,EAAArH,KAAAsH,YAAAF,GACA,SACAC,GACAA,EAAA/E,OACA+E,EAAA/E,MAAAmE,OACAY,EAAA/E,MAAAmE,MAAAtD,OAAA,IAOAmE,YAAA,SAAAF,GACAA,EAAA7E,EAAAjC,QAAAiH,KAAAH,GACA,IAAAC,OAAA,EACA,GAAA,QAAAA,EAAAlB,EAAAQ,KAAAa,KAAAJ,IAAA,CACA,IAAA9E,EAAAtC,KAAAyH,gBAAAJ,EAAA,IAEA,OACArG,YAAA,IAAAqG,EAAA,GAAA,KAAAA,EAAA,GACAK,KAAAL,EAAA,GACA/E,MAAAA,GAGA,OAAA,GAIAmF,gBAAA,SAAAL,GACA,IAAAC,OAAA,EACA,GAAA,QAAAA,EAAAlB,EAAAc,WAAAO,KAAAJ,IAAA,CACA,IAAAX,EAAAzG,KAAA2H,WAAAN,EAAA,IACA,OACAhB,WAAA,IAAAgB,EAAA,GAAA,EAAAA,EAAA,GACAZ,MAAAA,GAGA,OAAA,GAIAkB,WAAA,SAAAP,GACA,IAAAQ,EAAA5H,KAEAqH,OAAA,EACAQ,KACA,OAAA,QAAAR,EAAAD,EAAAU,MAAA3B,EAAAa,UACAzE,EAAAjC,QAAAyH,KAAAV,EAAA,SAAAnE,EAAA8E,GACA,IAAA1B,EAAAsB,EAAAK,UAAAD,GACA1B,GACAuB,EAAAK,KAAA5B,KAGAuB,IAMAM,YAAA,SAAA1B,EAAAxC,GAOA,IAAA,IANAqC,OAAA,EACA8B,KACAC,KACAC,KACAlC,OAAA,EAEAlD,EAAA,EAAAA,EAAAuD,EAAAtD,OAAAD,IAIAkD,OAFA,KADAE,EAAAG,EAAAvD,IACAkD,UAAA,OAAAE,EAAAF,SACA,IAAAlD,EACA,EACAA,IAAAuD,EAAAtD,OAAA,EACA,OAEAoF,EAGAjC,EAAAF,SAEAiC,EAAAH,KAAA9B,GACAkC,EAAAJ,KAAA5B,EAAApC,MAAAsE,YAGAH,EAAA,SAAAI,GAGA,IAAA,IAFAzD,EAAA,KACA0D,OAAA,EACAC,EAAA,EAAAA,EAAAF,EAAAtF,OAAAwF,IACA,GAAAC,MAAAH,EAAAE,KACA,GAAA,OAAA3D,EAAA,CACAA,EAAA2D,EACA,eAEA,GAAA3D,EAAA,CACA0D,GAAAD,EAAAE,GAAAF,EAAAzD,EAAA,KAAA2D,EAAA3D,EAAA,GACA,IAAA,IAAA6D,EAAA7D,EAAA6D,EAAAF,EAAAE,IACAJ,EAAAI,GAAAJ,EAAAzD,EAAA,IAAA6D,EAAA7D,EAAA,GAAA0D,EAEA1D,EAAA,KAIA,OAAAyD,EAlBA,CAmBAJ,GAEA,IAAA,IAAAS,EAAA,EAAAA,EAAArC,EAAAtD,OAAA2F,IAMA1C,EAJAnC,IACA,IAAA6E,GAAA,IAAAT,EAAAS,IACAA,IAAArC,EAAAtD,OAAA,GAAA,IAAAkF,EAAAS,IAEA,GAEA,IAAA9I,KAAA+I,eAAAV,EAAAS,IAGAV,EAAAF,KAAAI,EAAAQ,GAAA1C,GAEA,OAAAgC,EAAAY,KAAA,OAGAf,UAAA,SAAAb,GACA,IAAAC,OAAA,EACA,GAAA,QAAAA,EAAAlB,EAAAY,KAAAS,KAAAJ,IAAA,CACA,IAAAhB,EAAApG,KAAAiJ,cAAA5B,EAAA,IAEA,OACAnD,MAAAmD,EAAA,GACAjB,SAAAA,GAGA,OAAA,GAIA6C,cAAA,SAAA7B,GAKA,MAJA,iBAAAA,GAAA,MAAAA,EAAA8B,QAAA,KACA9B,EAAA+B,WAAA/B,EAAAgC,MAAA,GAAA,GAAA,WAGA,IAAAhC,GAAA,OAAAA,EACA+B,WAAA/B,EAAA,IAEA,MAIA2B,eAAA,SAAAzG,GACA,OAAA+G,SAAA,IAAA/G,EAAA,IAAA,KAGAgH,WAAA,SAAAlC,EAAAmC,GASA,GARA,iBAAAnC,GAAAA,EAAAtC,SAAA,SACAsC,EAAAA,EAAAoC,QAAA,MAAA,KAEAZ,MAAAxB,IACAmC,IACAnC,EAAApH,KAAAyJ,YAAArC,IAGA,iBAAAA,EAAA,CACA,IAAAsC,EAAAtC,EAAAuC,MAAA,KAEAC,KACA,IAAA,IAAA1G,KAAAwG,EACAxH,EAAAwH,EAAAxG,KACA0G,EAAA1B,KAAAwB,EAAAxG,GAAA2G,eAGA,IAAAC,EAAAF,EAAAZ,KAAA,KAEA5B,EAAAtC,SAAA,SACAgF,EAAA5I,EAAA4I,IAEAA,EAAA,MAAAA,EACA5E,EAAAjD,eAAA6H,KACA1C,EAAAlC,EAAA4E,IAGA,IAAAxH,EAAA6G,WAAA/B,EAAA,IAWA,OATA9E,EAAA,IACAA,GAAA,IACAA,EAAA,GAGA,KAFAA,IAAA,OAGAA,GAAA,KAGAA,GAGAmH,YAAA,SAAAnH,GAIA,OAHAA,EAAA6G,WAAA7G,GACAA,EAAAyH,KAAAC,IAAA,IAAA1H,GAAA,IACAA,EAAA6G,WAAA7G,EAAA2H,QAAA,KAIAC,YAAA,SAAA5H,EAAAiH,EAAAY,GAcA,OAbA7H,EAAA+G,SAAA/G,EAAA,IACA6H,GAAApE,EAAA9D,eAAAK,IACAA,EAAAyD,EAAAzD,GACAiH,IACAjH,EAAApB,EAAAoB,EAAA4G,OAAA,OAGAK,IACAjH,EAAAtC,KAAAyJ,YAAAnH,IAEAA,GAAA,OAGAA,IAIA8H,EAAA,WACA,SAAAA,EAAAlG,EAAAkC,EAAAiE,GACA9J,EAAAP,KAAAoK,GAEApK,KAAAkE,OAAA,EAAA1B,EAAAlC,SAAA4D,EAAAmG,EAAAC,QAAApG,OACAlE,KAAAoG,SAAAc,EAAA+B,cAAA7C,GACApG,KAAAuK,KAAAF,EAAAG,aACAxK,KAAAqK,SAAAA,EA4BA,OAzBAvH,EAAAsH,IAEA5G,IAAA,cACAlB,MAAA,SAAA8E,GACA,IAAAhB,EAAAc,EAAA+B,cAAA7B,GACApH,KAAAoG,WAAAA,IACApG,KAAAoG,SAAAA,EACApG,KAAAqK,SAAAI,cAKAjH,IAAA,WACAlB,MAAA,SAAA8E,GACApH,KAAAkE,MAAAwG,WAAAtD,MAIA5D,IAAA,SACAlB,MAAA,WACAtC,KAAAqK,SAAAM,WAAA3K,KAAAuK,QAKAH,EAnCA,GAsCAQ,GACAC,QACAC,MAAA,SAAAjD,GACA,OACApD,EACA,MAAAoD,EAAA,GAAAqB,QAAA,GACAG,SAAA,KAAAxB,EAAA,GAAAuB,MAAA,GAAA,GAAA,IACAC,SAAAxB,EAAA,GAAA,IACAnD,EACA,MAAAmD,EAAA,GAAAqB,QAAA,GACAG,SAAA,KAAAxB,EAAA,GAAAuB,MAAA,GAAA,GAAA,IACAC,SAAAxB,EAAA,GAAA,IACAlD,EACA,MAAAkD,EAAA,GAAAqB,QAAA,GACAG,SAAA,KAAAxB,EAAA,GAAAuB,MAAA,GAAA,GAAA,IACAC,SAAAxB,EAAA,GAAA,IACAjD,EAAA,IAGAmG,GAAA,SAAAV,EAAA7J,EAAAQ,GACA,GAAA,IAAAqJ,EAAA5D,MAAAtD,OACA,OAAA3C,EAAA8J,QAAAvG,YAEA,GAAA,IAAAsG,EAAA5D,MAAAtD,OACA,OAAAkH,EAAA5D,MAAA,GAAAvC,MAAA6G,GAAAvK,EAAA8J,QAAAtG,mBAGA,IAAAgH,EAAAxK,EAAA8J,QAAAzG,cACAoH,EAAAzK,EAAAyK,QAEAA,IACAD,GAAA,GAGAhK,IACA,IAAAuB,EAAAjC,QAAA4K,QAAAlK,EAAAR,EAAA8J,QAAA1G,YAEAoH,GAAA,EACAC,EAAAjK,GAGA,IAUAoH,EAAA,mBAVAlB,EAAAgD,YACAG,EAAAhE,OACA2E,EACAxK,EAAA8J,QAAAxG,iBAOA,KALAoD,EAAAiB,YACAkC,EAAA5D,MACAjG,EAAA8J,QAAArG,eAGA,IACA,OAAA+G,EACA5C,EAEA6C,EAAA7C,KAMArI,EAAA,WACA,SAAAA,EAAAqH,EAAAkD,GACA/J,EAAAP,KAAAD,GAIA,iBADA,IAAAqH,EAAA,YAAA3E,EAAA2E,UAEA,IAAAkD,IAEAA,EAAAlD,EACAA,OAAAmB,GAEAvI,KAAAsC,OACA+D,MAAA,EACAI,UAEAzG,KAAAsK,QAAA/H,EAAAjC,QAAA6K,QAAA,KAAAxH,EAAA2G,GAEAtK,KAAAoL,MAAA,SACApL,KAAAiL,QAAA,KACAjL,KAAAmD,OAAAnD,KAAAsC,MAAAmE,MAAAtD,OACAnD,KAAAqL,QAAA,EACArL,KAAAwK,aAAA,EAEAxK,KAAAsL,KAAAlE,UAGAtE,EACA/C,IAGAyD,IAAA,OACAlB,MAAA,SAAA8E,GACAA,GACApH,KAAA0K,WAAAtD,MAKA5D,IAAA,MACAlB,MAAA,SAAAA,GACA,YAAA,IAAAA,EACAtC,KAAAwI,YAEAxI,KAAA0K,WAAApI,GACAtC,SAKAwD,IAAA,QACAlB,MAAA,SAAAA,GACA,YAAA,IAAAA,EACAtC,KAAAsC,MAAA+D,OAEArG,KAAAsC,MAAA+D,MAAAa,EAAAoC,WAAAhH,GACAtC,SAKAwD,IAAA,SACAlB,MAAA,SAAA4B,EAAAkC,GACA,OAAApG,KAAAuL,OAAArH,EAAAkC,EAAApG,KAAAmD,WAIAK,IAAA,UACAlB,MAAA,WACAtC,KAAAmD,OAAA,IAIAnD,KAAAsC,MAAAmE,MAAAzG,KAAAsC,MAAAmE,MAAA+E,KAAA,SAAA5G,EAAAD,GACA,OAAAC,EAAAwB,SAAAzB,EAAAyB,eAKA5C,IAAA,SACAlB,MAAA,SAAA4B,EAAAkC,EAAAqF,QACA,IAAAA,IACAA,EAAAzL,KAAAqL,SAGA,IAAA/E,EAAA,IAAA8D,EAAAlG,EAAAkC,EAAApG,MAMA,OAJAA,KAAAsC,MAAAmE,MAAAiF,OAAAD,EAAA,EAAAnF,GAEAtG,KAAAmD,OAAAnD,KAAAmD,OAAA,EACAnD,KAAAqL,QAAAI,EACAnF,KAIA9C,IAAA,UACAlB,MAAA,SAAAiI,GACA,GAAAvK,KAAAmD,OAAA,EACA,IAAA,IAAAD,KAAAlD,KAAAsC,MAAAmE,MACA,GAAA8D,IAAAvK,KAAAsC,MAAAmE,MAAAvD,GAAAqH,GACA,OAAAvK,KAAAsC,MAAAmE,MAAAvD,GAIA,OAAA,KAIAM,IAAA,aACAlB,MAAA,SAAAiI,GACA,IAAAkB,EAAAzL,KAAA2L,aAAApB,GACAkB,GACAzL,KAAA4L,OAAAH,MAKAjI,IAAA,eACAlB,MAAA,SAAAiI,GACA,IAAAkB,EAAA,EACA,IAAA,IAAAvI,KAAAlD,KAAAsC,MAAAmE,MAAA,CACA,GAAA8D,IAAAvK,KAAAsC,MAAAmE,MAAAvD,GAAAqH,GACA,OAAAkB,EAEAA,IAEA,OAAA,KAIAjI,IAAA,aACAlB,MAAA,WACA,OAAAtC,KAAAsC,MAAAmE,MAAAzG,KAAAqL,YAIA7H,IAAA,iBACAlB,MAAA,SAAAiI,GACA,IAAAkB,EAAA,EACA,IAAA,IAAAvI,KAAAlD,KAAAsC,MAAAmE,MACAzG,KAAAsC,MAAAmE,MAAAvD,GAAAqH,KAAAA,EACAkB,IAEAzL,KAAAqL,QAAAI,KAMAjI,IAAA,MACAlB,MAAA,SAAAmJ,GAIA,YAHA,IAAAA,IACAA,EAAAzL,KAAAqL,SAEAI,GAAA,GAAAA,EAAAzL,KAAAmD,SACAnD,KAAAqL,QAAAI,EACAzL,KAAAsC,MAAAmE,MAAAgF,OAOAjI,IAAA,SACAlB,MAAA,SAAAmJ,QACA,IAAAA,IACAA,EAAAzL,KAAAqL,SAEAI,GAAA,GAAAA,EAAAzL,KAAAmD,SACAnD,KAAAsC,MAAAmE,MAAAiF,OAAAD,EAAA,GACAzL,KAAAmD,OAAAnD,KAAAmD,OAAA,EACAnD,KAAAqL,QAAAI,EAAA,MAKAjI,IAAA,QACAlB,MAAA,WACAtC,KAAAsC,MAAAmE,SACAzG,KAAAmD,OAAA,EACAnD,KAAAqL,QAAA,KAIA7H,IAAA,QACAlB,MAAA,WACAtC,KAAAsC,MAAAuJ,OAAA,EACA7L,KAAA8L,QACA9L,KAAAiL,QAAA,KACAjL,KAAAoL,MAAA,YAIA5H,IAAA,OACAlB,MAAA,SAAA8I,GACA,MACA,iBAAAA,IACAA,EAAAA,EAAAW,qBACA,IAAAnB,EAAAQ,IAEApL,KAAAoL,MAAAA,EACApL,MAEAA,KAAAoL,SAKA5H,IAAA,aACAlB,MAAA,SAAA8E,GACA,IAAA4E,EAAAhM,KAEAA,KAAAiM,QAEA,IAAApE,EAAAX,EAAAI,YAAAF,GAEAS,IACA7H,KAAAiL,QAAApD,EAAA7G,OACAhB,KAAA0H,KAAAG,EAAAH,MACAG,EAAAvF,QACAtC,KAAAsC,MAAA+D,MAAAa,EAAAoC,WACAzB,EAAAvF,MAAA+D,MACA,OAAArG,KAAAiL,isBA9NA", "file": "jquery-asGradient.min.js", "sourcesContent": ["/**\n* jQuery asGradient v0.3.3\n* https://github.com/amazingSurge/jquery-asGradient\n*\n* Copyright (c) amazingSurge\n* Released under the LGPL-3.0 license\n*/\nimport $ from 'jquery';\nimport Color from 'jquery-asColor';\n\nvar DEFAULTS = {\n  prefixes: ['-webkit-', '-moz-', '-ms-', '-o-'],\n  forceStandard: true,\n  angleUseKeyword: true,\n  emptyString: '',\n  degradationFormat: false,\n  cleanPosition: true,\n  color: {\n    format: false, // rgb, rgba, hsl, hsla, hex\n    hexUseName: false,\n    reduceAlpha: true,\n    shortenHex: true,\n    zeroAlphaAsTransparent: false,\n    invalidValue: {\n      r: 0,\n      g: 0,\n      b: 0,\n      a: 1\n    }\n  }\n};\n\n/* eslint no-extend-native: \"off\" */\nif (!String.prototype.includes) {\n  String.prototype.includes = function(search, start) {\n    'use strict';\n    if (typeof start !== 'number') {\n      start = 0;\n    }\n\n    if (start + search.length > this.length) {\n      return false;\n    }\n    return this.indexOf(search, start) !== -1;\n  };\n}\n\nfunction getPrefix() {\n  const ua = window.navigator.userAgent;\n  let prefix = '';\n  if (/MSIE/g.test(ua)) {\n    prefix = '-ms-';\n  } else if (/Firefox/g.test(ua)) {\n    prefix = '-moz-';\n  } else if (/(WebKit)/i.test(ua)) {\n    prefix = '-webkit-';\n  } else if (/Opera/g.test(ua)) {\n    prefix = '-o-';\n  }\n  return prefix;\n}\n\nfunction flip(o) {\n  const flipped = {};\n  for (const i in o) {\n    if (o.hasOwnProperty(i)) {\n      flipped[o[i]] = i;\n    }\n  }\n  return flipped;\n}\n\nfunction reverseDirection(direction) {\n  const mapping = {\n    'top': 'bottom',\n    'right': 'left',\n    'bottom': 'top',\n    'left': 'right',\n    'right top': 'left bottom',\n    'top right': 'bottom left',\n    'bottom right': 'top left',\n    'right bottom': 'left top',\n    'left bottom': 'right top',\n    'bottom left': 'top right',\n    'top left': 'bottom right',\n    'left top': 'right bottom'\n  };\n  return mapping.hasOwnProperty(direction) ? mapping[direction] : direction;\n}\n\nfunction isDirection(n) {\n  const reg = /^(top|left|right|bottom)$/i;\n  return reg.test(n);\n}\n\nvar keywordAngleMap = {\n  'to top': 0,\n  'to right': 90,\n  'to bottom': 180,\n  'to left': 270,\n  'to right top': 45,\n  'to top right': 45,\n  'to bottom right': 135,\n  'to right bottom': 135,\n  'to left bottom': 225,\n  'to bottom left': 225,\n  'to top left': 315,\n  'to left top': 315\n};\n\nconst angleKeywordMap = flip(keywordAngleMap);\n\nconst RegExpStrings = (() => {\n  const color = /(?:rgba|rgb|hsla|hsl)\\s*\\([\\s\\d\\.,%]+\\)|#[a-z0-9]{3,6}|[a-z]+/i;\n  const position = /\\d{1,3}%/i;\n  const angle = /(?:to ){0,1}(?:(?:top|left|right|bottom)\\s*){1,2}|\\d+deg/i;\n  const stop = new RegExp(`(${color.source})\\\\s*(${position.source}){0,1}`, 'i');\n  const stops = new RegExp(stop.source, 'gi');\n  const parameters = new RegExp(`(?:(${angle.source})){0,1}\\\\s*,{0,1}\\\\s*(.*?)\\\\s*`, 'i');\n  const full = new RegExp(`^(-webkit-|-moz-|-ms-|-o-){0,1}(linear|radial|repeating-linear)-gradient\\\\s*\\\\(\\\\s*(${parameters.source})\\\\s*\\\\)$`, 'i');\n\n  return {\n    FULL: full,\n    ANGLE: angle,\n    COLOR: color,\n    POSITION: position,\n    STOP: stop,\n    STOPS: stops,\n    PARAMETERS: new RegExp(`^${parameters.source}$`, 'i')\n  };\n})();\n\nvar GradientString = {\n  matchString: function(string) {\n    const matched = this.parseString(string);\n    if(matched && matched.value && matched.value.stops && matched.value.stops.length > 1){\n      return true;\n    }\n    return false;\n  },\n\n  parseString: function(string) {\n    string = $.trim(string);\n    let matched;\n    if ((matched = RegExpStrings.FULL.exec(string)) !== null) {\n      let value = this.parseParameters(matched[3]);\n\n      return {\n        prefix: (typeof matched[1] === 'undefined') ? null : matched[1],\n        type: matched[2],\n        value: value\n      };\n    } else {\n      return false;\n    }\n  },\n\n  parseParameters: function(string) {\n    let matched;\n    if ((matched = RegExpStrings.PARAMETERS.exec(string)) !== null) {\n      let stops = this.parseStops(matched[2]);\n      return {\n        angle: (typeof matched[1] === 'undefined') ? 0 : matched[1],\n        stops: stops\n      };\n    } else {\n      return false;\n    }\n  },\n\n  parseStops: function(string) {\n    let matched;\n    const result = [];\n    if ((matched = string.match(RegExpStrings.STOPS)) !== null) {\n\n      $.each(matched, (i, item) => {\n        const stop = this.parseStop(item);\n        if (stop) {\n          result.push(stop);\n        }\n      });\n      return result;\n    } else {\n      return false;\n    }\n  },\n\n  formatStops: function(stops, cleanPosition) {\n    let stop;\n    const output = [];\n    let positions = [];\n    const colors = [];\n    let position;\n\n    for (let i = 0; i < stops.length; i++) {\n      stop = stops[i];\n      if (typeof stop.position === 'undefined' || stop.position === null) {\n        if (i === 0) {\n          position = 0;\n        } else if (i === stops.length - 1) {\n          position = 1;\n        } else {\n          position = undefined;\n        }\n      } else {\n        position = stop.position;\n      }\n      positions.push(position);\n      colors.push(stop.color.toString());\n    }\n\n    positions = ((data => {\n      let start = null;\n      let average;\n      for (let i = 0; i < data.length; i++) {\n        if (isNaN(data[i])) {\n          if (start === null) {\n            start = i;\n            continue;\n          }\n        } else if (start) {\n          average = (data[i] - data[start - 1]) / (i - start + 1);\n          for (let j = start; j < i; j++) {\n            data[j] = data[start - 1] + (j - start + 1) * average;\n          }\n          start = null;\n        }\n      }\n\n      return data;\n    }))(positions);\n\n    for (let x = 0; x < stops.length; x++) {\n      if (cleanPosition && ((x === 0 && positions[x] === 0) || (x === stops.length - 1 && positions[x] === 1))) {\n        position = '';\n      } else {\n        position = ` ${this.formatPosition(positions[x])}`;\n      }\n\n      output.push(colors[x] + position);\n    }\n    return output.join(', ');\n  },\n\n  parseStop: function(string) {\n    let matched;\n    if ((matched = RegExpStrings.STOP.exec(string)) !== null) {\n      let position = this.parsePosition(matched[2]);\n\n      return {\n        color: matched[1],\n        position: position\n      };\n    } else {\n      return false;\n    }\n  },\n\n  parsePosition: function(string) {\n    if (typeof string === 'string' && string.substr(-1) === '%') {\n      string = parseFloat(string.slice(0, -1) / 100);\n    }\n\n    if(typeof string !== 'undefined' && string !== null) {\n      return parseFloat(string, 10);\n    } else {\n      return null;\n    }\n  },\n\n  formatPosition: function(value) {\n    return `${parseInt(value * 100, 10)}%`;\n  },\n\n  parseAngle: function(string, notStandard) {\n    if (typeof string === 'string' && string.includes('deg')) {\n      string = string.replace('deg', '');\n    }\n    if (!isNaN(string)) {\n      if (notStandard) {\n        string = this.fixOldAngle(string);\n      }\n    }\n    if (typeof string === 'string') {\n      const directions = string.split(' ');\n\n      const filtered = [];\n      for (const i in directions) {\n        if (isDirection(directions[i])) {\n          filtered.push(directions[i].toLowerCase());\n        }\n      }\n      let keyword = filtered.join(' ');\n\n      if (!string.includes('to ')) {\n        keyword = reverseDirection(keyword);\n      }\n      keyword = `to ${keyword}`;\n      if (keywordAngleMap.hasOwnProperty(keyword)) {\n        string = keywordAngleMap[keyword];\n      }\n    }\n    let value = parseFloat(string, 10);\n\n    if (value > 360) {\n      value %= 360;\n    } else if (value < 0) {\n      value %= -360;\n\n      if (value !== 0) {\n        value += 360;\n      }\n    }\n    return value;\n  },\n\n  fixOldAngle: function(value) {\n    value = parseFloat(value);\n    value = Math.abs(450 - value) % 360;\n    value = parseFloat(value.toFixed(3));\n    return value;\n  },\n\n  formatAngle: function(value, notStandard, useKeyword) {\n    value = parseInt(value, 10);\n    if (useKeyword && angleKeywordMap.hasOwnProperty(value)) {\n      value = angleKeywordMap[value];\n      if (notStandard) {\n        value = reverseDirection(value.substr(3));\n      }\n    } else {\n      if (notStandard) {\n        value = this.fixOldAngle(value);\n      }\n      value = `${value}deg`;\n    }\n\n    return value;\n  }\n};\n\nclass ColorStop {\n  constructor(color, position, gradient) {\n    this.color = Color(color, gradient.options.color);\n    this.position = GradientString.parsePosition(position);\n    this.id = ++gradient._stopIdCount;\n    this.gradient = gradient;\n  }\n\n  setPosition(string) {\n    const position = GradientString.parsePosition(string);\n    if(this.position !== position){\n      this.position = position;\n      this.gradient.reorder();\n    }\n  }\n\n  setColor(string) {\n    this.color.fromString(string);\n  }\n\n  remove() {\n    this.gradient.removeById(this.id);\n  }\n}\n\nvar GradientTypes = {\n  LINEAR: {\n    parse(result) {\n      return {\n        r: (result[1].substr(-1) === '%') ? parseInt(result[1].slice(0, -1) * 2.55, 10) : parseInt(result[1], 10),\n        g: (result[2].substr(-1) === '%') ? parseInt(result[2].slice(0, -1) * 2.55, 10) : parseInt(result[2], 10),\n        b: (result[3].substr(-1) === '%') ? parseInt(result[3].slice(0, -1) * 2.55, 10) : parseInt(result[3], 10),\n        a: 1\n      };\n    },\n    to(gradient, instance, prefix) {\n      if (gradient.stops.length === 0) {\n        return instance.options.emptyString;\n      }\n      if (gradient.stops.length === 1) {\n        return gradient.stops[0].color.to(instance.options.degradationFormat);\n      }\n\n      let standard = instance.options.forceStandard;\n      let _prefix = instance._prefix;\n\n      if (!_prefix) {\n        standard = true;\n      }\n      if (prefix && -1 !== $.inArray(prefix, instance.options.prefixes)) {\n        standard = false;\n        _prefix = prefix;\n      }\n\n      const angle = GradientString.formatAngle(gradient.angle, !standard, instance.options.angleUseKeyword);\n      const stops = GradientString.formatStops(gradient.stops, instance.options.cleanPosition);\n\n      const output = `linear-gradient(${angle}, ${stops})`;\n      if (standard) {\n        return output;\n      } else {\n        return _prefix + output;\n      }\n    }\n  }\n};\n\nclass AsGradient {\n  constructor(string, options) {\n    if (typeof string === 'object' && typeof options === 'undefined') {\n      options = string;\n      string = undefined;\n    }\n    this.value = {\n      angle: 0,\n      stops: []\n    };\n    this.options = $.extend(true, {}, DEFAULTS, options);\n\n    this._type = 'LINEAR';\n    this._prefix = null;\n    this.length = this.value.stops.length;\n    this.current = 0;\n    this._stopIdCount = 0;\n\n    this.init(string);\n  }\n\n  init(string) {\n    if (string) {\n      this.fromString(string);\n    }\n  }\n\n  val(value) {\n    if (typeof value === 'undefined') {\n      return this.toString();\n    } else {\n      this.fromString(value);\n      return this;\n    }\n  }\n\n  angle(value) {\n    if (typeof value === 'undefined') {\n      return this.value.angle;\n    } else {\n      this.value.angle = GradientString.parseAngle(value);\n      return this;\n    }\n  }\n\n  append(color, position) {\n    return this.insert(color, position, this.length);\n  }\n\n  reorder() {\n    if(this.length < 2){\n      return;\n    }\n\n    this.value.stops = this.value.stops.sort((a, b) => a.position - b.position);\n  }\n\n  insert(color, position, index) {\n    if (typeof index === 'undefined') {\n      index = this.current;\n    }\n\n    const stop = new ColorStop(color, position, this);\n\n    this.value.stops.splice(index, 0, stop);\n\n    this.length = this.length + 1;\n    this.current = index;\n    return stop;\n  }\n\n  getById(id) {\n    if(this.length > 0){\n      for(const i in this.value.stops){\n        if(id === this.value.stops[i].id){\n          return this.value.stops[i];\n        }\n      }\n    }\n    return false;\n  }\n\n  removeById(id) {\n    const index = this.getIndexById(id);\n    if(index){\n      this.remove(index);\n    }\n  }\n\n  getIndexById(id) {\n    let index = 0;\n    for(const i in this.value.stops){\n      if(id === this.value.stops[i].id){\n        return index;\n      }\n      index ++;\n    }\n    return false;\n  }\n\n  getCurrent() {\n    return this.value.stops[this.current];\n  }\n\n  setCurrentById(id) {\n    let index = 0;\n    for(const i in this.value.stops){\n      if(this.value.stops[i].id !== id){\n        index ++;\n      } else {\n        this.current = index;\n      }\n    }\n  }\n\n  get(index) {\n    if (typeof index === 'undefined') {\n      index = this.current;\n    }\n    if (index >= 0 && index < this.length) {\n      this.current = index;\n      return this.value.stops[index];\n    } else {\n      return false;\n    }\n  }\n\n  remove(index) {\n    if (typeof index === 'undefined') {\n      index = this.current;\n    }\n    if (index >= 0 && index < this.length) {\n      this.value.stops.splice(index, 1);\n      this.length = this.length - 1;\n      this.current = index - 1;\n    }\n  }\n\n  empty() {\n    this.value.stops = [];\n    this.length = 0;\n    this.current = 0;\n  }\n\n  reset() {\n    this.value._angle = 0;\n    this.empty();\n    this._prefix = null;\n    this._type = 'LINEAR';\n  }\n\n  type(type) {\n    if (typeof type === 'string' && (type = type.toUpperCase()) && typeof GradientTypes[type] !== 'undefined') {\n      this._type = type;\n      return this;\n    } else {\n      return this._type;\n    }\n  }\n\n  fromString(string) {\n    this.reset();\n\n    const result = GradientString.parseString(string);\n\n    if (result) {\n      this._prefix = result.prefix;\n      this.type(result.type);\n      if (result.value) {\n        this.value.angle = GradientString.parseAngle(result.value.angle, this._prefix !== null);\n\n        $.each(result.value.stops, (i, stop) => {\n          this.append(stop.color, stop.position);\n        });\n      }\n    }\n  }\n\n  toString(prefix) {\n    if(prefix === true){\n      prefix = getPrefix();\n    }\n    return GradientTypes[this.type()].to(this.value, this, prefix);\n  }\n\n  matchString(string) {\n    return GradientString.matchString(string);\n  }\n\n  toStringWithAngle(angle, prefix) {\n    const value = $.extend(true, {}, this.value);\n    value.angle = GradientString.parseAngle(angle);\n\n    if(prefix === true){\n      prefix = getPrefix();\n    }\n\n    return GradientTypes[this.type()].to(value, this, prefix);\n  }\n\n  getPrefixedStrings() {\n    const strings = [];\n    for (let i in this.options.prefixes) {\n      if(Object.hasOwnProperty.call(this.options.prefixes, i)){\n        strings.push(this.toString(this.options.prefixes[i]));\n      }\n    }\n    return strings;\n  }\n\n  static setDefaults(options) {\n    $.extend(true, DEFAULTS, $.isPlainObject(options) && options);\n  }\n}\n\nvar info = {\n  version:'0.3.3'\n};\n\nconst OtherAsGradient = $.asGradient;\n\nconst jQueryAsGradient = function(...args) {\n  return new AsGradient(...args);\n};\n\n$.asGradient = jQueryAsGradient;\n$.asGradient.Constructor = AsGradient;\n\n$.extend($.asGradient, {\n  setDefaults: AsGradient.setDefaults,\n  noConflict: function() {\n    $.asGradient = OtherAsGradient;\n    return jQueryAsGradient;\n  }\n}, GradientString, info);\n\nvar main = $.asGradient;\n\nexport default main;\n"]}