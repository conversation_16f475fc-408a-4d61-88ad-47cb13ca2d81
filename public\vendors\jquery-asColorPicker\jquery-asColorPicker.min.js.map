{"version": 3, "sources": ["jquery-asColorPicker.es.js"], "names": ["global", "factory", "define", "amd", "exports", "require", "mod", "j<PERSON><PERSON><PERSON>", "AsColor", "AsGrad<PERSON>", "jqueryAsColorPickerEs", "this", "_j<PERSON>y", "_jqueryAsColor", "_jqueryAsGradient", "_interopRequireDefault", "obj", "__esModule", "default", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "conventToPercentage", "n", "createId", "api", "id", "_jquery2", "_jqueryAsColor2", "_jqueryAsGradient2", "_createClass", "defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "Object", "defineProperty", "key", "protoProps", "staticProps", "prototype", "DEFAULTS", "namespace", "readonly", "skin", "lang", "hideInput", "hideFireChange", "keyboard", "color", "format", "alphaConvert", "RGB", "HSL", "HEX", "NAMESPACE", "shortenHex", "hexUseName", "reduceAlpha", "nameDegradation", "invalidV<PERSON>ue", "zeroAlphaAsTransparent", "mode", "onInit", "onReady", "onChange", "onClose", "onOpen", "onApply", "MODES", "simple", "trigger", "clear", "saturation", "hue", "alpha", "palettes", "complex", "preview", "hex", "buttons", "gradient", "size", "defaults", "direction", "template", "data", "init", "options", "that", "$", "extend", "$alpha", "call", "appendTo", "$dropdown", "$handle", "find", "$element", "on", "height", "width", "step", "bindEvents", "e", "update", "eventName", "which", "button", "proxy", "mousedown", "offset", "startY", "pageY", "top", "move", "startX", "pageX", "left", "mousemove", "position", "mouseup", "document", "off", "cach", "Math", "max", "min", "css", "set", "a", "round", "moveLeft", "moveRight", "moveUp", "moveDown", "_keyboard", "attr", "attach", "up", "down", "right", "detach", "value", "toHEX", "destroy", "$hex", "val", "$hue", "hub", "h", "$saturation", "x", "y", "undefined", "s", "v", "HSLtoHEX", "l", "apply", "cancel", "applyText", "cancelText", "applyTemplate", "cancelTemplate", "getString", "$buttons", "$apply", "$cancel", "$trigger", "$triggerInner", "children", "insertAfter", "opened", "close", "open", "toString", "toRGBA", "remove", "$clear", "info", "$info", "$r", "$g", "$b", "$a", "type", "parseInt", "parseFloat", "isNaN", "r", "g", "b", "window", "localStorage", "item", "colors", "asColor", "localKey", "getLocal", "setLocal", "hasOwnProperty", "push", "list", "each", "$palettes", "html", "preventDefault", "stopPropagation", "get", "rgba", "inArray", "shift", "eq", "append", "jsonValue", "JSON", "stringify", "parse", "$preview", "$current", "$previous", "updateCurrent", "updatePreview", "Gradient", "classes", "enable", "marker", "active", "focus", "isEnabled", "initialized", "current", "settings", "$doc", "$wrap", "$gradient", "filter", "angle", "markers", "wheel", "bind", "switchable", "matchString", "element", "getById", "_trigger", "_updateInput", "disable", "originValue", "overrideCore", "isEmpty", "stop", "_setup", "revertCore", "_set", "_this", "render", "background-image", "toStringWithAngle", "_this2", "$markers", "add", "del", "empty", "self", "is", "keyCode", "addClass", "removeClass", "<PERSON><PERSON><PERSON><PERSON>", "$marker", "$to", "prev", "next", "first", "start", "end", "setPosition", "_this3", "$wheel", "$pointer", "_this4", "wheelMove", "getPosition", "calAngle", "setAngle", "wheelMouseup", "sqrt", "deg", "atan", "abs", "PI", "pos", "calPointer", "sin", "cos", "$angle", "blur", "constructor", "_last", "string", "reorder", "fill", "get<PERSON>urrent", "setCurrentById", "insert", "removeById", "switchText", "forceStandard", "angleUseKeyword", "emptyString", "degradationFormat", "cleanPosition", "control", "event", "COMPONENTS", "LOCALIZATIONS", "en", "AsColorPicker", "firstOpen", "disabled", "initialed", "wrap", "dropdown", "input", "mask", "components", "_components", "eventType", "_len", "arguments", "params", "Array", "_key", "concat", "NAMESPACE$1", "onFunction", "replace", "word", "substring", "toUpperCase", "events", "split", "join", "_create", "parent", "prop", "_bindEvent", "_this5", "$body", "component", "_this6", "hidden", "outerHeight", "outerWidth", "picker<PERSON><PERSON><PERSON>", "pickerHeight", "scrollTop", "scrollLeft", "last", "$mask", "createMask", "before", "removeAttr", "show", "createElement", "hide"], "mappings": ";;;;;;;CAAA,SAAAA,EAAAC,GACA,GAAA,mBAAAC,QAAAA,OAAAC,IACAD,QAAA,SAAA,iBAAA,qBAAAD,QACA,GAAA,oBAAAG,QACAH,EACAI,QAAA,UACAA,QAAA,kBACAA,QAAA,0BAEA,CACA,IAAAC,GACAF,YAEAH,EAAAD,EAAAO,OAAAP,EAAAQ,QAAAR,EAAAS,YACAT,EAAAU,sBAAAJ,EAAAF,SAdA,CAgBAO,KAAA,SAAAC,EAAAC,EAAAC,GACA,aAQA,SAAAC,EAAAC,GACA,OAAAA,GAAAA,EAAAC,WACAD,GAEAE,QAAAF,GAIA,SAAAG,EAAAC,EAAAC,GACA,KAAAD,aAAAC,GACA,MAAA,IAAAC,UAAA,qCA2nCA,SAAAC,EAAAC,GAMA,OALAA,EAAA,EACAA,EAAA,EACAA,EAAA,IACAA,EAAA,GAEA,IAAAA,EAAA,IAisBA,SAAAC,EAAAC,GACAA,EAAAC,GAAAA,EACAA,IAp1DA,IAAAC,EAAAb,EAAAH,GAEAiB,EAAAd,EAAAF,GAEAiB,EAAAf,EAAAD,GAgBAiB,EAAA,WACA,SAAAC,EAAAC,EAAAC,GACA,IAAA,IAAAC,EAAA,EAAAA,EAAAD,EAAAE,OAAAD,IAAA,CACA,IAAAE,EAAAH,EAAAC,GACAE,EAAAC,WAAAD,EAAAC,aAAA,EACAD,EAAAE,cAAA,EACA,UAAAF,IAAAA,EAAAG,UAAA,GACAC,OAAAC,eAAAT,EAAAI,EAAAM,IAAAN,IAIA,OAAA,SAAAhB,EAAAuB,EAAAC,GAGA,OAFAD,GAAAZ,EAAAX,EAAAyB,UAAAF,GACAC,GAAAb,EAAAX,EAAAwB,GACAxB,GAdA,GAkBA0B,GACAC,UAAA,gBACAC,UAAA,EACAC,KAAA,KACAC,KAAA,KACAC,WAAA,EACAC,gBAAA,EACAC,UAAA,EACAC,OACAC,QAAA,EACAC,cAEAC,IAAA,OACAC,IAAA,OACAC,IAAA,OACAC,UAAA,QAEAC,YAAA,EACAC,YAAA,EACAC,aAAA,EACAC,gBAAA,MACAC,aAAA,GACAC,wBAAA,GAEAC,KAAA,SACAC,OAAA,KACAC,QAAA,KACAC,SAAA,KACAC,QAAA,KACAC,OAAA,KACAC,QAAA,MAGAC,GACAC,QACAC,SAAA,EACAC,OAAA,EACAC,YAAA,EACAC,KAAA,EACAC,OAAA,GAEAC,UACAL,SAAA,EACAC,OAAA,EACAI,UAAA,GAEAC,SACAN,SAAA,EACAC,OAAA,EACAM,SAAA,EACAF,UAAA,EACAH,YAAA,EACAC,KAAA,EACAC,OAAA,EACAI,KAAA,EACAC,SAAA,GAEAC,UACAV,SAAA,EACAC,OAAA,EACAM,SAAA,EACAF,UAAA,EACAH,YAAA,EACAC,KAAA,EACAC,OAAA,EACAI,KAAA,EACAE,UAAA,IAKAN,GACAO,KAAA,IAEAC,UACAC,UAAA,WACAC,SAAA,SAAA3C,GACA,MACA,eACAA,EACA,UACAA,EACA,UACArC,KAAA+E,UACA,oBAKAE,QAEAC,KAAA,SAAAnE,EAAAoE,GACA,IAAAC,EAAApF,KAEAA,KAAAmF,QAAAE,EAAAC,OAAAtF,KAAA8E,SAAAK,GACAC,EAAAL,UAAA/E,KAAAmF,QAAAJ,UACA/E,KAAAe,IAAAA,EAEAf,KAAAuF,OAAAF,EAAArF,KAAAmF,QAAAH,SAAAQ,KAAAJ,EAAArE,EAAAsB,YAAAoD,SACA1E,EAAA2E,WAEA1F,KAAA2F,QAAA3F,KAAAuF,OAAAK,KAAA,KAEA7E,EAAA8E,SAAAC,GAAA,2BAAA,WAEA,aAAAV,EAAAL,UACAK,EAAAP,KAAAO,EAAAG,OAAAQ,SAEAX,EAAAP,KAAAO,EAAAG,OAAAS,QAEAZ,EAAAa,KAAAb,EAAAP,KAAA,IAGAO,EAAAc,aACAd,EAAAzC,aAGA5B,EAAA8E,SAAAC,GAAA,6CAAA,SACAK,EACApF,EACA6B,GAEAwC,EAAAgB,OAAAxD,MAIAsD,WAAA,WACA,IAAAd,EAAApF,KACAA,KAAAuF,OAAAO,GAAA9F,KAAAe,IAAAsF,UAAA,aAAA,SAAAF,GAEA,GADAA,EAAAG,MAAA,IAAAH,EAAAG,MAAA,IAAAH,EAAAI,OAEA,OAAA,EAEAlB,EAAAmB,MAAApB,EAAAqB,UAAArB,GAAAe,MAIAM,UAAA,SAAAN,GACA,IAAAO,EAAA1G,KAAAuF,OAAAmB,SA2CA,MA1CA,aAAA1G,KAAA+E,WACA/E,KAAAiF,KAAA0B,OAAAR,EAAAS,MACA5G,KAAAiF,KAAA4B,IAAAV,EAAAS,MAAAF,EAAAG,IACA7G,KAAA8G,KAAA9G,KAAAiF,KAAA4B,OAEA7G,KAAAiF,KAAA8B,OAAAZ,EAAAa,MACAhH,KAAAiF,KAAAgC,KAAAd,EAAAa,MAAAN,EAAAO,KACAjH,KAAA8G,KAAA9G,KAAAiF,KAAAgC,OAGAjH,KAAAkH,UAAA,SAAAf,GACA,IAAAgB,OAAA,EAUA,OARAA,EADA,aAAAnH,KAAA+E,UAEA/E,KAAAiF,KAAA4B,KAAAV,EAAAS,OAAA5G,KAAAiF,KAAA0B,QAAA3G,KAAAiF,KAAA0B,OAGA3G,KAAAiF,KAAAgC,MAAAd,EAAAa,OAAAhH,KAAAiF,KAAA8B,QAAA/G,KAAAiF,KAAA8B,OAGA/G,KAAA8G,KAAAK,IACA,GAGAnH,KAAAoH,QAAA,WAWA,OAVA/B,EAAAgC,UAAAC,KACAJ,UAAAlH,KAAAkH,UACAE,QAAApH,KAAAoH,UAEA,aAAApH,KAAA+E,UACA/E,KAAAiF,KAAA4B,IAAA7G,KAAAiF,KAAAsC,KAEAvH,KAAAiF,KAAAgC,KAAAjH,KAAAiF,KAAAsC,MAGA,GAGAlC,EAAAgC,UAAAvB,IACAoB,UAAA7B,EAAAmB,MAAAxG,KAAAkH,UAAAlH,MACAoH,QAAA/B,EAAAmB,MAAAxG,KAAAoH,QAAApH,SAEA,GAGA8G,KAAA,SAAAK,EAAA7C,EAAA8B,GACAe,EAAAK,KAAAC,IAAA,EAAAD,KAAAE,IAAA1H,KAAA6E,KAAAsC,IACAnH,KAAAiF,KAAAsC,KAAAJ,OACA,IAAA7C,IACAA,EAAA,EAAA6C,EAAAnH,KAAA6E,MAEAP,EAAAkD,KAAAC,IAAA,EAAAD,KAAAE,IAAA,EAAApD,IACA,aAAAtE,KAAA+E,UACA/E,KAAA2F,QAAAgC,KACAd,IAAAM,IAGAnH,KAAA2F,QAAAgC,KACAV,KAAAE,KAIA,IAAAf,GACApG,KAAAe,IAAA6G,KACAC,EAAAL,KAAAM,MAAA,IAAAxD,GAAA,OAKAyD,SAAA,WACA,IAAA9B,EAAAjG,KAAAiG,KACAhB,EAAAjF,KAAAiF,KACAA,EAAAgC,KAAAO,KAAAC,IAAA,EAAAD,KAAAE,IAAA1H,KAAAgG,MAAAf,EAAAgC,KAAAhB,IACAjG,KAAA8G,KAAA7B,EAAAgC,OAGAe,UAAA,WACA,IAAA/B,EAAAjG,KAAAiG,KACAhB,EAAAjF,KAAAiF,KACAA,EAAAgC,KAAAO,KAAAC,IAAA,EAAAD,KAAAE,IAAA1H,KAAAgG,MAAAf,EAAAgC,KAAAhB,IACAjG,KAAA8G,KAAA7B,EAAAgC,OAGAgB,OAAA,WACA,IAAAhC,EAAAjG,KAAAiG,KACAhB,EAAAjF,KAAAiF,KACAA,EAAA4B,IAAAW,KAAAC,IAAA,EAAAD,KAAAE,IAAA1H,KAAAgG,MAAAf,EAAA4B,IAAAZ,IACAjG,KAAA8G,KAAA7B,EAAA4B,MAGAqB,SAAA,WACA,IAAAjC,EAAAjG,KAAAiG,KACAhB,EAAAjF,KAAAiF,KACAA,EAAA4B,IAAAW,KAAAC,IAAA,EAAAD,KAAAE,IAAA1H,KAAAgG,MAAAf,EAAA4B,IAAAZ,IACAjG,KAAA8G,KAAA7B,EAAA4B,MAGAlE,SAAA,WACA,IAAAA,OAAA,EACAyC,EAAApF,KACA,IAAAA,KAAAe,IAAAoH,UAGA,OAAA,EAFAxF,EAAA0C,EAAAC,QAAA,KAAAtF,KAAAe,IAAAoH,WAKAnI,KAAAuF,OACA6C,KAAA,WAAA,KACAtC,GAAA,QAAA,WAoBA,MAnBA,aAAA9F,KAAA+E,UACApC,EAAA0F,QACAC,GAAA,WACAlD,EAAA6C,UAEAM,KAAA,WACAnD,EAAA8C,cAIAvF,EAAA0F,QACApB,KAAA,WACA7B,EAAA2C,YAEAS,MAAA,WACApD,EAAA4C,gBAIA,IAEAlC,GAAA,OAAA,WACAnD,EAAA8F,YAIArC,OAAA,SAAAxD,GACA,IAAAuE,EAAAnH,KAAA6E,MAAA,EAAAjC,EAAA8F,MAAAb,GACA7H,KAAAuF,OAAAoC,IAAA,kBAAA/E,EAAA+F,SAEA3I,KAAA8G,KAAAK,EAAAvE,EAAA8F,MAAAb,GAAA,IAGAe,QAAA,WACAvD,EAAAgC,UAAAC,KACAJ,UAAAlH,KAAAkH,UACAE,QAAApH,KAAAoH,YAMA1C,GACAQ,KAAA,SAAAnE,GACA,IAAAiE,EAAA,6BAAAjE,EAAAsB,UAAA,WACArC,KAAA6I,KAAAxD,EAAAL,GAAAS,SAAA1E,EAAA2E,WAEA1F,KAAA6I,KAAA/C,GAAA,SAAA,WACA/E,EAAA6G,IAAA5H,KAAA0I,SAGA,IAAAtD,EAAApF,KACAe,EAAA8E,SAAAC,GAAA,6CAAA,SACAK,EACApF,EACA6B,GAEAwC,EAAAgB,OAAAxD,MAIAwD,OAAA,SAAAxD,GACA5C,KAAA6I,KAAAC,IAAAlG,EAAA+F,WAKAtE,GACAQ,KAAA,IAEAC,UACAC,UAAA,WACAC,SAAA,WACA,IAAA3C,EAAArC,KAAAe,IAAAsB,UACA,MACA,eACAA,EACA,QACAA,EACA,QACArC,KAAA+E,UACA,oBAKAE,QAEAC,KAAA,SAAAnE,EAAAoE,GACA,IAAAC,EAAApF,KAEAA,KAAAmF,QAAAE,EAAAC,OAAAtF,KAAA8E,SAAAK,GACAnF,KAAA+E,UAAA/E,KAAAmF,QAAAJ,UACA/E,KAAAe,IAAAA,EAEAf,KAAA+I,KAAA1D,EAAArF,KAAAmF,QAAAH,SAAAQ,KAAAJ,IAAAK,SAAA1E,EAAA2E,WACA1F,KAAA2F,QAAA3F,KAAA+I,KAAAnD,KAAA,KAEA7E,EAAA8E,SAAAC,GAAA,2BAAA,WAEA,aAAAV,EAAAL,UACAK,EAAAP,KAAAO,EAAA2D,KAAAhD,SAEAX,EAAAP,KAAAO,EAAA2D,KAAA/C,QAEAZ,EAAAa,KAAAb,EAAAP,KAAA,IAGAO,EAAAc,WAAAnF,GACAqE,EAAAzC,SAAA5B,KAGAA,EAAA8E,SAAAC,GAAA,6CAAA,SACAK,EACApF,EACA6B,GAEAwC,EAAAgB,OAAAxD,MAIAsD,WAAA,WACA,IAAAd,EAAApF,KACAA,KAAA+I,KAAAjD,GAAA9F,KAAAe,IAAAsF,UAAA,aAAA,SAAAF,GAEA,GADAA,EAAAG,MAAA,IAAAH,EAAAG,MAAA,IAAAH,EAAAI,OAEA,OAAA,EAEAlB,EAAAmB,MAAApB,EAAAqB,UAAArB,GAAAe,MAIAM,UAAA,SAAAN,GACA,IAAAO,EAAA1G,KAAA+I,KAAArC,SA4CA,MA3CA,aAAA1G,KAAA+E,WACA/E,KAAAiF,KAAA0B,OAAAR,EAAAS,MACA5G,KAAAiF,KAAA4B,IAAAV,EAAAS,MAAAF,EAAAG,IACA7G,KAAA8G,KAAA9G,KAAAiF,KAAA4B,OAEA7G,KAAAiF,KAAA8B,OAAAZ,EAAAa,MACAhH,KAAAiF,KAAAgC,KAAAd,EAAAa,MAAAN,EAAAO,KACAjH,KAAA8G,KAAA9G,KAAAiF,KAAAgC,OAGAjH,KAAAkH,UAAA,SAAAf,GACA,IAAAgB,OAAA,EAUA,OARAA,EADA,aAAAnH,KAAA+E,UAEA/E,KAAAiF,KAAA4B,KAAAV,EAAAS,OAAA5G,KAAAiF,KAAA0B,QAAA3G,KAAAiF,KAAA0B,OAGA3G,KAAAiF,KAAAgC,MAAAd,EAAAa,OAAAhH,KAAAiF,KAAA8B,QAAA/G,KAAAiF,KAAA8B,OAGA/G,KAAA8G,KAAAK,IACA,GAGAnH,KAAAoH,QAAA,WAWA,OAVA/B,EAAAgC,UAAAC,KACAJ,UAAAlH,KAAAkH,UACAE,QAAApH,KAAAoH,UAEA,aAAApH,KAAA+E,UACA/E,KAAAiF,KAAA4B,IAAA7G,KAAAiF,KAAAsC,KAEAvH,KAAAiF,KAAAgC,KAAAjH,KAAAiF,KAAAsC,MAGA,GAGAlC,EAAAgC,UAAAvB,IACAoB,UAAA7B,EAAAmB,MAAAxG,KAAAkH,UAAAlH,MACAoH,QAAA/B,EAAAmB,MAAAxG,KAAAoH,QAAApH,SAGA,GAGA8G,KAAA,SAAAK,EAAA6B,EAAA5C,GACAe,EAAAK,KAAAC,IAAA,EAAAD,KAAAE,IAAA1H,KAAA6E,KAAAsC,IACAnH,KAAAiF,KAAAsC,KAAAJ,OACA,IAAA6B,IACAA,EAAA,KAAA,EAAA7B,EAAAnH,KAAA6E,OAEAmE,EAAAxB,KAAAC,IAAA,EAAAD,KAAAE,IAAA,IAAAsB,IACA,aAAAhJ,KAAA+E,UACA/E,KAAA2F,QAAAgC,KACAd,IAAAM,IAGAnH,KAAA2F,QAAAgC,KACAV,KAAAE,KAGA,IAAAf,GACApG,KAAAe,IAAA6G,KACAqB,EAAAD,KAKAjB,SAAA,WACA,IAAA9B,EAAAjG,KAAAiG,KACAhB,EAAAjF,KAAAiF,KACAA,EAAAgC,KAAAO,KAAAC,IAAA,EAAAD,KAAAE,IAAA1H,KAAAgG,MAAAf,EAAAgC,KAAAhB,IACAjG,KAAA8G,KAAA7B,EAAAgC,OAGAe,UAAA,WACA,IAAA/B,EAAAjG,KAAAiG,KACAhB,EAAAjF,KAAAiF,KACAA,EAAAgC,KAAAO,KAAAC,IAAA,EAAAD,KAAAE,IAAA1H,KAAAgG,MAAAf,EAAAgC,KAAAhB,IACAjG,KAAA8G,KAAA7B,EAAAgC,OAGAgB,OAAA,WACA,IAAAhC,EAAAjG,KAAAiG,KACAhB,EAAAjF,KAAAiF,KACAA,EAAA4B,IAAAW,KAAAC,IAAA,EAAAD,KAAAE,IAAA1H,KAAAgG,MAAAf,EAAA4B,IAAAZ,IACAjG,KAAA8G,KAAA7B,EAAA4B,MAGAqB,SAAA,WACA,IAAAjC,EAAAjG,KAAAiG,KACAhB,EAAAjF,KAAAiF,KACAA,EAAA4B,IAAAW,KAAAC,IAAA,EAAAD,KAAAE,IAAA1H,KAAAgG,MAAAf,EAAA4B,IAAAZ,IACAjG,KAAA8G,KAAA7B,EAAA4B,MAGAlE,SAAA,WACA,IAAAA,OAAA,EACAyC,EAAApF,KACA,IAAAA,KAAAe,IAAAoH,UAGA,OAAA,EAFAxF,EAAA0C,EAAAC,QAAA,KAAAtF,KAAAe,IAAAoH,WAKAnI,KAAA+I,KACAX,KAAA,WAAA,KACAtC,GAAA,QAAA,WAoBA,MAnBA,aAAA9F,KAAA+E,UACApC,EAAA0F,QACAC,GAAA,WACAlD,EAAA6C,UAEAM,KAAA,WACAnD,EAAA8C,cAIAvF,EAAA0F,QACApB,KAAA,WACA7B,EAAA2C,YAEAS,MAAA,WACApD,EAAA4C,gBAIA,IAEAlC,GAAA,OAAA,WACAnD,EAAA8F,YAIArC,OAAA,SAAAxD,GACA,IAAAuE,EACA,IAAAvE,EAAA8F,MAAAO,EAAA,EAAAjJ,KAAA6E,MAAA,EAAAjC,EAAA8F,MAAAO,EAAA,KACAjJ,KAAA8G,KAAAK,EAAAvE,EAAA8F,MAAAO,GAAA,IAGAL,QAAA,WACAvD,EAAAgC,UAAAC,KACAJ,UAAAlH,KAAAkH,UACAE,QAAApH,KAAAoH,YAMAhD,GACAU,UACAE,SAAA,SAAA3C,GACA,MAAA,eAAAA,EAAA,sCAIA2D,MAAA,EACAD,OAAA,EACAlB,KAAA,EACAI,QAEAC,KAAA,SAAAnE,EAAAoE,GACA,IAAAC,EAAApF,KACAA,KAAAmF,QAAAE,EAAAC,OAAAtF,KAAA8E,SAAAK,GACAnF,KAAAe,IAAAA,EAGAf,KAAAkJ,YAAA7D,EACArF,KAAAmF,QAAAH,SAAAQ,KAAAJ,EAAArE,EAAAsB,YACAoD,SAAA1E,EAAA2E,WACA1F,KAAA2F,QAAA3F,KAAAkJ,YAAAtD,KAAA,KAEA7E,EAAA8E,SAAAC,GAAA,2BAAA,WAEAV,EAAAY,MAAAZ,EAAA8D,YAAAlD,QACAZ,EAAAW,OAAAX,EAAA8D,YAAAnD,SACAX,EAAAa,MACAgB,KAAA7B,EAAAY,MAAA,GACAa,IAAAzB,EAAAW,OAAA,IAEAX,EAAAP,KAAAO,EAAAO,QAAAK,QAAA,EAGAZ,EAAAc,aACAd,EAAAzC,SAAA5B,KAGAA,EAAA8E,SAAAC,GAAA,6CAAA,SACAK,EACApF,EACA6B,GAEAwC,EAAAgB,OAAAxD,MAIAsD,WAAA,WACA,IAAAd,EAAApF,KAEAA,KAAAkJ,YAAApD,GAAA9F,KAAAe,IAAAsF,UAAA,aAAA,SAAAF,GAEA,GADAA,EAAAG,MAAA,IAAAH,EAAAG,MAAA,IAAAH,EAAAI,OAEA,OAAA,EAEAnB,EAAAqB,UAAAN,MAIAM,UAAA,SAAAN,GACA,IAAAO,EAAA1G,KAAAkJ,YAAAxC,SAmCA,OAjCA1G,KAAAiF,KAAA0B,OAAAR,EAAAS,MACA5G,KAAAiF,KAAA8B,OAAAZ,EAAAa,MACAhH,KAAAiF,KAAA4B,IAAAV,EAAAS,MAAAF,EAAAG,IACA7G,KAAAiF,KAAAgC,KAAAd,EAAAa,MAAAN,EAAAO,KACAjH,KAAAiF,KAAAsC,QAEAvH,KAAA8G,KAAA9G,KAAAiF,KAAAgC,KAAAjH,KAAAiF,KAAA4B,KAEA7G,KAAAkH,UAAA,SAAAf,GACA,IAAAgD,EACAnJ,KAAAiF,KAAAgC,MAAAd,EAAAa,OAAAhH,KAAAiF,KAAA8B,QAAA/G,KAAAiF,KAAA8B,OACAqC,EACApJ,KAAAiF,KAAA4B,KAAAV,EAAAS,OAAA5G,KAAAiF,KAAA0B,QAAA3G,KAAAiF,KAAA0B,OAEA,OADA3G,KAAA8G,KAAAqC,EAAAC,IACA,GAGApJ,KAAAoH,QAAA,WAQA,OAPA/B,EAAAgC,UAAAC,KACAJ,UAAAlH,KAAAkH,UACAE,QAAApH,KAAAoH,UAEApH,KAAAiF,KAAAgC,KAAAjH,KAAAiF,KAAAsC,KAAAN,KACAjH,KAAAiF,KAAA4B,IAAA7G,KAAAiF,KAAAsC,KAAAV,KAEA,GAGAxB,EAAAgC,UAAAvB,IACAoB,UAAA7B,EAAAmB,MAAAxG,KAAAkH,UAAAlH,MACAoH,QAAA/B,EAAAmB,MAAAxG,KAAAoH,QAAApH,SAGA,GAGA8G,KAAA,SAAAqC,EAAAC,EAAAhD,GACAgD,EAAA5B,KAAAC,IAAA,EAAAD,KAAAE,IAAA1H,KAAA+F,OAAAqD,IACAD,EAAA3B,KAAAC,IAAA,EAAAD,KAAAE,IAAA1H,KAAAgG,MAAAmD,SAEAE,IAAArJ,KAAAiF,KAAAsC,OACAvH,KAAAiF,KAAAsC,SAEAvH,KAAAiF,KAAAsC,KAAAN,KAAAkC,EACAnJ,KAAAiF,KAAAsC,KAAAV,IAAAuC,EAEApJ,KAAA2F,QAAAgC,KACAd,IAAAuC,EAAApJ,KAAA6E,KACAoC,KAAAkC,EAAAnJ,KAAA6E,QAGA,IAAAuB,GACApG,KAAAe,IAAA6G,KACA0B,EAAAH,EAAAnJ,KAAAgG,MACAuD,EAAA,EAAAH,EAAApJ,KAAA+F,UAKAK,OAAA,SAAAxD,QACAyG,IAAAzG,EAAA8F,MAAAO,IACArG,EAAA8F,MAAAO,EAAA,GAEAjJ,KAAAkJ,YAAAvB,IACA,kBACAzG,EAAAX,QAAAiJ,UACAP,EAAArG,EAAA8F,MAAAO,EACAK,EAAA,EACAG,EAAA,MAIA,IAAAN,EAAAvG,EAAA8F,MAAAY,EAAAtJ,KAAAgG,MACAoD,GAAA,EAAAxG,EAAA8F,MAAAa,GAAAvJ,KAAA+F,OAEA/F,KAAA8G,KAAAqC,EAAAC,GAAA,IAGArB,SAAA,WACA,IAAA9B,EAAAjG,KAAAiG,KAAAgB,KACAhC,EAAAjF,KAAAiF,KACAA,EAAAgC,KAAAO,KAAAC,IAAA,EAAAD,KAAAE,IAAA1H,KAAAgG,MAAAf,EAAAgC,KAAAhB,IACAjG,KAAA8G,KAAA7B,EAAAgC,KAAAhC,EAAA4B,MAGAmB,UAAA,WACA,IAAA/B,EAAAjG,KAAAiG,KAAAgB,KACAhC,EAAAjF,KAAAiF,KACAA,EAAAgC,KAAAO,KAAAC,IAAA,EAAAD,KAAAE,IAAA1H,KAAAgG,MAAAf,EAAAgC,KAAAhB,IACAjG,KAAA8G,KAAA7B,EAAAgC,KAAAhC,EAAA4B,MAGAoB,OAAA,WACA,IAAAhC,EAAAjG,KAAAiG,KAAAY,IACA5B,EAAAjF,KAAAiF,KACAA,EAAA4B,IAAAW,KAAAC,IAAA,EAAAD,KAAAE,IAAA1H,KAAAgG,MAAAf,EAAA4B,IAAAZ,IACAjG,KAAA8G,KAAA7B,EAAAgC,KAAAhC,EAAA4B,MAGAqB,SAAA,WACA,IAAAjC,EAAAjG,KAAAiG,KAAAY,IACA5B,EAAAjF,KAAAiF,KACAA,EAAA4B,IAAAW,KAAAC,IAAA,EAAAD,KAAAE,IAAA1H,KAAAgG,MAAAf,EAAA4B,IAAAZ,IACAjG,KAAA8G,KAAA7B,EAAAgC,KAAAhC,EAAA4B,MAGAlE,SAAA,WACA,IAAAA,OAAA,EACAyC,EAAApF,KACA,IAAAA,KAAAe,IAAAoH,UAGA,OAAA,EAFAxF,EAAA0C,EAAAC,QAAA,KAAAtF,KAAAe,IAAAoH,WAKAnI,KAAAkJ,YACAd,KAAA,WAAA,KACAtC,GAAA,QAAA,WAeA,OAdAnD,EAAA0F,QACApB,KAAA,WACA7B,EAAA2C,YAEAS,MAAA,WACApD,EAAA4C,aAEAM,GAAA,WACAlD,EAAA6C,UAEAM,KAAA,WACAnD,EAAA8C,eAGA,IAEApC,GAAA,OAAA,WACAnD,EAAA8F,YAIAG,QAAA,WACAvD,EAAAgC,UAAAC,KACAJ,UAAAlH,KAAAkH,UACAE,QAAApH,KAAAoH,YAMAzC,GACAG,UACA4E,OAAA,EACAC,QAAA,EACAC,UAAA,KACAC,WAAA,KACA7E,SAAA,SAAA3C,GACA,MAAA,eAAAA,EAAA,oBAEAyH,cAAA,SAAAzH,GACA,MACA,oBACArC,KAAAmF,QAAAyE,UACA,YACAvH,EACA,mBACArC,KAAAmF,QAAAyE,UACA,QAGAG,eAAA,SAAA1H,GACA,MACA,oBACArC,KAAAmF,QAAA0E,WACA,YACAxH,EACA,mBACArC,KAAAmF,QAAA0E,WACA,SAKA3E,KAAA,SAAAnE,EAAAoE,GACA,IAAAC,EAAApF,KAEAA,KAAAmF,QAAAE,EAAAC,OACAtF,KAAA8E,UAEA8E,UAAA7I,EAAAiJ,UAAA,YAAA,SACAH,WAAA9I,EAAAiJ,UAAA,aAAA,WAEA7E,GAEAnF,KAAAiK,SAAA5E,EACArF,KAAAmF,QAAAH,SAAAQ,KAAAxF,KAAAe,EAAAsB,YACAoD,SAAA1E,EAAA2E,WAEA3E,EAAA8E,SAAAC,GAAA,2BAAA,WACAV,EAAAD,QAAAuE,QACAtE,EAAA8E,OAAA7E,EAAAD,EAAAD,QAAA2E,cAAAtE,KAAAJ,EAAArE,EAAAsB,YACAoD,SAAAL,EAAA6E,UACAnE,GAAA,QAAA,WAEA,OADA/E,EAAA2I,SACA,KAIAtE,EAAAD,QAAAwE,SACAvE,EAAA+E,QAAA9E,EACAD,EAAAD,QAAA4E,eAAAvE,KAAAJ,EAAArE,EAAAsB,YAEAoD,SAAAL,EAAA6E,UACAnE,GAAA,QAAA,WAEA,OADA/E,EAAA4I,UACA,SAQAzF,GACAY,UACAE,SAAA,SAAA3C,GACA,MAAA,eAAAA,EAAA,kCAIA6C,KAAA,SAAAnE,EAAAoE,GACAnF,KAAAmF,QAAAE,EAAAC,OAAAtF,KAAA8E,SAAAK,GACApE,EAAAqJ,SAAA/E,EAAArF,KAAAmF,QAAAH,SAAAQ,KAAAxF,KAAAe,EAAAsB,YACArC,KAAAqK,cAAAtJ,EAAAqJ,SAAAE,SAAA,QAEAvJ,EAAAqJ,SAAAG,YAAAxJ,EAAA8E,UACA9E,EAAAqJ,SAAAtE,GAAA,QAAA,WAMA,OALA/E,EAAAyJ,OAGAzJ,EAAA0J,QAFA1J,EAAA2J,QAIA,IAEA,IAAAtF,EAAApF,KACAe,EAAA8E,SAAAC,GAAA,wBAAA,SACAK,EACApF,EACA6B,EACAgC,QAEA,IAAAA,IACAA,GAAA,GAEAQ,EAAAgB,OAAAxD,EAAAgC,KAGA5E,KAAAoG,OAAArF,EAAA6B,QAGAwD,OAAA,SAAAxD,EAAAgC,GACAA,EACA5E,KAAAqK,cAAA1C,IAAA,aAAA/C,EAAA+F,UAAA,IAEA3K,KAAAqK,cAAA1C,IAAA,aAAA/E,EAAAgI,WAIAhC,QAAA,SAAA7H,GACAA,EAAAqJ,SAAAS,WAKA1G,GACAW,UACAE,SAAA,SAAA3C,GACA,MAAA,sBAAAA,EAAA,iBAIA6C,KAAA,SAAAnE,EAAAoE,GACApE,EAAAoE,QAAA1C,YAGAzC,KAAAmF,QAAAE,EAAAC,OAAAtF,KAAA8E,SAAAK,GACAnF,KAAA8K,OAAAzF,EACArF,KAAAmF,QAAAH,SAAAQ,KAAAxF,KAAAe,EAAAsB,YACAkI,YAAAxJ,EAAA8E,UAEA7F,KAAA8K,OAAAhF,GAAA,QAAA,WAEA,OADA/E,EAAAoD,SACA,OAMA4G,GACAnI,OAAA,QAAA,QAAA,eAEAsC,KAAA,SAAAnE,GACA,IAAAiE,EACA,cACAjE,EAAAsB,UACA,+PACArC,KAAAgL,MAAA3F,EAAAL,GAAAS,SAAA1E,EAAA2E,WACA1F,KAAAiL,GAAAjL,KAAAgL,MAAApF,KAAA,mBACA5F,KAAAkL,GAAAlL,KAAAgL,MAAApF,KAAA,mBACA5F,KAAAmL,GAAAnL,KAAAgL,MAAApF,KAAA,mBACA5F,KAAAoL,GAAApL,KAAAgL,MAAApF,KAAA,mBAEA5F,KAAAgL,MAAAlF,GAAA/E,EAAAsF,UAAA,uBAAA,QAAA,SAAAF,GACA,IAAA2C,OAAA,EACAuC,EAAAhG,EAAAc,EAAA7E,QAAA2D,KAAA,QACA,OAAAoG,GACA,IAAA,IACA,IAAA,IACA,IAAA,KACAvC,EAAAwC,SAAAtL,KAAA0I,MAAA,KACA,IACAI,EAAA,IACAA,EAAA,IACAA,EAAA,GAEA,MACA,IAAA,KACAA,EAAAyC,WAAAvL,KAAA0I,MAAA,KACA,EACAI,EAAA,EACAA,EAAA,IACAA,EAAA,GAMA0C,MAAA1C,KACAA,EAAA,GAEA,IAAAlG,KACAA,EAAAyI,GAAAvC,EACA/H,EAAA6G,IAAAhF,KAGA,IAAAwC,EAAApF,KACAe,EAAA8E,SAAAC,GAAA,6CAAA,SACAK,EACAvD,GAEAwC,EAAAgB,OAAAxD,MAIAwD,OAAA,SAAAxD,GACA5C,KAAAiL,GAAAnC,IAAAlG,EAAA8F,MAAA+C,GACAzL,KAAAkL,GAAApC,IAAAlG,EAAA8F,MAAAgD,GACA1L,KAAAmL,GAAArC,IAAAlG,EAAA8F,MAAAiD,GACA3L,KAAAoL,GAAAtC,IAAAlG,EAAA8F,MAAAb,KAQA+D,OAAAC,eACAD,OAAAC,aAJA,cAOA,IAAAtH,GACAO,UACAE,SAAA,SAAA3C,GACA,MAAA,cAAAA,EAAA,oBAEAyJ,KAAA,SAAAzJ,EAAAO,GACA,MACA,mBACAA,EACA,mCACAA,EACA,aAIAmJ,QAAA,QAAA,QAAA,MAAA,OAAA,UACAtE,IAAA,GACAoE,cAAA,GAGA3G,KAAA,SAAAnE,EAAAoE,GACA,IAAAC,EAAApF,KACA+L,OAAA,EACAC,GAAA,EAAA9K,EAAAX,WAEAP,KAAAmF,QAAAE,EAAAC,QAAA,KAAAtF,KAAA8E,SAAAK,GACAnF,KAAA+L,UACA,IAAAE,OAAA,EAEAjM,KAAAmF,QAAA0G,cACAI,EAAAlL,EAAAsB,UAAA,aAAAtB,EAAAC,IACA+K,EAAA/L,KAAAkM,SAAAD,MAEAF,EAAA/L,KAAAmF,QAAA4G,OACA/L,KAAAmM,SAAAF,EAAAF,KAGAA,EAAA/L,KAAAmF,QAAA4G,OAGA,IAAA,IAAAvK,KAAAuK,EACAjK,OAAAsK,eAAA5G,KAAAuG,EAAAvK,IACAxB,KAAA+L,OAAAM,KAAAL,EAAAlD,IAAAiD,EAAAvK,IAAAoJ,UAIA,IAAA0B,EAAA,GACAjH,EAAAkH,KAAAvM,KAAA+L,OAAA,SAAAvK,EAAAoB,GACA0J,GAAAlH,EAAAD,QAAA2G,KAAA/K,EAAAsB,UAAAO,KAGA5C,KAAAwM,UAAAnH,EAAArF,KAAAmF,QAAAH,SAAAQ,KAAAxF,KAAAe,EAAAsB,YACAoK,KAAAH,GACA7G,SAAA1E,EAAA2E,WAEA1F,KAAAwM,UAAA1G,GAAA/E,EAAAsF,UAAA,SAAA,KAAA,SAAAF,GACA,IAAAvD,EAAAyC,EAAArF,MAAAiF,KAAA,SACAlE,EAAA6G,IAAAhF,GAEAuD,EAAAuG,iBACAvG,EAAAwG,oBAGA5L,EAAA8E,SAAAC,GAAA,uBAAA,SAAAK,EAAApF,EAAA6B,GACA,mBAAAA,EAAAgI,SACAhI,EAAAA,EAAAgK,MAAAhK,OAGA,IAAAiK,EAAAjK,EAAAgI,UACA,IAAAvF,EAAAyH,QAAAD,EAAAzH,EAAA2G,UACA3G,EAAA2G,OAAAtK,QAAA2D,EAAAD,QAAAsC,MACArC,EAAA2G,OAAAgB,QACA3H,EAAAoH,UACA5G,KAAA,MACAoH,GAAA,GACAnC,UAGAzF,EAAA2G,OAAAM,KAAAQ,GAEAzH,EAAAoH,UAAAS,OAAA7H,EAAAD,QAAA2G,KAAA/K,EAAAsB,UAAAO,IAEAwC,EAAAD,QAAA0G,cACAzG,EAAA+G,SAAAF,EAAA7G,EAAA2G,YAMAI,SAAA,SAAAnK,EAAA0G,GACA,IAAAwE,EAAAC,KAAAC,UAAA1E,GAEAmD,aAAA7J,GAAAkL,GAGAhB,SAAA,SAAAlK,GACA,IAAA0G,EAAAmD,aAAA7J,GAEA,OAAA0G,EAAAyE,KAAAE,MAAA3E,GAAAA,IAKAjE,GACAK,UACAE,SAAA,SAAA3C,GACA,MACA,cACAA,EACA,wBACAA,EACA,6CACAA,EACA,0CAKA6C,KAAA,SAAAnE,EAAAoE,GACA,IAAAC,EAAApF,KACAA,KAAAmF,QAAAE,EAAAC,OAAAtF,KAAA8E,SAAAK,GACAnF,KAAAsN,SAAAjI,EACArF,KAAAmF,QAAAH,SAAAQ,KAAAJ,EAAArE,EAAAsB,YACAoD,SAAA1E,EAAA2E,WACA1F,KAAAuN,SAAAvN,KAAAsN,SAAA1H,KACA,IAAA7E,EAAAsB,UAAA,yBAEArC,KAAAwN,UAAAxN,KAAAsN,SAAA1H,KACA,IAAA7E,EAAAsB,UAAA,0BAGAtB,EAAA8E,SAAAC,GAAA,2BAAA,WACAV,EAAAoI,UAAA1H,GAAA,QAAA,WAEA,OADA/E,EAAA6G,IAAAvC,EAAArF,MAAAiF,KAAA,WACA,MAIAlE,EAAA8E,SAAAC,GAAA,uBAAA,SAAAK,EAAApF,EAAA6B,GACAwC,EAAAqI,cAAA7K,GACAwC,EAAAsI,cAAA9K,KAEA7B,EAAA8E,SAAAC,GAAA,wBAAA,SAAAK,EAAApF,EAAA6B,GACAwC,EAAAqI,cAAA7K,MAIA6K,cAAA,SAAA7K,GACA5C,KAAAuN,SAAA5F,IAAA,kBAAA/E,EAAAgI,WAGA8C,cAAA,SAAA9K,GACA5C,KAAAwN,UAAA7F,IAAA,kBAAA/E,EAAAgI,UACA5K,KAAAwN,UAAAvI,KAAA,SACAwG,EAAA7I,EAAA8F,MAAA+C,EACAC,EAAA9I,EAAA8F,MAAAgD,EACAC,EAAA/I,EAAA8F,MAAAiD,EACA9D,EAAAjF,EAAA8F,MAAAb,MAeA8F,EAAA,SAAA5M,EAAAoE,GACAnF,KAAAe,IAAAA,EACAf,KAAAmF,QAAAA,EACAnF,KAAA4N,SACAC,OAAA9M,EAAAsB,UAAA,mBACAyL,OAAA/M,EAAAsB,UAAA,mBACA0L,OAAAhN,EAAAsB,UAAA,0BACA2L,MAAAjN,EAAAsB,UAAA,mBAEArC,KAAAiO,WAAA,EACAjO,KAAAkO,aAAA,EACAlO,KAAAmO,QAAA,KACAnO,KAAA0I,OAAA,EAAAvH,EAAAZ,SAAAP,KAAAmF,QAAAiJ,UACApO,KAAAqO,KAAAhJ,EAAAgC,UAEA,IAAAjC,EAAApF,KACAqF,EAAAC,OAAAF,GACAF,KAAA,WACAE,EAAAkJ,MAAAjJ,EAAAD,EAAAD,QAAAH,SAAAQ,KAAAJ,IAAAK,SACA1E,EAAA2E,WAGAN,EAAAmJ,UAAAnJ,EAAAkJ,MAAAE,OAAA,IAAAzN,EAAAsB,UAAA,aAEArC,KAAAyO,MAAAvJ,OACAlF,KAAAyE,QAAAS,OACAlF,KAAA0O,QAAAxJ,OACAlF,KAAA2O,MAAAzJ,OAEAlF,KAAA4O,SAGA,IAAAxJ,EAAAD,QAAA0J,YACA7O,KAAA0I,MAAAoG,YAAA/N,EAAAgO,QAAArG,SAEAtD,EAAAyI,SAEA7N,KAAAkO,aAAA,GAEAU,KAAA,WACA,IAAAvM,EAAAtB,EAAAsB,UAEA+C,EAAAmJ,UAAAzI,GAAA,SAAA,WACA,IAAAqI,EAAA/I,EAAAsD,MAAAsG,QAAA5J,EAAA+I,SAEAA,GACApN,EAAAkO,SAAA,SAAAd,EAAAvL,MAAAwC,EAAAsD,OAGA3H,EAAAgO,QAAArG,QAAAtD,EAAAsD,MAAAiC,YACA5J,EAAAmO,iBAYA9J,EAAAD,QAAA0J,YACAzJ,EAAAkJ,MAAAxI,GACA,QACA,IAAAzD,EAAA,mBACA,WAOA,OANA+C,EAAA6I,UACA7I,EAAA+J,UAEA/J,EAAAyI,UAGA,IAKAzI,EAAAkJ,MAAAxI,GACA,QACA,IAAAzD,EAAA,mBACA,WAUA,QARA,IAAA+C,EAAAD,QAAA0J,YACA1N,EAAAZ,QAAAuO,YAAA/N,EAAAqO,eAEAhK,EAAAiK,eAGAtO,EAAA4I,UAEA,KAIA0F,aAAA,WACAtO,EAAA6G,IAAA,SAAAc,GAMA,GAJA3H,EAAAuO,QADA,KAAA5G,EAKA,iBAAAA,GAEA,IAAAtD,EAAAD,QAAA0J,YACA1N,EAAAZ,QAAAuO,YAAApG,GAEAtD,EAAA6I,WACA7I,EAAA0D,IAAAJ,GACA3H,EAAA6B,MAAAwC,EAAAsD,MACAtD,EAAAmJ,UAAArK,QAAA,SAAAkB,EAAAsD,MAAAA,QAEAtD,EAAAyI,OAAAnF,IAGAtD,EAAA+J,UACApO,EAAA+H,IAAAJ,QAEA,CACA,IAAAyF,EAAA/I,EAAAsD,MAAAsG,QAAA5J,EAAA+I,SAEAA,IACAA,EAAAvL,MAAAkG,IAAAJ,GACA3H,EAAAkO,SAAA,SAAAd,EAAAvL,MAAAwC,EAAAsD,QAGAtD,EAAAmJ,UAAArK,QAAA,UACAlD,GAAAoE,EAAA+I,QACAoB,KAAApB,MAKApN,EAAAyO,OAAA,WACA,IAAArB,EAAA/I,EAAAsD,MAAAsG,QAAA5J,EAAA+I,SAEApN,EAAAkO,SAAA,QAAAd,EAAAvL,SAGA6M,WAAA,WACA1O,EAAA6G,IAAAvC,EAAAmB,MAAAzF,EAAA2O,KAAA3O,GACAA,EAAAyO,OAAA,WACAzO,EAAAkO,SAAA,QAAAlO,EAAA6B,SAIA6B,SACAS,KAAA,WACA,IAAAyK,EAAA3P,KAEAoF,EAAAkI,SAAAlI,EAAAmJ,UAAA3I,KACA,IAAA7E,EAAAsB,UAAA,qBAGA+C,EAAAmJ,UAAAzI,GAAA,uBAAA,WACA6J,EAAAC,YAGAA,OAAA,WACAxK,EAAAkI,SAAA3F,KACAkI,mBAAAzK,EAAAsD,MAAAoH,kBAAA,YAAA,KAEA1K,EAAAkI,SAAA3F,KACAkI,mBAAAzK,EAAAsD,MAAAoH,kBAAA,gBAIApB,SACA1I,MAAA,IACAd,KAAA,WACA,IAAA6K,EAAA/P,KAEAoF,EAAA4K,SAAA5K,EAAAmJ,UACA3I,KAAA,IAAA7E,EAAAsB,UAAA,qBACA+F,KAAA,WAAA,GAEAhD,EAAAmJ,UAAAzI,GAAA,MAAA,SAAAK,EAAAlB,GACA8K,EAAAE,IAAAhL,EAAAsK,QAGAnK,EAAAmJ,UAAAzI,GAAA,SAAA,SAAAK,EAAAlB,GACA8K,EAAAhC,OAAA9I,EAAAjE,MAGAoE,EAAAmJ,UAAAzI,GAAA,MAAA,SAAAK,EAAAlB,GACA8K,EAAAG,IAAAjL,EAAAjE,MAGAoE,EAAAmJ,UAAAzI,GAAA,SAAA,SAAAK,EAAAlB,GACAA,EAAAsK,MACAQ,EAAA3J,OAAAnB,EAAAsK,KAAAvO,GAAAiE,EAAAsK,KAAA3M,SAIAwC,EAAAmJ,UAAAzI,GAAA,QAAA,WACAiK,EAAAI,UAGA/K,EAAA4K,SAAAlK,GAAAV,EAAArE,IAAAsF,UAAA,aAAA,SAAAF,GAEA,GADAA,EAAAG,MAAA,IAAAH,EAAAG,MAAA,IAAAH,EAAAI,OAEA,OAAA,EAGA,IAAAY,EAAAoE,YACApF,EAAAa,MAAA5B,EAAA4K,SAAAtJ,SAAAO,MAAA7B,EAAAsJ,QAAA1I,MACA,IAGA,OADAZ,EAAA6K,IAAA,OAAA9I,IACA,IAIA,IAAAiJ,EAAApQ,KACAoF,EAAA4K,SAAAlK,GAAAV,EAAArE,IAAAsF,UAAA,aAAA,KAAA,SAAAF,GAEA,OADAA,EAAAG,MAAA,IAAAH,EAAAG,MAAA,IAAAH,EAAAI,UAIA6J,EAAA3J,UAAAzG,KAAAmG,IACA,KAGAf,EAAAiJ,KAAAvI,GAAAV,EAAArE,IAAAsF,UAAA,WAAA,SAAAF,GACA,GAAAf,EAAArE,IAAAyJ,QAAApF,EAAA4K,SAAAK,GAAA,IAAAjL,EAAAwI,QAAAI,OAAA,CACA,IAAAhM,EAAAmE,EAAAmK,SAAAnK,EAAAG,MACA,GAAA,KAAAtE,GAAA,IAAAA,EACA,QAAAoD,EAAAsD,MAAAjH,QAAA,KAIA2D,EAAA8K,IAAA9K,EAAA+I,UAEA,MAKA/I,EAAA4K,SACAlK,GAAAV,EAAArE,IAAAsF,UAAA,SAAA,WACAjB,EAAA4K,SAAAO,SAAAnL,EAAAwI,QAAAI,SAEAlI,GAAAV,EAAArE,IAAAsF,UAAA,QAAA,WACAjB,EAAA4K,SAAAQ,YAAApL,EAAAwI,QAAAI,SAGA5I,EAAA4K,SAAAlK,GAAAV,EAAArE,IAAAsF,UAAA,SAAA,KAAA,WACA,IAAArF,EAAAqE,EAAArF,MAAAiF,KAAA,MACAG,EAAA2I,OAAA/M,MAGAyP,UAAA,SAAAzP,GACA,OAAAoE,EAAA4K,SAAApK,KAAA,aAAA5E,EAAA,OAEAoF,OAAA,SAAApF,EAAA4B,GACA,IAAA8N,EAAA1Q,KAAAyQ,UAAAzP,GACA0P,EAAA9K,KAAA,QAAA+B,IAAA,mBAAA/E,EAAA+F,SACA+H,EAAA9K,KAAA,KAAA+B,IAAA,mBAAA/E,EAAA+F,UAEAsH,IAAA,SAAAV,GACAlK,EACA,gBACAkK,EAAAvO,GACA,iBACAJ,EAAA2O,EAAApI,UACA,YACA/B,EAAAwI,QAAAE,OACA,oCACAyB,EAAA3M,MAAA+F,QACA,wCACA4G,EAAA3M,MAAA+F,QACA,eACAlD,SAAAL,EAAA4K,WAEAG,MAAA,WACA/K,EAAA4K,SAAAvD,KAAA,KAEAyD,IAAA,SAAAlP,GACA,IAAA0P,EAAA1Q,KAAAyQ,UAAAzP,GACA2P,EAAAD,EAAAE,OACA,IAAAD,EAAAlP,SACAkP,EAAAD,EAAAG,QAGAzL,EAAA2I,OAAA4C,EAAA1L,KAAA,OACAyL,EAAA7F,UAEAkD,OAAA,SAAA/M,GACAoE,EAAA4K,SAAA1F,WAAAkG,YAAApL,EAAAwI,QAAAG,QAEA/N,KAAAyQ,UAAAzP,GACAuP,SAAAnL,EAAAwI,QAAAG,QAEA3I,EAAA4K,SAAAhC,SAGAvH,UAAA,SAAAqH,EAAA3H,GACA,IAAAiK,EAAApQ,KAEAgB,EAAAqE,EAAAyI,GAAA7I,KAAA,MACA6L,EAAAzL,EAAAyI,GAAA3G,WAAAF,KACA8J,EAAA5K,EAAAa,MACAgK,OAAA,EAuBA,OArBAhR,KAAAkH,UAAA,SAAAf,GACA6K,EAAA7K,EAAAa,OAAA+J,EACA,IAAA5J,GAAA2J,EAAAE,EAAAD,GAAA/Q,KAAAgG,MAEA,OADAoK,EAAAtJ,KAAAgH,EAAA3G,EAAAnG,IACA,GAGAhB,KAAAoH,QAAA,WAMA,OALA/B,EAAAgC,UAAAC,KACAJ,UAAAlH,KAAAkH,UACAE,QAAApH,KAAAoH,WAGA,GAGAhC,EAAAiJ,KAAAvI,IACAoB,UAAA7B,EAAAmB,MAAAxG,KAAAkH,UAAAlH,MACAoH,QAAA/B,EAAAmB,MAAAxG,KAAAoH,QAAApH,QAEAoF,EAAA2I,OAAA/M,IACA,GAEA8F,KAAA,SAAAgH,EAAA3G,EAAAnG,GACAoE,EAAArE,IAAAuO,SAAA,EACAnI,EAAAK,KAAAC,IAAA,EAAAD,KAAAE,IAAA,EAAAP,IACA9B,EAAAyI,GAAAnG,KACAV,KAAArG,EAAAuG,KAEAnG,IACAA,EAAAqE,EAAAyI,GAAA7I,KAAA,OAGAG,EAAAsD,MAAAsG,QAAAhO,GAAAiQ,YAAA9J,GAEA/B,EAAAmJ,UAAArK,QAAA,UACAlD,GAAAqE,EAAAyI,GAAA7I,KAAA,MACAkC,SAAAA,MAIAwH,OACAzJ,KAAA,WACA,IAAAgM,EAAAlR,KAEAoF,EAAA+L,OAAA/L,EAAAmJ,UAAA3I,KACA,IAAA7E,EAAAsB,UAAA,mBAEA+C,EAAAgM,SAAAhM,EAAA+L,OAAAvL,KAAA,KAEAR,EAAAmJ,UAAAzI,GAAA,SAAA,SAAAK,EAAAlB,QACA,IAAAA,EAAAwJ,OACAyC,EAAA/J,SAAAlC,EAAAwJ,SAIArJ,EAAA+L,OAAArL,GAAAV,EAAArE,IAAAsF,UAAA,aAAA,SAAAF,GAEA,OADAA,EAAAG,MAAA,IAAAH,EAAAG,MAAA,IAAAH,EAAAI,UAIA2K,EAAAzK,UAAAN,EAAAf,IACA,MAGAqB,UAAA,SAAAN,EAAAf,GACA,IAAAiM,EAAArR,KAEA0G,EAAAtB,EAAA+L,OAAAzK,SACA+E,EAAArG,EAAA+L,OAAAnL,QAAA,EACAe,EAAAL,EAAAO,KAAAwE,EACA9E,EAAAD,EAAAG,IAAA4E,EACA4C,EAAAjJ,EAAAiJ,KAEArO,KAAAyL,EAAAA,EAEAzL,KAAAsR,UAAA,SAAAnL,GACA,IAAAgD,EAAAhD,EAAAa,MAAAD,EACAqC,EAAAzC,EAAAR,EAAAS,MAEAO,EAAAkK,EAAAE,YAAApI,EAAAC,GACAqF,EAAA4C,EAAAG,SAAArK,EAAAgC,EAAAhC,EAAAiC,GACAhE,EAAArE,IAAAuO,SAAA,EACAlK,EAAAqM,SAAAhD,IAEAzO,KAAA0R,aAAA,WAKA,OAJArD,EAAA/G,KACAJ,UAAAlH,KAAAsR,UACAlK,QAAApH,KAAA0R,gBAEA,GAEArD,EAAAvI,IACAoB,UAAA7B,EAAAmB,MAAAxG,KAAAsR,UAAAtR,MACAoH,QAAA/B,EAAAmB,MAAAxG,KAAA0R,aAAA1R,QAGAA,KAAAsR,UAAAnL,IAEAoL,YAAA,SAAA1J,EAAA8D,GACA,IAAAF,EAAAzL,KAAAyL,EAGA,OACAtC,EAHAtB,EAAAL,KAAAmK,KAAA9J,EAAAA,EAAA8D,EAAAA,GAAAF,EAIArC,EAHAuC,EAAAnE,KAAAmK,KAAA9J,EAAAA,EAAA8D,EAAAA,GAAAF,IAMA+F,SAAA,SAAArI,EAAAC,GACA,IAAAwI,EAAApK,KAAAM,MAAAN,KAAAqK,KAAArK,KAAAsK,IAAA3I,EAAAC,KAAA,IAAA5B,KAAAuK,KACA,OAAA5I,EAAA,GAAAC,EAAA,EACA,IAAAwI,EAEAzI,EAAA,GAAAC,GAAA,EACAwI,EAAA,IAEAzI,GAAA,GAAAC,GAAA,EACA,IAAAwI,EAEAzI,GAAA,GAAAC,EAAA,EACAwI,OADA,GAIAhK,IAAA,SAAAc,GACAtD,EAAAsD,MAAA+F,MAAA/F,GACAtD,EAAAmJ,UAAArK,QAAA,UACAuK,MAAA/F,KAGAvB,SAAA,SAAAsH,GACA,IAAAhD,EAAAzL,KAAAyL,GAAArG,EAAA+L,OAAAnL,QAAA,EACAgM,EAAAhS,KAAAiS,WAAAxD,EAAAhD,GACArG,EAAAgM,SAAAzJ,KACAV,KAAA+K,EAAA7I,EACAtC,IAAAmL,EAAA5I,KAGA6I,WAAA,SAAAxD,EAAAhD,GAGA,OACAtC,EAAAsC,EAHAjE,KAAA0K,IAAAzD,EAAAjH,KAAAuK,GAAA,KAAAtG,EAIArC,EAAAqC,EAHAjE,KAAA2K,IAAA1D,EAAAjH,KAAAuK,GAAA,KAAAtG,KAOAgD,OACAvJ,KAAA,WACAE,EAAAgN,OAAAhN,EAAAmJ,UAAA3I,KACA,IAAA7E,EAAAsB,UAAA,mBAGA+C,EAAAgN,OACAtM,GAAAV,EAAArE,IAAAsF,UAAA,QAAA,WAEA,OADAjB,EAAAqM,SAAAzR,KAAA0I,QACA,IAEA5C,GAAAV,EAAArE,IAAAsF,UAAA,WAAA,SAAAF,GAEA,GAAA,MADAA,EAAAmK,SAAAnK,EAAAG,OAIA,OAFAlB,EAAArE,IAAAuO,SAAA,EACAjK,EAAArF,MAAAqS,QACA,IAIAjN,EAAAmJ,UAAAzI,GAAA,SAAA,SAAAK,EAAAlB,QACA,IAAAA,EAAAwJ,OACArJ,EAAAgN,OAAAtJ,IAAA7D,EAAAwJ,UAIA7G,IAAA,SAAAc,GACAtD,EAAAsD,MAAA+F,MAAA/F,GACAtD,EAAAmJ,UAAArK,QAAA,UACAuK,MAAA/F,QAMA1I,KAAAkF,QAGAyI,EAAAxL,WACAmQ,YAAA3E,EAEAE,OAAA,SAAAnF,IACA,IAAA1I,KAAAiO,YAGAjO,KAAAiO,WAAA,EACAjO,KAAAqP,eAEArP,KAAAuO,UAAAgC,SAAAvQ,KAAA4N,QAAAC,QACA7N,KAAA0O,QAAA1I,MAAAhG,KAAAgQ,SAAAhK,aAEA,IAAA0C,IACAA,EAAA1I,KAAAe,IAAAgO,QAAArG,OAIA1I,KAAAe,IAAAuO,QADA,KAAA5G,GAMAvH,EAAAZ,QAAAuO,YAAApG,IAAA1I,KAAAuS,MACAvS,KAAA0I,MAAA1I,KAAAuS,MAEAvS,KAAA8I,IAAAJ,GAEA1I,KAAAe,IAAA6B,MAAA5C,KAAA0I,MAEA1I,KAAAuO,UAAArK,QAAA,SAAAlE,KAAA0I,MAAAA,OAEA1I,KAAAe,IAAAyJ,QACAxK,KAAAe,IAAAoG,aAGA2B,IAAA,SAAA0J,GACA,GAAA,KAAAA,GAAAxS,KAAA0I,MAAAiC,aAAA6H,EAAA,CAOA,GAJAxS,KAAAmQ,QACAnQ,KAAA0I,MAAAI,IAAA0J,GACAxS,KAAA0I,MAAA+J,UAEAzS,KAAA0I,MAAAjH,OAAA,EAAA,CACA,IAAAiR,EAAAF,EAEAtR,EAAAX,QAAAuO,YAAA0D,KACAE,EAAA,iBAGA,IAAA1S,KAAA0I,MAAAjH,QACAzB,KAAA0I,MAAAuE,OAAAyF,EAAA,GAEA,IAAA1S,KAAA0I,MAAAjH,QACAzB,KAAA0I,MAAAuE,OAAAyF,EAAA,GAKA,IAAA,IADAnD,OAAA,EACA/N,EAAA,EAAAA,EAAAxB,KAAA0I,MAAAjH,OAAAD,KACA+N,EAAAvP,KAAA0I,MAAAkE,IAAApL,KAEAxB,KAAAuO,UAAArK,QAAA,OACAqL,KAAAA,IAKAvP,KAAA+N,OAAAwB,EAAAvO,MAEAmO,QAAA,YACA,IAAAnP,KAAAiO,YAGAjO,KAAAiO,WAAA,EACAjO,KAAAyP,aAEAzP,KAAAuO,UAAAiC,YAAAxQ,KAAA4N,QAAAC,QACA7N,KAAAuS,MAAAvS,KAAA0I,MACA1I,KAAAe,IAAA6B,MAAA5C,KAAAe,IAAA6B,MAAA+P,aAAA/P,MACA5C,KAAAe,IAAA6G,IAAA5H,KAAAe,IAAA6B,MAAA8F,OAEA1I,KAAAe,IAAAyJ,QACAxK,KAAAe,IAAAoG,aAGA4G,OAAA,SAAA/M,GACAhB,KAAAmO,UAAAnN,IACAhB,KAAAmO,QAAAnN,EACAhB,KAAA0I,MAAAkK,eAAA5R,GAEAhB,KAAAuO,UAAArK,QAAA,UACAlD,GAAAA,MAIAmP,MAAA,WACAnQ,KAAA0I,MAAAyH,QACAnQ,KAAAuO,UAAArK,QAAA,UAEA+L,IAAA,SAAArN,EAAAuE,GACA,IAAAoI,EAAAvP,KAAA0I,MAAAmK,OAAAjQ,EAAAuE,GAaA,OAZAnH,KAAAe,IAAAuO,SAAA,EACAtP,KAAA0I,MAAA+J,UAEAzS,KAAAuO,UAAArK,QAAA,OACAqL,KAAAA,IAGAvP,KAAA+N,OAAAwB,EAAAvO,IAEAhB,KAAAuO,UAAArK,QAAA,UACAqL,KAAAA,IAEAA,GAEAW,IAAA,SAAAlP,GACAhB,KAAA0I,MAAAjH,QAAA,IAGAzB,KAAA0I,MAAAoK,WAAA9R,GACAhB,KAAA0I,MAAA+J,UACAzS,KAAAuO,UAAArK,QAAA,OACAlD,GAAAA,IAGAhB,KAAAuO,UAAArK,QAAA,eAEAuN,SAAA,SAAA/I,GACA1I,KAAA0I,MAAA+F,MAAA/F,GACA1I,KAAAuO,UAAArK,QAAA,UACAuK,MAAA/F,MAKA,IAAA9D,GACAE,UACA+J,YAAA,EACAkE,WAAA,WACAlJ,WAAA,SACAuE,UACA4E,eAAA,EACAC,iBAAA,EACAC,YAAA,GACAC,mBAAA,EACAC,eAAA,EACAxQ,OACAC,OAAA,QAGAmC,SAAA,WACA,IAAA3C,EAAArC,KAAAe,IAAAsB,UACAgR,EAAA,eAAAhR,EAAA,sBAgBA,OAfArC,KAAAmF,QAAA0J,aACAwE,GACA,sBACAhR,EACA,qBACArC,KAAAmF,QAAA4N,WACA,SAEAM,GACA,sBACAhR,EACA,qBACArC,KAAAmF,QAAA0E,WACA,cAIA,eACAxH,EACA,0BACAA,EACA,iCACAA,EACA,6CACAA,EACA,+CACAA,EACA,4DAKA6C,KAAA,SAAAnE,EAAAoE,GACA,IAAAC,EAAApF,KAEAe,EAAA8E,SAAAC,GAAA,uBAAA,SAAAwN,EAAA7S,GACA,aAAAA,EAAA0E,QAAA1B,OAIA2B,EAAAN,SAAAsJ,SAAAxL,MAAA7B,EAAAoE,QAAAvC,MACAuC,EAAAE,EAAAC,QAAA,EAAAF,EAAAN,SAAAK,GAEApE,EAAA6D,SAAA,IAAA+I,EAAA5M,EAAAoE,QAMAoO,KACAC,GACAC,IACA5J,WAAA,SACAD,UAAA,UAIA5I,EAAA,EAOA0S,EAAA,WACA,SAAAA,EAAA3E,EAAA5J,GACA3E,EAAAR,KAAA0T,GAEA1T,KAAA+O,QAAAA,EACA/O,KAAA6F,UAAA,EAAA5E,EAAAV,SAAAwO,GAGA/O,KAAAwK,QAAA,EACAxK,KAAA2T,WAAA,EACA3T,KAAA4T,UAAA,EACA5T,KAAA6T,WAAA,EACA7T,KAAAoP,YAAApP,KAAA+O,QAAArG,MACA1I,KAAAsP,SAAA,EAEAxO,EAAAd,MAEAA,KAAAmF,QAAAlE,EAAAV,QAAA+E,QACA,KAEAlD,EACA+C,EACAnF,KAAA6F,SAAAZ,QAEAjF,KAAAqC,UAAArC,KAAAmF,QAAA9C,UAEArC,KAAA4N,SACAkG,KAAA9T,KAAAqC,UAAA,QACA0R,SAAA/T,KAAAqC,UAAA,YACA2R,MAAAhU,KAAAqC,UAAA,SACAE,KAAAvC,KAAAqC,UAAA,IAAArC,KAAAmF,QAAA5C,KACAmI,KAAA1K,KAAAqC,UAAA,QACA4R,KAAAjU,KAAAqC,UAAA,QACAI,UAAAzC,KAAAqC,UAAA,aACAuR,SAAA5T,KAAAqC,UAAA,YACAoB,KAAAzD,KAAAqC,UAAA,SAAArC,KAAAmF,QAAA1B,MAGAzD,KAAAmF,QAAA1C,WACAzC,KAAA6F,SAAA0K,SAAAvQ,KAAA4N,QAAAnL,WAGAzC,KAAAkU,WAAAlQ,EAAAhE,KAAAmF,QAAA1B,MACAzD,KAAAmU,YAAAlT,EAAAV,QAAA+E,QAAA,KAAAiO,GAEAvT,KAAAiP,SAAA,QACAjP,KAAAkF,cAGA9D,EACAsS,IAGA1R,IAAA,WACA0G,MAAA,SAAA0L,GACA,IACA,IAAAC,EAAAC,UAAA7S,OACA8S,EAAAC,MAAAH,EAAA,EAAAA,EAAA,EAAA,GACAI,EAAA,EACAA,EAAAJ,EACAI,IAEAF,EAAAE,EAAA,GAAAH,UAAAG,GAGA,IAAAxP,GAAAjF,MAAA0U,OAAAH,GAGAvU,KAAA6F,SAAA3B,QAAAyQ,kBAAAP,EAAAnP,GAMA,IAAA2P,EAAA,MAHAR,EAAAA,EAAAS,QAAA,WAAA,SAAAC,GACA,OAAAA,EAAAC,UAAA,EAAA,GAAAC,cAAAF,EAAAC,UAAA,MAIA,mBAAA/U,KAAAmF,QAAAyP,IACA5U,KAAAmF,QAAAyP,GAAAlL,MAAA1J,KAAAuU,MAKAvS,IAAA,YACA0G,MAAA,SAAAuM,GACA,GAAA,iBAAAA,GAAA,KAAAA,EACA,MAAA,IAAAjV,KAAAmF,QAAA9C,UAKA,IAAA,IADAZ,GAFAwT,EAAAA,EAAAC,MAAA,MAEAzT,OACAD,EAAA,EAAAA,EAAAC,EAAAD,IACAyT,EAAAzT,GAAAyT,EAAAzT,GAAA,IAAAxB,KAAAmF,QAAA9C,UAEA,OAAA4S,EAAAE,KAAA,QAIAnT,IAAA,OACA0G,MAAA,WACA1I,KAAA4C,OAAA,EAAA1B,EAAAX,SACAP,KAAA+O,QAAArG,MACA1I,KAAAmF,QAAAvC,OAGA5C,KAAAoV,UAEApV,KAAAmF,QAAA5C,OACAvC,KAAA0F,UAAA6K,SAAAvQ,KAAA4N,QAAArL,MACAvC,KAAA6F,SAAAwP,SAAA9E,SAAAvQ,KAAA4N,QAAArL,OAGAvC,KAAAmF,QAAA7C,UACAtC,KAAA6F,SAAAyP,KAAA,YAAA,GAGAtV,KAAAuV,aAEAvV,KAAA6T,WAAA,EACA7T,KAAAiP,SAAA,YAIAjN,IAAA,UACA0G,MAAA,WACA,IAAA8M,EAAAxV,KAEAA,KAAA0F,WAAA,EAAAzE,EAAAV,SACA,eACAP,KAAA4N,QAAAmG,SACA,gBACA/T,KAAAmF,QAAA1B,KACA,YAEAzD,KAAA6F,SACAiO,KAAA,eAAA9T,KAAA4N,QAAAkG,KAAA,YACAvD,SAAAvQ,KAAA4N,QAAAoG,OAEAhU,KAAAsO,MAAAtO,KAAA6F,SAAAwP,SACArV,KAAAyV,OAAA,EAAAxU,EAAAV,SAAA,QAEAP,KAAA0F,UAAAT,KA5JA,gBA4JAjF,MAEA,IAAA0V,OAAA,EACAzU,EAAAV,QAAAgM,KAAAvM,KAAAkU,WAAA,SAAAlS,EAAAmD,IACA,IAAAA,IACAA,WAEAkE,IAAAmM,EAAArQ,QAAAnD,KACAmD,EAAAlE,EAAAV,QAAA+E,QACA,KAEAH,EACAqQ,EAAArQ,QAAAnD,KAGAF,OAAAsK,eAAA5G,KAAAgQ,EAAArB,YAAAnS,KACA0T,EAAAF,EAAArB,YAAAnS,IACAkD,KAAAsQ,EAAArQ,KAIAnF,KAAAiP,SAAA,aAIAjN,IAAA,aACA0G,MAAA,WACA,IAAAiN,EAAA3V,KAEAA,KAAA6F,SAAAC,GAAA9F,KAAAqG,UAAA,SAAA,WAIA,OAHAsP,EAAAnL,QACAmL,EAAAjL,QAEA,IAGA1K,KAAA6F,SAAAC,GAAA9F,KAAAqG,UAAA,WAAA,SAAAF,GACA,IAAAA,EAAAmK,QACAqF,EAAAlL,QACA,KAAAtE,EAAAmK,UACAqF,EAAA7M,IAAA6M,EAAA5G,QAAArG,OACAiN,EAAAlL,WAIAzK,KAAA6F,SAAAC,GAAA9F,KAAAqG,UAAA,SAAA,WACAsP,EAAA/S,MAAAkM,YAAA6G,EAAA5G,QAAArG,QACAiN,EAAA7M,IAAA6M,EAAA5G,QAAArG,YAMA1G,IAAA,UACA0G,MAAA,SAAAa,GACA,IAAAA,EAGA,OAAAvJ,KAAA4C,MAAA0B,QAFAtE,KAAA4C,MAAA0B,MAAAiF,MAOAvH,IAAA,WACA0G,MAAA,WACA,IAAAkN,GAAA5V,KAAA6F,SAAAwK,GAAA,YACA3J,EAAAkP,EACA5V,KAAAoK,SAAA1D,SACA1G,KAAA6F,SAAAa,SACAX,EAAA6P,EACA5V,KAAAoK,SAAAyL,cACA7V,KAAA6F,SAAAgQ,cACA7P,EAAA4P,EACA5V,KAAAoK,SAAA0L,aACA9V,KAAA6F,SAAAiQ,aAAA9V,KAAAoK,SAAA0L,aACAC,EAAA/V,KAAA0F,UAAAoQ,YAAA,GACAE,EAAAhW,KAAA0F,UAAAmQ,aAAA,GACAhP,OAAA,EACAI,OAAA,EAOAJ,EAJAmP,EAAAtP,EAAAG,KACA,EAAA5F,EAAAV,SAAAqL,QAAA7F,UACA,EAAA9E,EAAAV,SAAAqL,QAAAqK,YAEAvP,EAAAG,IAAAmP,EAEAtP,EAAAG,IAAAd,EAQAkB,EAJA8O,EAAArP,EAAAO,MACA,EAAAhG,EAAAV,SAAAqL,QAAA5F,SACA,EAAA/E,EAAAV,SAAAqL,QAAAsK,aAEAxP,EAAAO,KAAA8O,EAAA/P,EAEAU,EAAAO,KAGAjH,KAAA0F,UAAAiC,KACAR,SAAA,WACAN,IAAAA,EACAI,KAAAA,OAKAjF,IAAA,OACA0G,MAAA,WACA1I,KAAA4T,WAGA5T,KAAAoP,YAAApP,KAAA+O,QAAArG,MAEA1I,KAAA0F,UAAA,KAAA1F,KAAAyV,MAAAnL,WAAA6L,OAAA,IACAnW,KAAA0F,UAAA+C,SAAAhD,SAAAzF,KAAAyV,OAGAzV,KAAAoW,OAAA,EAAAnV,EAAAV,SAAA,IAAAP,KAAA4N,QAAAqG,MACA,IAAAjU,KAAAoW,MAAA3U,QACAzB,KAAAqW,aAIArW,KAAA0F,UAAAkL,OAAA,KAAA5Q,KAAAoW,MAAA,IACApW,KAAA0F,UAAA4Q,OAAAtW,KAAAoW,QAGA,EAAAnV,EAAAV,SAAA,2BAAAgW,WAAA,MACAvW,KAAA0F,UAAA0C,KAAA,KAAA,0BAGApI,KAAAoW,MAAAI,OAEAxW,KAAAmH,YAEA,EAAAlG,EAAAV,SAAAqL,QAAA9F,GACA9F,KAAAqG,UAAA,UACApF,EAAAV,QAAAiG,MAAAxG,KAAAmH,SAAAnH,OAGAA,KAAA0F,UAAA6K,SAAAvQ,KAAA4N,QAAAlD,MAEA1K,KAAAwK,QAAA,EAEAxK,KAAA2T,YACA3T,KAAA2T,WAAA,EACA3T,KAAAiP,SAAA,cAEAjP,KAAAwP,SACAxP,KAAAiP,SAAA,YAIAjN,IAAA,aACA0G,MAAA,WACA1I,KAAAoW,OAAA,EAAAnV,EAAAV,SAAA8G,SAAAoP,cAAA,QACAzW,KAAAoW,MAAAhO,KAAA,QAAApI,KAAA4N,QAAAqG,MACAjU,KAAAoW,MAAAM,OACA1W,KAAAoW,MAAA3Q,SAAAzF,KAAAyV,OAEAzV,KAAAoW,MAAAtQ,GACA9F,KAAAqG,UAAA,8BACA,SAAAF,GACA,IAAAT,GAAA,EAAAzE,EAAAV,SACA,2BAEA6P,OAAA,EACA1K,EAAAjE,OAAA,KACA2O,EAAA1K,EAAAT,KAtUA,kBAuUAuF,SACA4F,EAAAjL,QAAAzC,eACA0N,EAAA1G,QAEA0G,EAAAzG,UAIAxD,EAAAuG,iBACAvG,EAAAwG,wBAOA3K,IAAA,QACA0G,MAAA,WACA1I,KAAAwK,QAAA,EACAxK,KAAA6F,SAAAwM,OACArS,KAAAoW,MAAAM,OAEA1W,KAAA0F,UAAA8K,YAAAxQ,KAAA4N,QAAAlD,OAEA,EAAAzJ,EAAAV,SAAAqL,QAAAtE,IAAAtH,KAAAqG,UAAA,WAEArG,KAAAiP,SAAA,suCAjWA,mYAgBA", "file": "jquery-asColorPicker.min.js", "sourcesContent": ["/**\n* asColorPicker v0.4.4\n* https://github.com/amazingSurge/jquery-asColorPicker\n*\n* Copyright (c) amazingSurge\n* Released under the LGPL-3.0 license\n*/\nimport $$1 from 'jquery';\nimport AsColor from 'jquery-asColor';\nimport AsGradient from 'jquery-asGradient';\n\nvar DEFAULTS = {\n  namespace: 'asColorPicker',\n  readonly: false,\n  skin: null,\n  lang: 'en',\n  hideInput: false,\n  hideFireChange: true,\n  keyboard: false,\n  color: {\n    format: false,\n    alphaConvert: { // or false will disable convert\n      'RGB': 'RGBA',\n      'HSL': 'HSLA',\n      'HEX': 'RGBA',\n      'NAMESPACE': 'RGBA',\n    },\n    shortenHex: false,\n    hexUseName: false,\n    reduceAlpha: true,\n    nameDegradation: 'HEX',\n    invalidValue: '',\n    zeroAlphaAsTransparent: true\n  },\n  mode: 'simple',\n  onInit: null,\n  onReady: null,\n  onChange: null,\n  onClose: null,\n  onOpen: null,\n  onApply: null\n};\n\nvar MODES = {\n  'simple': {\n    trigger: true,\n    clear: true,\n    saturation: true,\n    hue: true,\n    alpha: true\n  },\n  'palettes': {\n    trigger: true,\n    clear: true,\n    palettes: true\n  },\n  'complex': {\n    trigger: true,\n    clear: true,\n    preview: true,\n    palettes: true,\n    saturation: true,\n    hue: true,\n    alpha: true,\n    hex: true,\n    buttons: true\n  },\n  'gradient': {\n    trigger: true,\n    clear: true,\n    preview: true,\n    palettes: true,\n    saturation: true,\n    hue: true,\n    alpha: true,\n    hex: true,\n    gradient: true\n  }\n};\n\n// alpha\nvar alpha = {\n  size: 150,\n\n  defaults: {\n    direction: 'vertical', // horizontal\n    template(namespace) {\n      return `<div class=\"${namespace}-alpha ${namespace}-alpha-${this.direction}\"><i></i></div>`;\n    }\n  },\n\n  data: {},\n\n  init: function(api, options) {\n    const that = this;\n\n    this.options = $.extend(this.defaults, options);\n    that.direction = this.options.direction;\n    this.api = api;\n\n    this.$alpha = $(this.options.template.call(that, api.namespace)).appendTo(api.$dropdown);\n    this.$handle = this.$alpha.find('i');\n\n    api.$element.on('asColorPicker::firstOpen', () => {\n      // init variable\n      if (that.direction === 'vertical') {\n        that.size = that.$alpha.height();\n      } else {\n        that.size = that.$alpha.width();\n      }\n      that.step = that.size / 360;\n\n      // bind events\n      that.bindEvents();\n      that.keyboard();\n    });\n\n    api.$element.on('asColorPicker::update asColorPicker::setup', (e, api, color) => {\n      that.update(color);\n    });\n  },\n\n  bindEvents: function() {\n    const that = this;\n    this.$alpha.on(this.api.eventName('mousedown'), e => {\n      const rightclick = (e.which) ? (e.which === 3) : (e.button === 2);\n      if (rightclick) {\n        return false;\n      }\n      $.proxy(that.mousedown, that)(e);\n    });\n  },\n\n  mousedown: function(e) {\n    const offset = this.$alpha.offset();\n    if (this.direction === 'vertical') {\n      this.data.startY = e.pageY;\n      this.data.top = e.pageY - offset.top;\n      this.move(this.data.top);\n    } else {\n      this.data.startX = e.pageX;\n      this.data.left = e.pageX - offset.left;\n      this.move(this.data.left);\n    }\n\n    this.mousemove = function(e) {\n      let position;\n      if (this.direction === 'vertical') {\n        position = this.data.top + (e.pageY || this.data.startY) - this.data.startY;\n      } else {\n        position = this.data.left + (e.pageX || this.data.startX) - this.data.startX;\n      }\n\n      this.move(position);\n      return false;\n    };\n\n    this.mouseup = function() {\n      $(document).off({\n        mousemove: this.mousemove,\n        mouseup: this.mouseup\n      });\n      if (this.direction === 'vertical') {\n        this.data.top = this.data.cach;\n      } else {\n        this.data.left = this.data.cach;\n      }\n\n      return false;\n    };\n\n    $(document).on({\n      mousemove: $.proxy(this.mousemove, this),\n      mouseup: $.proxy(this.mouseup, this)\n    });\n    return false;\n  },\n\n  move: function(position, alpha, update) {\n    position = Math.max(0, Math.min(this.size, position));\n    this.data.cach = position;\n    if (typeof alpha === 'undefined') {\n      alpha = 1 - (position / this.size);\n    }\n    alpha = Math.max(0, Math.min(1, alpha));\n    if (this.direction === 'vertical') {\n      this.$handle.css({\n        top: position\n      });\n    } else {\n      this.$handle.css({\n        left: position\n      });\n    }\n\n    if (update !== false) {\n      this.api.set({\n        a: Math.round(alpha * 100) / 100\n      });\n    }\n  },\n\n  moveLeft: function() {\n    const step = this.step;\n    const data = this.data;\n    data.left = Math.max(0, Math.min(this.width, data.left - step));\n    this.move(data.left);\n  },\n\n  moveRight: function() {\n    const step = this.step;\n    const data = this.data;\n    data.left = Math.max(0, Math.min(this.width, data.left + step));\n    this.move(data.left);\n  },\n\n  moveUp: function() {\n    const step = this.step;\n    const data = this.data;\n    data.top = Math.max(0, Math.min(this.width, data.top - step));\n    this.move(data.top);\n  },\n\n  moveDown: function() {\n    const step = this.step;\n    const data = this.data;\n    data.top = Math.max(0, Math.min(this.width, data.top + step));\n    this.move(data.top);\n  },\n\n  keyboard: function() {\n    let keyboard;\n    const that = this;\n    if (this.api._keyboard) {\n      keyboard = $.extend(true, {}, this.api._keyboard);\n    } else {\n      return false;\n    }\n\n    this.$alpha.attr('tabindex', '0').on('focus', function() {\n      if (this.direction === 'vertical') {\n        keyboard.attach({\n          up() {\n            that.moveUp();\n          },\n          down() {\n            that.moveDown();\n          }\n        });\n      } else {\n        keyboard.attach({\n          left() {\n            that.moveLeft();\n          },\n          right() {\n            that.moveRight();\n          }\n        });\n      }\n      return false;\n    }).on('blur', () => {\n      keyboard.detach();\n    });\n  },\n\n  update: function(color) {\n    const position = this.size * (1 - color.value.a);\n    this.$alpha.css('backgroundColor', color.toHEX());\n\n    this.move(position, color.value.a, false);\n  },\n\n  destroy: function() {\n    $(document).off({\n      mousemove: this.mousemove,\n      mouseup: this.mouseup\n    });\n  }\n};\n\n// hex\nvar hex = {\n  init: function(api) {\n    const template = `<input type=\"text\" class=\"${api.namespace}-hex\" />`;\n    this.$hex = $(template).appendTo(api.$dropdown);\n\n    this.$hex.on('change', function() {\n      api.set(this.value);\n    });\n\n    const that = this;\n    api.$element.on('asColorPicker::update asColorPicker::setup', (e, api, color) => {\n      that.update(color);\n    });\n  },\n\n  update: function(color) {\n    this.$hex.val(color.toHEX());\n  }\n};\n\n// hue\nvar hue = {\n  size: 150,\n\n  defaults: {\n    direction: 'vertical', // horizontal\n    template() {\n      const namespace = this.api.namespace;\n      return `<div class=\"${namespace}-hue ${namespace}-hue-${this.direction}\"><i></i></div>`;\n    }\n  },\n\n  data: {},\n\n  init: function(api, options) {\n    const that = this;\n\n    this.options = $.extend(this.defaults, options);\n    this.direction = this.options.direction;\n    this.api = api;\n\n    this.$hue = $(this.options.template.call(that)).appendTo(api.$dropdown);\n    this.$handle = this.$hue.find('i');\n\n    api.$element.on('asColorPicker::firstOpen', () => {\n      // init variable\n      if (that.direction === 'vertical') {\n        that.size = that.$hue.height();\n      } else {\n        that.size = that.$hue.width();\n      }\n      that.step = that.size / 360;\n\n      // bind events\n      that.bindEvents(api);\n      that.keyboard(api);\n    });\n\n    api.$element.on('asColorPicker::update asColorPicker::setup', (e, api, color) => {\n      that.update(color);\n    });\n  },\n\n  bindEvents: function() {\n    const that = this;\n    this.$hue.on(this.api.eventName('mousedown'), e => {\n      const rightclick = (e.which) ? (e.which === 3) : (e.button === 2);\n      if (rightclick) {\n        return false;\n      }\n      $.proxy(that.mousedown, that)(e);\n    });\n  },\n\n  mousedown: function(e) {\n    const offset = this.$hue.offset();\n    if (this.direction === 'vertical') {\n      this.data.startY = e.pageY;\n      this.data.top = e.pageY - offset.top;\n      this.move(this.data.top);\n    } else {\n      this.data.startX = e.pageX;\n      this.data.left = e.pageX - offset.left;\n      this.move(this.data.left);\n    }\n\n    this.mousemove = function(e) {\n      let position;\n      if (this.direction === 'vertical') {\n        position = this.data.top + (e.pageY || this.data.startY) - this.data.startY;\n      } else {\n        position = this.data.left + (e.pageX || this.data.startX) - this.data.startX;\n      }\n\n      this.move(position);\n      return false;\n    };\n\n    this.mouseup = function() {\n      $(document).off({\n        mousemove: this.mousemove,\n        mouseup: this.mouseup\n      });\n      if (this.direction === 'vertical') {\n        this.data.top = this.data.cach;\n      } else {\n        this.data.left = this.data.cach;\n      }\n\n      return false;\n    };\n\n    $(document).on({\n      mousemove: $.proxy(this.mousemove, this),\n      mouseup: $.proxy(this.mouseup, this)\n    });\n\n    return false;\n  },\n\n  move: function(position, hub, update) {\n    position = Math.max(0, Math.min(this.size, position));\n    this.data.cach = position;\n    if (typeof hub === 'undefined') {\n      hub = (1 - position / this.size) * 360;\n    }\n    hub = Math.max(0, Math.min(360, hub));\n    if (this.direction === 'vertical') {\n      this.$handle.css({\n        top: position\n      });\n    } else {\n      this.$handle.css({\n        left: position\n      });\n    }\n    if (update !== false) {\n      this.api.set({\n        h: hub\n      });\n    }\n  },\n\n  moveLeft: function() {\n    const step = this.step;\n    const data = this.data;\n    data.left = Math.max(0, Math.min(this.width, data.left - step));\n    this.move(data.left);\n  },\n\n  moveRight: function() {\n    const step = this.step;\n    const data = this.data;\n    data.left = Math.max(0, Math.min(this.width, data.left + step));\n    this.move(data.left);\n  },\n\n  moveUp: function() {\n    const step = this.step;\n    const data = this.data;\n    data.top = Math.max(0, Math.min(this.width, data.top - step));\n    this.move(data.top);\n  },\n\n  moveDown: function() {\n    const step = this.step;\n    const data = this.data;\n    data.top = Math.max(0, Math.min(this.width, data.top + step));\n    this.move(data.top);\n  },\n\n  keyboard: function() {\n    let keyboard;\n    const that = this;\n    if (this.api._keyboard) {\n      keyboard = $.extend(true, {}, this.api._keyboard);\n    } else {\n      return false;\n    }\n\n    this.$hue.attr('tabindex', '0').on('focus', function() {\n      if (this.direction === 'vertical') {\n        keyboard.attach({\n          up() {\n            that.moveUp();\n          },\n          down() {\n            that.moveDown();\n          }\n        });\n      } else {\n        keyboard.attach({\n          left() {\n            that.moveLeft();\n          },\n          right() {\n            that.moveRight();\n          }\n        });\n      }\n      return false;\n    }).on('blur', () => {\n      keyboard.detach();\n    });\n  },\n\n  update: function(color) {\n    const position = (color.value.h === 0) ? 0 : this.size * (1 - color.value.h / 360);\n    this.move(position, color.value.h, false);\n  },\n\n  destroy: function() {\n    $(document).off({\n      mousemove: this.mousemove,\n      mouseup: this.mouseup\n    });\n  }\n};\n\n// saturation\nvar saturation = {\n  defaults: {\n    template(namespace) {\n      return `<div class=\"${namespace}-saturation\"><i><b></b></i></div>`;\n    }\n  },\n\n  width: 0,\n  height: 0,\n  size: 6,\n  data: {},\n\n  init: function(api, options) {\n    const that = this;\n    this.options = $.extend(this.defaults, options);\n    this.api = api;\n\n    //build element and add component to picker\n    this.$saturation = $(this.options.template.call(that, api.namespace)).appendTo(api.$dropdown);\n    this.$handle = this.$saturation.find('i');\n\n    api.$element.on('asColorPicker::firstOpen', () => {\n      // init variable\n      that.width = that.$saturation.width();\n      that.height = that.$saturation.height();\n      that.step = {\n        left: that.width / 20,\n        top: that.height / 20\n      };\n      that.size = that.$handle.width() / 2;\n\n      // bind events\n      that.bindEvents();\n      that.keyboard(api);\n    });\n\n    api.$element.on('asColorPicker::update asColorPicker::setup', (e, api, color) => {\n      that.update(color);\n    });\n  },\n\n  bindEvents: function() {\n    const that = this;\n\n    this.$saturation.on(this.api.eventName('mousedown'), e => {\n      const rightclick = (e.which) ? (e.which === 3) : (e.button === 2);\n      if (rightclick) {\n        return false;\n      }\n      that.mousedown(e);\n    });\n  },\n\n  mousedown: function(e) {\n    const offset = this.$saturation.offset();\n\n    this.data.startY = e.pageY;\n    this.data.startX = e.pageX;\n    this.data.top = e.pageY - offset.top;\n    this.data.left = e.pageX - offset.left;\n    this.data.cach = {};\n\n    this.move(this.data.left, this.data.top);\n\n    this.mousemove = function(e) {\n      const x = this.data.left + (e.pageX || this.data.startX) - this.data.startX;\n      const y = this.data.top + (e.pageY || this.data.startY) - this.data.startY;\n      this.move(x, y);\n      return false;\n    };\n\n    this.mouseup = function() {\n      $(document).off({\n        mousemove: this.mousemove,\n        mouseup: this.mouseup\n      });\n      this.data.left = this.data.cach.left;\n      this.data.top = this.data.cach.top;\n\n      return false;\n    };\n\n    $(document).on({\n      mousemove: $.proxy(this.mousemove, this),\n      mouseup: $.proxy(this.mouseup, this)\n    });\n\n    return false;\n  },\n\n  move: function(x, y, update) {\n    y = Math.max(0, Math.min(this.height, y));\n    x = Math.max(0, Math.min(this.width, x));\n\n    if (this.data.cach === undefined) {\n      this.data.cach = {};\n    }\n    this.data.cach.left = x;\n    this.data.cach.top = y;\n\n    this.$handle.css({\n      top: y - this.size,\n      left: x - this.size\n    });\n\n    if (update !== false) {\n      this.api.set({\n        s: x / this.width,\n        v: 1 - (y / this.height)\n      });\n    }\n  },\n\n  update: function(color) {\n    if (color.value.h === undefined) {\n      color.value.h = 0;\n    }\n    this.$saturation.css('backgroundColor', AsColor.HSLtoHEX({\n      h: color.value.h,\n      s: 1,\n      l: 0.5\n    }));\n\n    const x = color.value.s * this.width;\n    const y = (1 - color.value.v) * this.height;\n\n    this.move(x, y, false);\n  },\n\n  moveLeft: function() {\n    const step = this.step.left;\n    const data = this.data;\n    data.left = Math.max(0, Math.min(this.width, data.left - step));\n    this.move(data.left, data.top);\n  },\n\n  moveRight: function() {\n    const step = this.step.left;\n    const data = this.data;\n    data.left = Math.max(0, Math.min(this.width, data.left + step));\n    this.move(data.left, data.top);\n  },\n\n  moveUp: function() {\n    const step = this.step.top;\n    const data = this.data;\n    data.top = Math.max(0, Math.min(this.width, data.top - step));\n    this.move(data.left, data.top);\n  },\n\n  moveDown: function() {\n    const step = this.step.top;\n    const data = this.data;\n    data.top = Math.max(0, Math.min(this.width, data.top + step));\n    this.move(data.left, data.top);\n  },\n\n  keyboard: function() {\n    let keyboard;\n    const that = this;\n    if (this.api._keyboard) {\n      keyboard = $.extend(true, {}, this.api._keyboard);\n    } else {\n      return false;\n    }\n\n    this.$saturation.attr('tabindex', '0').on('focus', () => {\n      keyboard.attach({\n        left() {\n          that.moveLeft();\n        },\n        right() {\n          that.moveRight();\n        },\n        up() {\n          that.moveUp();\n        },\n        down() {\n          that.moveDown();\n        }\n      });\n      return false;\n    }).on('blur', () => {\n      keyboard.detach();\n    });\n  },\n\n  destroy: function() {\n    $(document).off({\n      mousemove: this.mousemove,\n      mouseup: this.mouseup\n    });\n  }\n};\n\n// buttons\nvar buttons = {\n  defaults: {\n    apply: false,\n    cancel: true,\n    applyText: null,\n    cancelText: null,\n    template(namespace) {\n      return `<div class=\"${namespace}-buttons\"></div>`;\n    },\n    applyTemplate(namespace) {\n      return `<a href=\"#\" alt=\"${this.options.applyText}\" class=\"${namespace}-buttons-apply\">${this.options.applyText}</a>`;\n    },\n    cancelTemplate(namespace) {\n      return `<a href=\"#\" alt=\"${this.options.cancelText}\" class=\"${namespace}-buttons-apply\">${this.options.cancelText}</a>`;\n    }\n  },\n\n  init: function(api, options) {\n    const that = this;\n\n    this.options = $.extend(this.defaults, {\n      applyText: api.getString('applyText', 'apply'),\n      cancelText: api.getString('cancelText', 'cancel')\n    }, options);\n    this.$buttons = $(this.options.template.call(this, api.namespace)).appendTo(api.$dropdown);\n\n    api.$element.on('asColorPicker::firstOpen', () => {\n      if (that.options.apply) {\n        that.$apply = $(that.options.applyTemplate.call(that, api.namespace)).appendTo(that.$buttons).on('click', () => {\n          api.apply();\n          return false;\n        });\n      }\n\n      if (that.options.cancel) {\n        that.$cancel = $(that.options.cancelTemplate.call(that, api.namespace)).appendTo(that.$buttons).on('click', () => {\n          api.cancel();\n          return false;\n        });\n      }\n    });\n  }\n};\n\n// trigger\nvar trigger = {\n  defaults: {\n    template(namespace) {\n      return `<div class=\"${namespace}-trigger\"><span></span></div>`;\n    }\n  },\n\n  init: function(api, options) {\n    this.options = $.extend(this.defaults, options);\n    api.$trigger = $(this.options.template.call(this, api.namespace));\n    this.$triggerInner = api.$trigger.children('span');\n\n    api.$trigger.insertAfter(api.$element);\n    api.$trigger.on('click', () => {\n      if (!api.opened) {\n        api.open();\n      } else {\n        api.close();\n      }\n      return false;\n    });\n    const that = this;\n    api.$element.on('asColorPicker::update', (e, api, color, gradient) => {\n      if (typeof gradient === 'undefined') {\n        gradient = false;\n      }\n      that.update(color, gradient);\n    });\n\n    this.update(api.color);\n  },\n\n  update: function(color, gradient) {\n    if (gradient) {\n      this.$triggerInner.css('background', gradient.toString(true));\n    } else {\n      this.$triggerInner.css('background', color.toRGBA());\n    }\n  },\n\n  destroy: function(api) {\n    api.$trigger.remove();\n  }\n};\n\n// clear\nvar clear = {\n  defaults: {\n    template(namespace) {\n      return `<a href=\"#\" class=\"${namespace}-clear\"></a>`;\n    }\n  },\n\n  init: function(api, options) {\n    if (api.options.hideInput) {\n      return;\n    }\n    this.options = $.extend(this.defaults, options);\n    this.$clear = $(this.options.template.call(this, api.namespace)).insertAfter(api.$element);\n\n    this.$clear.on('click', () => {\n      api.clear();\n      return false;\n    });\n  }\n};\n\n// info\nvar info = {\n  color: ['white', 'black', 'transparent'],\n\n  init: function(api) {\n    const template = `<ul class=\"${api.namespace}-info\"><li><label>R:<input type=\"text\" data-type=\"r\"/></label></li><li><label>G:<input type=\"text\" data-type=\"g\"/></label></li><li><label>B:<input type=\"text\" data-type=\"b\"/></label></li><li><label>A:<input type=\"text\" data-type=\"a\"/></label></li></ul>`;\n    this.$info = $(template).appendTo(api.$dropdown);\n    this.$r = this.$info.find('[data-type=\"r\"]');\n    this.$g = this.$info.find('[data-type=\"g\"]');\n    this.$b = this.$info.find('[data-type=\"b\"]');\n    this.$a = this.$info.find('[data-type=\"a\"]');\n\n    this.$info.on(api.eventName('keyup update change'), 'input', function(e) {\n      let val;\n      const type = $(e.target).data('type');\n      switch (type) {\n        case 'r':\n        case 'g':\n        case 'b':\n          val = parseInt(this.value, 10);\n          if (val > 255) {\n            val = 255;\n          } else if (val < 0) {\n            val = 0;\n          }\n          break;\n        case 'a':\n          val = parseFloat(this.value, 10);\n          if (val > 1) {\n            val = 1;\n          } else if (val < 0) {\n            val = 0;\n          }\n          break;\n        default:\n          break;\n      }\n      if (isNaN(val)) {\n        val = 0;\n      }\n      const color = {};\n      color[type] = val;\n      api.set(color);\n    });\n\n    const that = this;\n    api.$element.on('asColorPicker::update asColorPicker::setup', (e, color) => {\n      that.update(color);\n    });\n  },\n\n  update: function(color) {\n    this.$r.val(color.value.r);\n    this.$g.val(color.value.g);\n    this.$b.val(color.value.b);\n    this.$a.val(color.value.a);\n  }\n};\n\n// palettes\nfunction noop() {\n  return;\n}\nif (!window.localStorage) {\n  window.localStorage = noop;\n}\n\nvar palettes = {\n  defaults: {\n    template(namespace) {\n      return `<ul class=\"${namespace}-palettes\"></ul>`;\n    },\n    item(namespace, color) {\n      return `<li data-color=\"${color}\"><span style=\"background-color:${color}\" /></li>`;\n    },\n    colors: ['white', 'black', 'red', 'blue', 'yellow'],\n    max: 10,\n    localStorage: true\n  },\n\n  init: function(api, options) {\n    const that = this;\n    let colors;\n    const asColor = AsColor();\n\n    this.options = $.extend(true, {}, this.defaults, options);\n    this.colors = [];\n    let localKey;\n\n    if (this.options.localStorage) {\n      localKey = `${api.namespace}_palettes_${api.id}`;\n      colors = this.getLocal(localKey);\n      if (!colors) {\n        colors = this.options.colors;\n        this.setLocal(localKey, colors);\n      }\n    } else {\n      colors = this.options.colors;\n    }\n\n    for (const i in colors) {\n      if(Object.hasOwnProperty.call(colors, i)){\n        this.colors.push(asColor.val(colors[i]).toRGBA());\n      }\n    }\n\n    let list = '';\n    $.each(this.colors, (i, color) => {\n      list += that.options.item(api.namespace, color);\n    });\n\n    this.$palettes = $(this.options.template.call(this, api.namespace)).html(list).appendTo(api.$dropdown);\n\n    this.$palettes.on(api.eventName('click'), 'li', function(e) {\n      const color = $(this).data('color');\n      api.set(color);\n\n      e.preventDefault();\n      e.stopPropagation();\n    });\n\n    api.$element.on('asColorPicker::apply', (e, api, color) => {\n      if (typeof color.toRGBA !== 'function') {\n        color = color.get().color;\n      }\n\n      const rgba = color.toRGBA();\n      if ($.inArray(rgba, that.colors) === -1) {\n        if (that.colors.length >= that.options.max) {\n          that.colors.shift();\n          that.$palettes.find('li').eq(0).remove();\n        }\n\n        that.colors.push(rgba);\n\n        that.$palettes.append(that.options.item(api.namespace, color));\n\n        if (that.options.localStorage) {\n          that.setLocal(localKey, that.colors);\n        }\n      }\n    });\n  },\n\n  setLocal: function(key, value) {\n    const jsonValue = JSON.stringify(value);\n\n    localStorage[key] = jsonValue;\n  },\n\n  getLocal: function(key) {\n    const value = localStorage[key];\n\n    return value ? JSON.parse(value) : value;\n  }\n};\n\n// preview\nvar preview = {\n  defaults: {\n    template(namespace) {\n      return `<ul class=\"${namespace}-preview\"><li class=\"${namespace}-preview-current\"><span /></li><li class=\"${namespace}-preview-previous\"><span /></li></ul>`;\n    }\n  },\n\n  init: function(api, options) {\n    const that = this;\n    this.options = $.extend(this.defaults, options);\n    this.$preview = $(this.options.template.call(that, api.namespace)).appendTo(api.$dropdown);\n    this.$current = this.$preview.find(`.${api.namespace}-preview-current span`);\n    this.$previous = this.$preview.find(`.${api.namespace}-preview-previous span`);\n\n    api.$element.on('asColorPicker::firstOpen', () => {\n      that.$previous.on('click', function() {\n        api.set($(this).data('color'));\n        return false;\n      });\n    });\n\n    api.$element.on('asColorPicker::setup', (e, api, color) => {\n      that.updateCurrent(color);\n      that.updatePreview(color);\n    });\n    api.$element.on('asColorPicker::update', (e, api, color) => {\n      that.updateCurrent(color);\n    });\n  },\n\n  updateCurrent: function(color) {\n    this.$current.css('backgroundColor', color.toRGBA());\n  },\n\n  updatePreview: function(color) {\n    this.$previous.css('backgroundColor', color.toRGBA());\n    this.$previous.data('color', {\n      r: color.value.r,\n      g: color.value.g,\n      b: color.value.b,\n      a: color.value.a\n    });\n  }\n};\n\n// gradient\nfunction conventToPercentage(n) {\n  if (n < 0) {\n    n = 0;\n  } else if (n > 1) {\n    n = 1;\n  }\n  return `${n * 100}%`;\n}\n\nvar Gradient = function(api, options) {\n  this.api = api;\n  this.options = options;\n  this.classes = {\n    enable: `${api.namespace}-gradient_enable`,\n    marker: `${api.namespace}-gradient-marker`,\n    active: `${api.namespace}-gradient-marker_active`,\n    focus: `${api.namespace}-gradient_focus`\n  };\n  this.isEnabled = false;\n  this.initialized = false;\n  this.current = null;\n  this.value = AsGradient(this.options.settings);\n  this.$doc = $(document);\n\n  const that = this;\n  $.extend(that, {\n    init() {\n      that.$wrap = $(that.options.template.call(that)).appendTo(api.$dropdown);\n\n      that.$gradient = that.$wrap.filter(`.${api.namespace}-gradient`);\n\n      this.angle.init();\n      this.preview.init();\n      this.markers.init();\n      this.wheel.init();\n\n      this.bind();\n\n      if (that.options.switchable === false || this.value.matchString(api.element.value)) {\n        that.enable();\n      }\n      this.initialized = true;\n    },\n    bind() {\n      const namespace = api.namespace;\n\n      that.$gradient.on('update', () => {\n        const current = that.value.getById(that.current);\n\n        if (current) {\n          api._trigger('update', current.color, that.value);\n        }\n\n        if (api.element.value !== that.value.toString()) {\n          api._updateInput();\n        }\n      });\n\n      // that.$gradient.on('add', function(e, data) {\n      //   if (data.stop) {\n      //     that.active(data.stop.id);\n      //     api._trigger('update', data.stop.color, that.value);\n      //     api._updateInput();\n      //   }\n      // });\n\n      if (that.options.switchable) {\n        that.$wrap.on('click', `.${namespace}-gradient-switch`, () => {\n          if (that.isEnabled) {\n            that.disable();\n          } else {\n            that.enable();\n          }\n\n          return false;\n        });\n      }\n\n      that.$wrap.on('click', `.${namespace}-gradient-cancel`, () => {\n        if (that.options.switchable === false || AsGradient.matchString(api.originValue)) {\n          that.overrideCore();\n        }\n\n        api.cancel();\n\n        return false;\n      });\n    },\n    overrideCore() {\n      api.set = value => {\n        if (value !== '') {\n          api.isEmpty = false;\n        } else {\n          api.isEmpty = true;\n        }\n        if (typeof value === 'string') {\n          if (that.options.switchable === false || AsGradient.matchString(value)) {\n            if (that.isEnabled) {\n              that.val(value);\n              api.color = that.value;\n              that.$gradient.trigger('update', that.value.value);\n            } else {\n              that.enable(value);\n            }\n          } else {\n            that.disable();\n            api.val(value);\n          }\n        } else {\n          const current = that.value.getById(that.current);\n\n          if (current) {\n            current.color.val(value);\n            api._trigger('update', current.color, that.value);\n          }\n\n          that.$gradient.trigger('update', {\n            id: that.current,\n            stop: current\n          });\n        }\n      };\n\n      api._setup = () => {\n        const current = that.value.getById(that.current);\n\n        api._trigger('setup', current.color);\n      };\n    },\n    revertCore() {\n      api.set = $.proxy(api._set, api);\n      api._setup = () => {\n        api._trigger('setup', api.color);\n      };\n    },\n    preview: {\n      init() {\n        that.$preview = that.$gradient.find(`.${api.namespace}-gradient-preview`);\n\n        that.$gradient.on('add del update empty', () => {\n          this.render();\n        });\n      },\n      render() {\n        that.$preview.css({\n          'background-image': that.value.toStringWithAngle('to right', true),\n        });\n        that.$preview.css({\n          'background-image': that.value.toStringWithAngle('to right'),\n        });\n      }\n    },\n    markers: {\n      width: 160,\n      init() {\n        that.$markers = that.$gradient.find(`.${api.namespace}-gradient-markers`).attr('tabindex', 0);\n\n        that.$gradient.on('add', (e, data) => {\n          this.add(data.stop);\n        });\n\n        that.$gradient.on('active', (e, data) => {\n          this.active(data.id);\n        });\n\n        that.$gradient.on('del', (e, data) => {\n          this.del(data.id);\n        });\n\n        that.$gradient.on('update', (e, data) => {\n          if (data.stop) {\n            this.update(data.stop.id, data.stop.color);\n          }\n        });\n\n        that.$gradient.on('empty', () => {\n          this.empty();\n        });\n\n        that.$markers.on(that.api.eventName('mousedown'), e => {\n          const rightclick = (e.which) ? (e.which === 3) : (e.button === 2);\n          if (rightclick) {\n            return false;\n          }\n\n          const position = parseFloat((e.pageX - that.$markers.offset().left) / that.markers.width, 10);\n          that.add('#fff', position);\n          return false;\n        });\n\n        /* eslint consistent-this: \"off\" */\n        let self = this;\n        that.$markers.on(that.api.eventName('mousedown'), 'li', function(e) {\n          const rightclick = (e.which) ? (e.which === 3) : (e.button === 2);\n          if (rightclick) {\n            return false;\n          }\n          self.mousedown(this, e);\n          return false;\n        });\n\n        that.$doc.on(that.api.eventName('keydown'), e => {\n          if (that.api.opened && that.$markers.is(`.${that.classes.focus}`)) {\n\n            const key = e.keyCode || e.which;\n            if (key === 46 || key === 8) {\n              if (that.value.length <= 2) {\n                return false;\n              }\n\n              that.del(that.current);\n\n              return false;\n            }\n          }\n        });\n\n        that.$markers.on(that.api.eventName('focus'), () => {\n          that.$markers.addClass(that.classes.focus);\n        }).on(that.api.eventName('blur'), () => {\n          that.$markers.removeClass(that.classes.focus);\n        });\n\n        that.$markers.on(that.api.eventName('click'), 'li', function() {\n          const id = $(this).data('id');\n          that.active(id);\n        });\n      },\n      getMarker(id) {\n        return that.$markers.find(`[data-id=\"${id}\"]`);\n      },\n      update(id, color) {\n        const $marker = this.getMarker(id);\n        $marker.find('span').css('background-color', color.toHEX());\n        $marker.find('i').css('background-color', color.toHEX());\n      },\n      add(stop) {\n        $(`<li data-id=\"${stop.id}\" style=\"left:${conventToPercentage(stop.position)}\" class=\"${that.classes.marker}\"><span style=\"background-color: ${stop.color.toHEX()}\"></span><i style=\"background-color: ${stop.color.toHEX()}\"></i></li>`).appendTo(that.$markers);\n      },\n      empty() {\n        that.$markers.html('');\n      },\n      del(id) {\n        const $marker = this.getMarker(id);\n        let $to = $marker.prev();\n        if ($to.length === 0) {\n          $to = $marker.next();\n        }\n\n        that.active($to.data('id'));\n        $marker.remove();\n      },\n      active(id) {\n        that.$markers.children().removeClass(that.classes.active);\n\n        const $marker = this.getMarker(id);\n        $marker.addClass(that.classes.active);\n\n        that.$markers.focus();\n        // that.api._trigger('apply', that.value.getById(id).color);\n      },\n      mousedown(marker, e) {\n        const self = this;\n        /* eslint consistent-this: \"off\" */\n        const id = $(marker).data('id');\n        const first = $(marker).position().left;\n        const start = e.pageX;\n        let end;\n\n        this.mousemove = function(e) {\n          end = e.pageX || start;\n          const position = (first + end - start) / this.width;\n          self.move(marker, position, id);\n          return false;\n        };\n\n        this.mouseup = function() {\n          $(document).off({\n            mousemove: this.mousemove,\n            mouseup: this.mouseup\n          });\n\n          return false;\n        };\n\n        that.$doc.on({\n          mousemove: $.proxy(this.mousemove, this),\n          mouseup: $.proxy(this.mouseup, this)\n        });\n        that.active(id);\n        return false;\n      },\n      move(marker, position, id) {\n        that.api.isEmpty = false;\n        position = Math.max(0, Math.min(1, position));\n        $(marker).css({\n          left: conventToPercentage(position)\n        });\n        if (!id) {\n          id = $(marker).data('id');\n        }\n\n        that.value.getById(id).setPosition(position);\n\n        that.$gradient.trigger('update', {\n          id: $(marker).data('id'),\n          position\n        });\n      },\n    },\n    wheel: {\n      init() {\n        that.$wheel = that.$gradient.find(`.${api.namespace}-gradient-wheel`);\n        that.$pointer = that.$wheel.find('i');\n\n        that.$gradient.on('update', (e, data) => {\n          if (typeof data.angle !== 'undefined') {\n            this.position(data.angle);\n          }\n        });\n\n        that.$wheel.on(that.api.eventName('mousedown'), e => {\n          const rightclick = (e.which) ? (e.which === 3) : (e.button === 2);\n          if (rightclick) {\n            return false;\n          }\n          this.mousedown(e, that);\n          return false;\n        });\n      },\n      mousedown(e, that) {\n        const offset = that.$wheel.offset();\n        const r = that.$wheel.width() / 2;\n        const startX = offset.left + r;\n        const startY = offset.top + r;\n        const $doc = that.$doc;\n\n        this.r = r;\n\n        this.wheelMove = e => {\n          const x = e.pageX - startX;\n          const y = startY - e.pageY;\n\n          const position = this.getPosition(x, y);\n          const angle = this.calAngle(position.x, position.y);\n          that.api.isEmpty = false;\n          that.setAngle(angle);\n        };\n        this.wheelMouseup = function() {\n          $doc.off({\n            mousemove: this.wheelMove,\n            mouseup: this.wheelMouseup\n          });\n          return false;\n        };\n        $doc.on({\n          mousemove: $.proxy(this.wheelMove, this),\n          mouseup: $.proxy(this.wheelMouseup, this)\n        });\n\n        this.wheelMove(e);\n      },\n      getPosition(a, b) {\n        const r = this.r;\n        const x = a / Math.sqrt(a * a + b * b) * r;\n        const y = b / Math.sqrt(a * a + b * b) * r;\n        return {\n          x,\n          y\n        };\n      },\n      calAngle(x, y) {\n        const deg = Math.round(Math.atan(Math.abs(x / y)) * (180 / Math.PI));\n        if (x < 0 && y > 0) {\n          return 360 - deg;\n        }\n        if (x < 0 && y <= 0) {\n          return deg + 180;\n        }\n        if (x >= 0 && y <= 0) {\n          return 180 - deg;\n        }\n        if (x >= 0 && y > 0) {\n          return deg;\n        }\n      },\n      set(value) {\n        that.value.angle(value);\n        that.$gradient.trigger('update', {\n          angle: value\n        });\n      },\n      position(angle) {\n        const r = this.r || that.$wheel.width() / 2;\n        const pos = this.calPointer(angle, r);\n        that.$pointer.css({\n          left: pos.x,\n          top: pos.y\n        });\n      },\n      calPointer(angle, r) {\n        const x = Math.sin(angle * Math.PI / 180) * r;\n        const y = Math.cos(angle * Math.PI / 180) * r;\n        return {\n          x: r + x,\n          y: r - y\n        };\n      }\n    },\n    angle: {\n      init() {\n        that.$angle = that.$gradient.find(`.${api.namespace}-gradient-angle`);\n\n        that.$angle.on(that.api.eventName('blur'), function() {\n          that.setAngle(this.value);\n          return false;\n        }).on(that.api.eventName('keydown'), function(e) {\n          const key = e.keyCode || e.which;\n          if (key === 13) {\n            that.api.isEmpty = false;\n            $(this).blur();\n            return false;\n          }\n        });\n\n        that.$gradient.on('update', (e, data) => {\n          if (typeof data.angle !== 'undefined') {\n            that.$angle.val(data.angle);\n          }\n        });\n      },\n      set(value) {\n        that.value.angle(value);\n        that.$gradient.trigger('update', {\n          angle: value\n        });\n      }\n    }\n  });\n\n  this.init();\n};\n\nGradient.prototype = {\n  constructor: Gradient,\n\n  enable(value) {\n    if (this.isEnabled === true) {\n      return;\n    }\n    this.isEnabled = true;\n    this.overrideCore();\n\n\n\n    this.$gradient.addClass(this.classes.enable);\n    this.markers.width = this.$markers.width();\n\n    if (typeof value === 'undefined') {\n      value = this.api.element.value;\n    }\n\n    if (value !== '') {\n      this.api.isEmpty = false;\n    } else {\n      this.api.isEmpty = true;\n    }\n\n    if (!AsGradient.matchString(value) && this._last) {\n      this.value = this._last;\n    } else {\n      this.val(value);\n    }\n    this.api.color = this.value;\n\n    this.$gradient.trigger('update', this.value.value);\n\n    if (this.api.opened) {\n      this.api.position();\n    }\n  },\n  val(string) {\n    if (string !== '' && this.value.toString() === string) {\n      return;\n    }\n    this.empty();\n    this.value.val(string);\n    this.value.reorder();\n\n    if (this.value.length < 2) {\n      let fill = string;\n\n      if (!AsColor.matchString(string)) {\n        fill = 'rgba(0,0,0,1)';\n      }\n\n      if (this.value.length === 0) {\n        this.value.append(fill, 0);\n      }\n      if (this.value.length === 1) {\n        this.value.append(fill, 1);\n      }\n    }\n\n    let stop;\n    for (let i = 0; i < this.value.length; i++) {\n      stop = this.value.get(i);\n      if (stop) {\n        this.$gradient.trigger('add', {\n          stop\n        });\n      }\n    }\n\n    this.active(stop.id);\n  },\n  disable() {\n    if (this.isEnabled === false) {\n      return;\n    }\n    this.isEnabled = false;\n    this.revertCore();\n\n    this.$gradient.removeClass(this.classes.enable);\n    this._last = this.value;\n    this.api.color = this.api.color.getCurrent().color;\n    this.api.set(this.api.color.value);\n\n    if (this.api.opened) {\n      this.api.position();\n    }\n  },\n  active(id) {\n    if (this.current !== id) {\n      this.current = id;\n      this.value.setCurrentById(id);\n\n      this.$gradient.trigger('active', {\n        id\n      });\n    }\n  },\n  empty() {\n    this.value.empty();\n    this.$gradient.trigger('empty');\n  },\n  add(color, position) {\n    const stop = this.value.insert(color, position);\n    this.api.isEmpty = false;\n    this.value.reorder();\n\n    this.$gradient.trigger('add', {\n      stop\n    });\n\n    this.active(stop.id);\n\n    this.$gradient.trigger('update', {\n      stop\n    });\n    return stop;\n  },\n  del(id) {\n    if (this.value.length <= 2) {\n      return;\n    }\n    this.value.removeById(id);\n    this.value.reorder();\n    this.$gradient.trigger('del', {\n      id\n    });\n\n    this.$gradient.trigger('update', {});\n  },\n  setAngle(value) {\n    this.value.angle(value);\n    this.$gradient.trigger('update', {\n      angle: value\n    });\n  }\n};\n\n\nvar gradient = {\n  defaults: {\n    switchable: true,\n    switchText: 'Gradient',\n    cancelText: 'Cancel',\n    settings: {\n      forceStandard: true,\n      angleUseKeyword: true,\n      emptyString: '',\n      degradationFormat: false,\n      cleanPosition: false,\n      color: {\n        format: 'rgb' // rgb, rgba, hsl, hsla, hex\n      }\n    },\n    template() {\n      const namespace = this.api.namespace;\n      let control = `<div class=\"${namespace}-gradient-control\">`;\n      if (this.options.switchable) {\n        control += `<a href=\"#\" class=\"${namespace}-gradient-switch\">${this.options.switchText}</a>`;\n      }\n      control += `<a href=\"#\" class=\"${namespace}-gradient-cancel\">${this.options.cancelText}</a></div>`;\n\n      return `${control}<div class=\"${namespace}-gradient\"><div class=\"${namespace}-gradient-preview\"><ul class=\"${namespace}-gradient-markers\"></ul></div><div class=\"${namespace}-gradient-wheel\"><i></i></div><input class=\"${namespace}-gradient-angle\" type=\"text\" value=\"\" size=\"3\" /></div>`;\n    }\n  },\n\n  init: function(api, options) {\n    const that = this;\n\n    api.$element.on('asColorPicker::ready', (event, instance) => {\n      if (instance.options.mode !== 'gradient') {\n        return;\n      }\n\n      that.defaults.settings.color = api.options.color;\n      options = $.extend(true, that.defaults, options);\n\n      api.gradient = new Gradient(api, options);\n    });\n  }\n};\n\nconst NAMESPACE$1 = 'asColorPicker';\nconst COMPONENTS = {};\nconst LOCALIZATIONS = {\n  en: {\n    cancelText: 'cancel',\n    applyText: 'apply'\n  }\n};\n\nlet id = 0;\n\nfunction createId(api) {\n  api.id = id;\n  id++;\n}\n\nclass AsColorPicker {\n  constructor(element, options) {\n    this.element = element;\n    this.$element = $$1(element);\n\n    //flag\n    this.opened = false;\n    this.firstOpen = true;\n    this.disabled = false;\n    this.initialed = false;\n    this.originValue = this.element.value;\n    this.isEmpty = false;\n\n    createId(this);\n\n    this.options = $$1.extend(true, {}, DEFAULTS, options, this.$element.data());\n    this.namespace = this.options.namespace;\n\n    this.classes = {\n      wrap: `${this.namespace}-wrap`,\n      dropdown: `${this.namespace}-dropdown`,\n      input: `${this.namespace}-input`,\n      skin: `${this.namespace}_${this.options.skin}`,\n      open: `${this.namespace}_open`,\n      mask: `${this.namespace}-mask`,\n      hideInput: `${this.namespace}_hideInput`,\n      disabled: `${this.namespace}_disabled`,\n      mode: `${this.namespace}-mode_${this.options.mode}`\n    };\n\n    if (this.options.hideInput) {\n      this.$element.addClass(this.classes.hideInput);\n    }\n\n    this.components = MODES[this.options.mode];\n    this._components = $$1.extend(true, {}, COMPONENTS);\n\n    this._trigger('init');\n    this.init();\n  }\n\n  _trigger(eventType, ...params) {\n    let data = [this].concat(params);\n\n    // event\n    this.$element.trigger(`${NAMESPACE$1}::${eventType}`, data);\n\n    // callback\n    eventType = eventType.replace(/\\b\\w+\\b/g, (word) => {\n      return word.substring(0, 1).toUpperCase() + word.substring(1);\n    });\n    let onFunction = `on${eventType}`;\n\n    if (typeof this.options[onFunction] === 'function') {\n      this.options[onFunction].apply(this, params);\n    }\n  }\n\n  eventName(events) {\n    if (typeof events !== 'string' || events === '') {\n      return `.${this.options.namespace}`;\n    }\n    events = events.split(' ');\n\n    let length = events.length;\n    for (let i = 0; i < length; i++) {\n      events[i] = `${events[i]}.${this.options.namespace}`;\n    }\n    return events.join(' ');\n  }\n\n  init() {\n    this.color = AsColor(this.element.value, this.options.color);\n\n    this._create();\n\n    if (this.options.skin) {\n      this.$dropdown.addClass(this.classes.skin);\n      this.$element.parent().addClass(this.classes.skin);\n    }\n\n    if (this.options.readonly) {\n      this.$element.prop('readonly', true);\n    }\n\n    this._bindEvent();\n\n    this.initialed = true;\n    this._trigger('ready');\n  }\n\n  _create() {\n    this.$dropdown = $$1(`<div class=\"${this.classes.dropdown}\" data-mode=\"${this.options.mode}\"></div>`);\n    this.$element.wrap(`<div class=\"${this.classes.wrap}\"></div>`).addClass(this.classes.input);\n\n    this.$wrap = this.$element.parent();\n    this.$body = $$1('body');\n\n    this.$dropdown.data(NAMESPACE$1, this);\n\n    let component;\n    $$1.each(this.components, (key, options) => {\n      if (options === true) {\n        options = {};\n      }\n      if (this.options[key] !== undefined) {\n        options = $$1.extend(true, {}, options, this.options[key]);\n      }\n      if (Object.hasOwnProperty.call(this._components, key)) {\n        component = this._components[key];\n        component.init(this, options);\n      }\n    });\n\n    this._trigger('create');\n  }\n\n  _bindEvent() {\n    this.$element.on(this.eventName('click'), () => {\n      if (!this.opened) {\n        this.open();\n      }\n      return false;\n    });\n\n    this.$element.on(this.eventName('keydown'), (e) => {\n      if (e.keyCode === 9) {\n        this.close();\n      } else if (e.keyCode === 13) {\n        this.val(this.element.value);\n        this.close();\n      }\n    });\n\n    this.$element.on(this.eventName('keyup'), () => {\n      if (this.color.matchString(this.element.value)) {\n        this.val(this.element.value);\n      }\n    });\n  }\n\n  opacity(v) {\n    if (v) {\n      this.color.alpha(v);\n    } else {\n      return this.color.alpha();\n    }\n  }\n\n  position() {\n    const hidden = !this.$element.is(':visible');\n    const offset = hidden ? this.$trigger.offset() : this.$element.offset();\n    const height = hidden ? this.$trigger.outerHeight() : this.$element.outerHeight();\n    const width = hidden ? this.$trigger.outerWidth() : this.$element.outerWidth() + this.$trigger.outerWidth();\n    const pickerWidth = this.$dropdown.outerWidth(true);\n    const pickerHeight = this.$dropdown.outerHeight(true);\n    let top;\n    let left;\n\n    if (pickerHeight + offset.top > $$1(window).height() + $$1(window).scrollTop()) {\n      top = offset.top - pickerHeight;\n    } else {\n      top = offset.top + height;\n    }\n\n    if (pickerWidth + offset.left > $$1(window).width() + $$1(window).scrollLeft()) {\n      left = offset.left - pickerWidth + width;\n    } else {\n      left = offset.left;\n    }\n\n    this.$dropdown.css({\n      position: 'absolute',\n      top,\n      left\n    });\n  }\n\n  open() {\n    if (this.disabled) {\n      return;\n    }\n    this.originValue = this.element.value;\n\n    if (this.$dropdown[0] !== this.$body.children().last()[0]) {\n      this.$dropdown.detach().appendTo(this.$body);\n    }\n\n    this.$mask = $$1(`.${this.classes.mask}`);\n    if (this.$mask.length === 0) {\n      this.createMask();\n    }\n\n    // ensure the mask is always right before the dropdown\n    if (this.$dropdown.prev()[0] !== this.$mask[0]) {\n      this.$dropdown.before(this.$mask);\n    }\n\n    $$1(\"#asColorPicker-dropdown\").removeAttr(\"id\");\n    this.$dropdown.attr(\"id\", \"asColorPicker-dropdown\");\n\n    // show the mask\n    this.$mask.show();\n\n    this.position();\n\n    $$1(window).on(this.eventName('resize'), $$1.proxy(this.position, this));\n\n    this.$dropdown.addClass(this.classes.open);\n\n    this.opened = true;\n\n    if (this.firstOpen) {\n      this.firstOpen = false;\n      this._trigger('firstOpen');\n    }\n    this._setup();\n    this._trigger('open');\n  }\n\n  createMask() {\n    this.$mask = $$1(document.createElement(\"div\"));\n    this.$mask.attr(\"class\", this.classes.mask);\n    this.$mask.hide();\n    this.$mask.appendTo(this.$body);\n\n    this.$mask.on(this.eventName(\"mousedown touchstart click\"), e => {\n      const $dropdown = $$1(\"#asColorPicker-dropdown\");\n      let self;\n      if ($dropdown.length > 0) {\n        self = $dropdown.data(NAMESPACE$1);\n        if (self.opened) {\n          if (self.options.hideFireChange) {\n            self.apply();\n          } else {\n            self.cancel();\n          }\n        }\n\n        e.preventDefault();\n        e.stopPropagation();\n      }\n    });\n  }\n\n  close() {\n    this.opened = false;\n    this.$element.blur();\n    this.$mask.hide();\n\n    this.$dropdown.removeClass(this.classes.open);\n\n    $$1(window).off(this.eventName('resize'));\n\n    this._trigger('close');\n  }\n\n  clear() {\n    this.val('');\n  }\n\n  cancel() {\n    this.close();\n\n    this.set(this.originValue);\n  }\n\n  apply() {\n    this._trigger('apply', this.color);\n    this.close();\n  }\n\n  val(value) {\n    if (typeof value === 'undefined') {\n      return this.color.toString();\n    }\n\n    this.set(value);\n  }\n\n  _update() {\n    this._trigger('update', this.color);\n    this._updateInput();\n  }\n\n  _updateInput() {\n    let value = this.color.toString();\n    if (this.isEmpty) {\n      value = '';\n    }\n    this._trigger('change', value);\n    this.$element.val(value);\n  }\n\n  set(value) {\n    if (value !== '') {\n      this.isEmpty = false;\n    } else {\n      this.isEmpty = true;\n    }\n    return this._set(value);\n  }\n\n  _set(value) {\n    if (typeof value === 'string') {\n      this.color.val(value);\n    } else {\n      this.color.set(value);\n    }\n\n    this._update();\n  }\n\n  _setup() {\n    this._trigger('setup', this.color);\n  }\n\n  get() {\n    return this.color;\n  }\n\n  enable() {\n    this.disabled = false;\n    this.$parent.addClass(this.classes.disabled);\n    this._trigger('enable');\n    return this;\n  }\n\n  disable() {\n    this.disabled = true;\n    this.$parent.removeClass(this.classes.disabled);\n    this._trigger('disable');\n    return this;\n  }\n\n  destroy() {\n    this.$element.unwrap();\n    this.$element.off(this.eventName());\n    this.$mask.remove();\n    this.$dropdown.remove();\n\n    this.initialized = false;\n    this.$element.data(NAMESPACE$1, null);\n\n    this._trigger('destroy');\n    return this;\n  }\n\n  getString(name, def) {\n    if(this.options.lang in LOCALIZATIONS && typeof LOCALIZATIONS[this.options.lang][name] !== 'undefined') {\n      return LOCALIZATIONS[this.options.lang][name];\n    }\n    return def;\n  }\n\n  static setLocalization(lang, strings) {\n    LOCALIZATIONS[lang] = strings;\n  }\n\n  static registerComponent(name, method) {\n    COMPONENTS[name] = method;\n  }\n\n  static setDefaults(options) {\n    $$1.extend(true, DEFAULTS, $$1.isPlainObject(options) && options);\n  }\n}\n\nAsColorPicker.registerComponent('alpha', alpha);\nAsColorPicker.registerComponent('hex', hex);\nAsColorPicker.registerComponent('hue', hue);\nAsColorPicker.registerComponent('saturation', saturation);\nAsColorPicker.registerComponent('buttons', buttons);\nAsColorPicker.registerComponent('trigger', trigger);\nAsColorPicker.registerComponent('clear', clear);\nAsColorPicker.registerComponent('info', info);\nAsColorPicker.registerComponent('palettes', palettes);\nAsColorPicker.registerComponent('preview', preview);\nAsColorPicker.registerComponent('gradient', gradient);\n\n// Chinese (cn) localization\nAsColorPicker.setLocalization('cn', {\n  cancelText: \"取消\",\n  applyText: \"应用\"\n});\n\n// German (de) localization\nAsColorPicker.setLocalization('de', {\n  cancelText: \"Abbrechen\",\n  applyText: \"Wählen\"\n});\n\n// Danish (dk) localization\nAsColorPicker.setLocalization('dk', {\n  cancelText: \"annuller\",\n  applyText: \"Vælg\"\n});\n\n// Spanish (es) localization\nAsColorPicker.setLocalization('es', {\n  cancelText: \"Cancelar\",\n  applyText: \"Elegir\"\n});\n\n// Finnish (fi) localization\nAsColorPicker.setLocalization('fi', {\n  cancelText: \"Kumoa\",\n  applyText: \"Valitse\"\n});\n\n// French (fr) localization\nAsColorPicker.setLocalization('fr', {\n  cancelText: \"Annuler\",\n  applyText: \"Valider\"\n});\n\n// Italian (it) localization\nAsColorPicker.setLocalization('it', {\n  cancelText: \"annulla\",\n  applyText: \"scegli\"\n});\n\n// Japanese (ja) localization\nAsColorPicker.setLocalization('ja', {\n  cancelText: \"中止\",\n  applyText: \"選択\"\n});\n\n// Russian (ru) localization\nAsColorPicker.setLocalization('ru', {\n  cancelText: \"отмена\",\n  applyText: \"выбрать\"\n});\n\n// Swedish (sv) localization\nAsColorPicker.setLocalization('sv', {\n  cancelText: \"Avbryt\",\n  applyText: \"Välj\"\n});\n\n// Turkish (tr) localization\nAsColorPicker.setLocalization('tr', {\n  cancelText: \"Avbryt\",\n  applyText: \"Välj\"\n});\n\nvar info$1 = {\n  version:'0.4.4'\n};\n\nconst NAMESPACE = 'asColorPicker';\nconst OtherAsColorPicker = $$1.fn.asColorPicker;\n\nconst jQueryAsColorPicker = function(options, ...args) {\n  if (typeof options === 'string') {\n    const method = options;\n\n    if (/^_/.test(method)) {\n      return false;\n    } else if ((/^(get)$/.test(method)) || (method === 'val' && args.length === 0)) {\n      const instance = this.first().data(NAMESPACE);\n      if (instance && typeof instance[method] === 'function') {\n        return instance[method](...args);\n      }\n    } else {\n      return this.each(function() {\n        const instance = $$1.data(this, NAMESPACE);\n        if (instance && typeof instance[method] === 'function') {\n          instance[method](...args);\n        }\n      });\n    }\n  }\n\n  return this.each(function() {\n    if (!$$1(this).data(NAMESPACE)) {\n      $$1(this).data(NAMESPACE, new AsColorPicker(this, options));\n    }\n  });\n};\n\n$$1.fn.asColorPicker = jQueryAsColorPicker;\n\n$$1.asColorPicker = $$1.extend({\n  setDefaults: AsColorPicker.setDefaults,\n  registerComponent: AsColorPicker.registerComponent,\n  setLocalization: AsColorPicker.setLocalization,\n  noConflict: function() {\n    $$1.fn.asColorPicker = OtherAsColorPicker;\n    return jQueryAsColorPicker;\n  }\n}, info$1);\n"]}