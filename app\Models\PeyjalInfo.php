<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PeyjalInfo extends Model
{
    use HasFactory;
    public function ulb()
    {
        return $this->belongsTo(User::class,'ulb_id');
    }
    public function division()
    {
        return $this->belongsTo(MasterDivision::class, 'division_id');
    }
    protected $fillable = [
        'ulb_id',
        'dateInput',
        'yojna_name',
        'phe_handover_date',
        'not_handover_reason',
        'wtp_number',
        'wtp_location',
        'wtp_capcity',
    ];
}
