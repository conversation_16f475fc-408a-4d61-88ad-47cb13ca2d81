<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EnvironmentalInitiative extends Model
{
    use HasFactory;

    protected $fillable = [
        'ulb_id',
        'green_spaces',
        'tree_plantation_drives',
        'household_rainwater_harvesting_count',
        'rainwater_harvesting_initiatives',
        'under_progress_stp',
        'stp_number',
        'attempts'
    ];

    public function ulb()
    {
        return $this->belongsTo(User::class, 'ulb_id');
    }
} 