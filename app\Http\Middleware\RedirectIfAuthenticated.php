<?php

namespace App\Http\Middleware;

use App\Providers\RouteServiceProvider;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class RedirectIfAuthenticated
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle($request, Closure $next, $guard = null): Response
    {
        // Log::info('Middleware check', ['guard' => $guard, 'authenticated' => Auth::guard($guard)->check()]);

        if (Auth::guard('web')->check() && Auth::guard($guard)->check()) {
            return redirect()->route('ulb.dashboard');
        }

        if (Auth::guard('admin')->check()) {
            $user = Auth::guard('admin')->user();
            if ($user) {
                if ($user->role_id == 1) {
                    return redirect()->route('admin.dashboard');
                } elseif ($user->role_id == 2) {
                    return redirect()->route('department.dashboard');
                } elseif ($user->role_id == 3) {
                    return redirect()->route('jddashboard');
                }
            }
        }

        return $next($request);
    }
}
