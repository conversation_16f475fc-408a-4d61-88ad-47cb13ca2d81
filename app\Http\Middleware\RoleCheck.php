<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class RoleCheck
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, $role): Response
    {
        // Check if user is authenticated with the admin guard
        if (!Auth::guard('admin')->check()) {
            return redirect()->route('admin.login')->withErrors(['error' => 'You must be logged in to access this page.']);
        }

        // Check if the authenticated user has the correct role
        $user = Auth::guard('admin')->user();
        if ($user->role_id !== (int)$role) {
            return redirect('/')->withErrors(['error' => 'Unauthorized access.']);
        }

        return $next($request);
    }
}
