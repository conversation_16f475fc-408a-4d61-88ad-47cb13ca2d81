<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ElectricityBill;
use App\Models\MasterDivision;
use App\Models\RevenueCollection;
use App\Models\SalaryDetail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ApiController extends Controller
{

    public function electricity()
    {
        $electricitybill = ElectricityBill::with('ulb')->select(
            'ulb_id',
            DB::raw('MAX(pending_electricity_bill) as total_pending_electricity_bill'),
            DB::raw('MAX(surcharge_amount) as total_surcharge_amount'),
            DB::raw('MAX(previous_paid_bill_amount	) as total_previous_paid_bill_amoun'),
            DB::raw('MAX(previous_paid_bill_date) as previous_paid_bill_date'),
        )
            ->groupBy('ulb_id')
            ->get();
        return response()->json($electricitybill);
    }

    public function salarydetail()
    {
        $salarydetails = SalaryDetail::with('ulb')->select(
            'ulb_id',
            DB::raw('MAX(total_amount_paid) as total_amount_paid'),
            DB::raw('MAX(date_of_payement) as date_of_payement'),
            DB::raw('MAX(total_amount_paid_placement) as total_amount_paid_placement'),
            DB::raw('MAX(date_of_payement_placement	) as date_of_payement_placement'),
        )
            ->groupBy('ulb_id')
            ->get();
        return response()->json($salarydetails);
    }


    
    // public function revenuecollection()
    // {
    //     $revenueCollection = RevenueCollection::with('ulb')->select(
    //         'ulb_id',
    //         DB::raw('MAX(current_demand) as current_demand'),
    //         DB::raw('MAX(current_collection) as current_collection'),
    //         DB::raw('MAX(current_collection_percentage) as current_collection_percentage'),
    //         DB::raw('MAX(pending_demand) as pending_demand'),
    //         DB::raw('MAX(pending_collection) as pending_collection'),
    //         DB::raw('MAX(pending_collection_percentage) as pending_collection_percentage'),
    //         DB::raw('MAX(total_demand) as total_demand'),
    //         DB::raw('MAX(total_collection) as total_collection'),
    //         DB::raw('MAX(total_collection_percentage) as total_collection_percentage')
    //     )
    //         ->groupBy('ulb_id')
    //         ->orderBy('total_collection_percentage', 'asc')
    //         ->get();
    //     return response()->json($revenueCollection);
    // }

    public function revenuecollectiondurg()
    {
        $revenueCollection = RevenueCollection::with(['ulb:id,division_id,name', 'ulb.division:id,name'])
            ->whereHas('ulb', function ($query) {
                $query->where('division_id', 2);
            })
            ->select(
                'ulb_id',
                DB::raw('MAX(current_demand) as current_demand'),
                DB::raw('MAX(current_collection) as current_collection'),
                DB::raw('MAX(current_collection_percentage) as current_collection_percentage'),
                DB::raw('MAX(pending_demand) as pending_demand'),
                DB::raw('MAX(pending_collection) as pending_collection'),
                DB::raw('MAX(pending_collection_percentage) as pending_collection_percentage'),
                DB::raw('MAX(total_demand) as total_demand'),
                DB::raw('MAX(total_collection) as total_collection'),
                DB::raw('MAX(total_collection_percentage) as total_collection_percentage')
            )
            ->groupBy('ulb_id')
            ->orderBy('total_collection_percentage', 'asc')
            ->get()
            ->map(function ($item) {
                return [
                    'ulb_id' => $item->ulb_id,
                    'division_id' => $item->ulb->division->id ?? null,
                    'ulb_name' => $item->ulb->name ?? null,
                    'division_name' => $item->ulb->division->name ?? null,
                    'current_demand' => $item->current_demand,
                    'current_collection' => $item->current_collection,
                    'current_collection_percentage' => $item->current_collection_percentage,
                    'pending_demand' => $item->pending_demand,
                    'pending_collection' => $item->pending_collection,
                    'pending_collection_percentage' => $item->pending_collection_percentage,
                    'total_demand' => $item->total_demand,
                    'total_collection' => $item->total_collection,
                    'total_collection_percentage' => $item->total_collection_percentage,
                ];
            });

        return response()->json($revenueCollection);
    }
    public function revenuecollectionraipur()
    {
        $revenueCollection = RevenueCollection::with(['ulb:id,division_id,name', 'ulb.division:id,name'])
            ->whereHas('ulb', function ($query) {
                $query->where('division_id', 3);
            })
            ->select(
                'ulb_id',
                DB::raw('MAX(current_demand) as current_demand'),
                DB::raw('MAX(current_collection) as current_collection'),
                DB::raw('MAX(current_collection_percentage) as current_collection_percentage'),
                DB::raw('MAX(pending_demand) as pending_demand'),
                DB::raw('MAX(pending_collection) as pending_collection'),
                DB::raw('MAX(pending_collection_percentage) as pending_collection_percentage'),
                DB::raw('MAX(total_demand) as total_demand'),
                DB::raw('MAX(total_collection) as total_collection'),
                DB::raw('MAX(total_collection_percentage) as total_collection_percentage')
            )
            ->groupBy('ulb_id')
            ->orderBy('total_collection_percentage', 'asc')
            ->get()
            ->map(function ($item) {
                return [
                    'ulb_id' => $item->ulb_id,
                    'division_id' => $item->ulb->division->id ?? null,
                    'ulb_name' => $item->ulb->name ?? null,
                    'division_name' => $item->ulb->division->name ?? null,
                    'current_demand' => $item->current_demand,
                    'current_collection' => $item->current_collection,
                    'current_collection_percentage' => $item->current_collection_percentage,
                    'pending_demand' => $item->pending_demand,
                    'pending_collection' => $item->pending_collection,
                    'pending_collection_percentage' => $item->pending_collection_percentage,
                    'total_demand' => $item->total_demand,
                    'total_collection' => $item->total_collection,
                    'total_collection_percentage' => $item->total_collection_percentage,
                ];
            });

        return response()->json($revenueCollection);
    }
    public function revenuecollectionbilaspur()
    {
        $revenueCollection = RevenueCollection::with(['ulb:id,division_id,name', 'ulb.division:id,name'])
            ->whereHas('ulb', function ($query) {
                $query->where('division_id', 4);
            })
            ->select(
                'ulb_id',
                DB::raw('MAX(current_demand) as current_demand'),
                DB::raw('MAX(current_collection) as current_collection'),
                DB::raw('MAX(current_collection_percentage) as current_collection_percentage'),
                DB::raw('MAX(pending_demand) as pending_demand'),
                DB::raw('MAX(pending_collection) as pending_collection'),
                DB::raw('MAX(pending_collection_percentage) as pending_collection_percentage'),
                DB::raw('MAX(total_demand) as total_demand'),
                DB::raw('MAX(total_collection) as total_collection'),
                DB::raw('MAX(total_collection_percentage) as total_collection_percentage')
            )
            ->groupBy('ulb_id')
            ->orderBy('total_collection_percentage', 'asc')
            ->get()
            ->map(function ($item) {
                return [
                    'ulb_id' => $item->ulb_id,
                    'division_id' => $item->ulb->division->id ?? null,
                    'ulb_name' => $item->ulb->name ?? null,
                    'division_name' => $item->ulb->division->name ?? null,
                    'current_demand' => $item->current_demand,
                    'current_collection' => $item->current_collection,
                    'current_collection_percentage' => $item->current_collection_percentage,
                    'pending_demand' => $item->pending_demand,
                    'pending_collection' => $item->pending_collection,
                    'pending_collection_percentage' => $item->pending_collection_percentage,
                    'total_demand' => $item->total_demand,
                    'total_collection' => $item->total_collection,
                    'total_collection_percentage' => $item->total_collection_percentage,
                ];
            });

        return response()->json($revenueCollection);
    }
    public function revenuecollectionsarguja()
    {
        $revenueCollection = RevenueCollection::with(['ulb:id,division_id,name', 'ulb.division:id,name'])
            ->whereHas('ulb', function ($query) {
                $query->where('division_id', 5);
            })
            ->select(
                'ulb_id',
                DB::raw('MAX(current_demand) as current_demand'),
                DB::raw('MAX(current_collection) as current_collection'),
                DB::raw('MAX(current_collection_percentage) as current_collection_percentage'),
                DB::raw('MAX(pending_demand) as pending_demand'),
                DB::raw('MAX(pending_collection) as pending_collection'),
                DB::raw('MAX(pending_collection_percentage) as pending_collection_percentage'),
                DB::raw('MAX(total_demand) as total_demand'),
                DB::raw('MAX(total_collection) as total_collection'),
                DB::raw('MAX(total_collection_percentage) as total_collection_percentage')
            )
            ->groupBy('ulb_id')
            ->orderBy('total_collection_percentage', 'asc')
            ->get()
            ->map(function ($item) {
                return [
                    'ulb_id' => $item->ulb_id,
                    'division_id' => $item->ulb->division->id ?? null,
                    'ulb_name' => $item->ulb->name ?? null,
                    'division_name' => $item->ulb->division->name ?? null,
                    'current_demand' => $item->current_demand,
                    'current_collection' => $item->current_collection,
                    'current_collection_percentage' => $item->current_collection_percentage,
                    'pending_demand' => $item->pending_demand,
                    'pending_collection' => $item->pending_collection,
                    'pending_collection_percentage' => $item->pending_collection_percentage,
                    'total_demand' => $item->total_demand,
                    'total_collection' => $item->total_collection,
                    'total_collection_percentage' => $item->total_collection_percentage,
                ];
            });

        return response()->json($revenueCollection);
    }
    public function revenuecollectionbastar()
    {
        $revenueCollection = RevenueCollection::with(['ulb:id,division_id,name', 'ulb.division:id,name'])
            ->whereHas('ulb', function ($query) {
                $query->where('division_id', 1);
            })
            ->select(
                'ulb_id',
                DB::raw('MAX(current_demand) as current_demand'),
                DB::raw('MAX(current_collection) as current_collection'),
                DB::raw('MAX(current_collection_percentage) as current_collection_percentage'),
                DB::raw('MAX(pending_demand) as pending_demand'),
                DB::raw('MAX(pending_collection) as pending_collection'),
                DB::raw('MAX(pending_collection_percentage) as pending_collection_percentage'),
                DB::raw('MAX(total_demand) as total_demand'),
                DB::raw('MAX(total_collection) as total_collection'),
                DB::raw('MAX(total_collection_percentage) as total_collection_percentage')
            )
            ->groupBy('ulb_id')
            ->orderBy('total_collection_percentage', 'asc')
            ->get()
            ->map(function ($item) {
                return [
                    'ulb_id' => $item->ulb_id,
                    'division_id' => $item->ulb->division->id ?? null,
                    'ulb_name' => $item->ulb->name ?? null,
                    'division_name' => $item->ulb->division->name ?? null,
                    'current_demand' => $item->current_demand,
                    'current_collection' => $item->current_collection,
                    'current_collection_percentage' => $item->current_collection_percentage,
                    'pending_demand' => $item->pending_demand,
                    'pending_collection' => $item->pending_collection,
                    'pending_collection_percentage' => $item->pending_collection_percentage,
                    'total_demand' => $item->total_demand,
                    'total_collection' => $item->total_collection,
                    'total_collection_percentage' => $item->total_collection_percentage,
                ];
            });

        return response()->json($revenueCollection);
    }
}
