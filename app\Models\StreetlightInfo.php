<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StreetlightInfo extends Model
{
    use HasFactory;
    public function ulb()
    {
        return $this->belongsTo(User::class,'ulb_id');
    }
    public function division()
    {
        return $this->belongsTo(MasterDivision::class, 'division_id');
    }
    protected $fillable = [
        'ulb_id',
        'unique_id',
        'dateInput',
        'total_pole',
        'pole_without_light',
        'total_light',
        'total_working_light',
        'total_not_working_light',
    ];
}
