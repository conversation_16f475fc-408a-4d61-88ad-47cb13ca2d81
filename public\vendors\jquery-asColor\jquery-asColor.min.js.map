{"version": 3, "sources": ["jquery-asColor.es.js"], "names": ["global", "factory", "define", "amd", "exports", "require", "mod", "j<PERSON><PERSON><PERSON>", "AsColor", "this", "_j<PERSON>y", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "expandHex", "hex", "indexOf", "substr", "length", "shrinkHex", "parseIntFromHex", "val", "parseInt", "isPercentage", "n", "conventPercentageToRgb", "Math", "round", "slice", "convertPercentageToFloat", "parseFloat", "Object", "defineProperty", "value", "_jquery2", "obj", "__esModule", "default", "_interopRequireDefault", "_typeof", "Symbol", "iterator", "constructor", "prototype", "_createClass", "defineProperties", "target", "props", "i", "descriptor", "enumerable", "configurable", "writable", "key", "protoProps", "staticProps", "DEFAULTS", "format", "shortenHex", "hexUseName", "reduceAlpha", "alphaConvert", "RGB", "HSL", "HEX", "NAMESPACE", "nameDegradation", "invalidV<PERSON>ue", "zeroAlphaAsTransparent", "NAMES", "aliceblue", "antiquewhite", "aqua", "aquamarine", "azure", "beige", "bisque", "black", "blanche<PERSON><PERSON>", "blue", "blueviolet", "brown", "burlywood", "<PERSON><PERSON><PERSON>", "cadetblue", "chartreuse", "chocolate", "coral", "cornflowerblue", "cornsilk", "crimson", "cyan", "darkblue", "dark<PERSON>an", "darkgoldenrod", "darkgray", "darkgreen", "<PERSON><PERSON>rey", "<PERSON><PERSON><PERSON>", "darkmagenta", "darkolivegreen", "darkorange", "darkorchid", "darkred", "<PERSON><PERSON><PERSON>", "darkseagreen", "darkslateblue", "darkslategray", "darkslateg<PERSON>", "darkturquoise", "darkviolet", "deeppink", "deepskyblue", "dimgray", "<PERSON><PERSON><PERSON>", "dodgerblue", "firebrick", "<PERSON><PERSON><PERSON><PERSON>", "forestgreen", "fuchsia", "gainsboro", "ghostwhite", "gold", "goldenrod", "gray", "green", "greenyellow", "grey", "honeydew", "hotpink", "indianred", "indigo", "ivory", "khaki", "lavender", "lavenderblush", "lawngreen", "lemon<PERSON>ffon", "lightblue", "lightcoral", "lightcyan", "lightgoldenrodyellow", "lightgray", "lightgreen", "<PERSON><PERSON>rey", "lightpink", "<PERSON><PERSON><PERSON>", "lightseagreen", "lightskyblue", "lightslategray", "lightslategrey", "lightsteelblue", "lightyellow", "lime", "limegreen", "linen", "magenta", "maroon", "mediumaquamarine", "mediumblue", "mediumorchid", "mediumpurple", "mediumseagreen", "mediumslateblue", "mediumspringgreen", "mediumturquoise", "mediumvioletred", "midnightblue", "mintcream", "mistyrose", "moccasin", "navajowhite", "navy", "oldlace", "olive", "<PERSON><PERSON><PERSON>", "orange", "orangered", "orchid", "palegoldenrod", "palegreen", "paleturquoise", "palevioletred", "papayawhip", "peachpuff", "peru", "pink", "plum", "powderblue", "purple", "red", "rosybrown", "royalblue", "saddlebrown", "salmon", "sandybrown", "seagreen", "seashell", "sienna", "silver", "skyblue", "slateblue", "slategray", "<PERSON><PERSON><PERSON>", "snow", "springgreen", "steelblue", "tan", "teal", "thistle", "tomato", "turquoise", "violet", "wheat", "white", "whitesmoke", "yellow", "yellowgreen", "hexNames", "o", "flipped", "hasOwnProperty", "flip", "Converter", "HSLtoRGB", "hsl", "h", "s", "l", "m1", "m2", "rgb", "r", "hueToRGB", "g", "b", "a", "v", "RGBtoHSL", "min", "max", "diff", "add", "RGBtoHEX", "toString", "each", "nr", "join", "HSLtoHEX", "HSVtoHEX", "hsv", "HSVtoRGB", "RGBtoHSV", "c", "x", "abs", "HEXtoRGB", "isNAME", "string", "NAMEtoHEX", "name", "NAMEtoRGB", "hasNAME", "RGBtoNAME", "<PERSON><PERSON><PERSON>", "CSS_UNIT", "PERMISSIVE_MATCH3", "PERMISSIVE_MATCH4", "ColorStrings", "match", "RegExp", "parse", "result", "to", "color", "RGBA", "HSLA", "hsla", "options", "TRANSPARENT", "NAME", "toUpperCase", "String", "includes", "search", "start", "undefined", "extend", "_format", "_matchFormat", "_valid", "init", "fromString", "isNaN", "matchString", "updateFormat", "trim", "matched", "exec", "set"], "mappings": ";;;;;;;CAAA,SAAAA,EAAAC,GACA,GAAA,mBAAAC,QAAAA,OAAAC,IACAD,OAAA,WAAA,UAAA,UAAAD,QACA,GAAA,oBAAAG,QACAH,EAAAG,QAAAC,QAAA,eACA,CACA,IAAAC,GACAF,YAEAH,EAAAK,EAAAF,QAAAJ,EAAAO,QACAP,EAAAQ,QAAAF,EAAAF,SAVA,CAYAK,KAAA,SAAAL,EAAAM,GACA,aA8BA,SAAAC,EAAAC,EAAAC,GACA,KAAAD,aAAAC,GACA,MAAA,IAAAC,UAAA,qCAuCA,SAAAC,EAAAC,GAIA,OAHA,IAAAA,EAAAC,QAAA,OACAD,EAAAA,EAAAE,OAAA,IAEAF,GAGA,IAAAA,EAAAG,SACAH,EAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,IAEA,IAAAA,EAAAG,OAAA,IAAAH,EAAA,MALA,KAQA,SAAAI,EAAAJ,GAYA,OAXA,IAAAA,EAAAC,QAAA,OACAD,EAAAA,EAAAE,OAAA,IAGA,IAAAF,EAAAG,QACAH,EAAA,KAAAA,EAAA,IACAA,EAAA,KAAAA,EAAA,IACAA,EAAA,KAAAA,EAAA,KAEAA,EAAAA,EAAA,GAAAA,EAAA,GAAAA,EAAA,IAEA,IAAAA,EAGA,SAAAK,EAAAC,GACA,OAAAC,SAAAD,EAAA,IAGA,SAAAE,EAAAC,GACA,MAAA,iBAAAA,GAAAA,EAAAR,QAAA,OAAAQ,EAAAN,OAAA,EAGA,SAAAO,EAAAD,GACA,OAAAF,SAAAI,KAAAC,MAAA,KAAAH,EAAAI,MAAA,GAAA,IAAA,IAGA,SAAAC,EAAAL,GACA,OAAAM,WAAAN,EAAAI,MAAA,GAAA,GAAA,IAAA,IA9GAG,OAAAC,eAAA7B,EAAA,cACA8B,OAAA,IAGA,IAAAC,EAEA,SAAAC,GACA,OAAAA,GAAAA,EAAAC,WACAD,GAEAE,QAAAF,GANAG,CAAA7B,GAUA8B,EACA,mBAAAC,QAAA,iBAAAA,OAAAC,SACA,SAAAN,GACA,cAAAA,GAEA,SAAAA,GACA,OAAAA,GACA,mBAAAK,QACAL,EAAAO,cAAAF,QACAL,IAAAK,OAAAG,UACA,gBACAR,GASAS,EAAA,WACA,SAAAC,EAAAC,EAAAC,GACA,IAAA,IAAAC,EAAA,EAAAA,EAAAD,EAAA7B,OAAA8B,IAAA,CACA,IAAAC,EAAAF,EAAAC,GACAC,EAAAC,WAAAD,EAAAC,aAAA,EACAD,EAAAE,cAAA,EACA,UAAAF,IAAAA,EAAAG,UAAA,GACArB,OAAAC,eAAAc,EAAAG,EAAAI,IAAAJ,IAIA,OAAA,SAAArC,EAAA0C,EAAAC,GAGA,OAFAD,GAAAT,EAAAjC,EAAA+B,UAAAW,GACAC,GAAAV,EAAAjC,EAAA2C,GACA3C,GAdA,GAkBA4C,GACAC,QAAA,EACAC,YAAA,EACAC,YAAA,EACAC,aAAA,EACAC,cAEAC,IAAA,OACAC,IAAA,OACAC,IAAA,OACAC,UAAA,QAEAC,gBAAA,MACAC,aAAA,GACAC,wBAAA,GAyDAC,GACAC,UAAA,SACAC,aAAA,SACAC,KAAA,MACAC,WAAA,SACAC,MAAA,SACAC,MAAA,SACAC,OAAA,SACAC,MAAA,MACAC,eAAA,SACAC,KAAA,MACAC,WAAA,SACAC,MAAA,SACAC,UAAA,SACAC,YAAA,SACAC,UAAA,SACAC,WAAA,SACAC,UAAA,SACAC,MAAA,SACAC,eAAA,SACAC,SAAA,SACAC,QAAA,SACAC,KAAA,MACAC,SAAA,SACAC,SAAA,SACAC,cAAA,SACAC,SAAA,SACAC,UAAA,SACAC,SAAA,SACAC,UAAA,SACAC,YAAA,SACAC,eAAA,SACAC,WAAA,SACAC,WAAA,SACAC,QAAA,SACAC,WAAA,SACAC,aAAA,SACAC,cAAA,SACAC,cAAA,SACAC,cAAA,SACAC,cAAA,SACAC,WAAA,SACAC,SAAA,SACAC,YAAA,SACAC,QAAA,SACAC,QAAA,SACAC,WAAA,SACAC,UAAA,SACAC,YAAA,SACAC,YAAA,SACAC,QAAA,MACAC,UAAA,SACAC,WAAA,SACAC,KAAA,SACAC,UAAA,SACAC,KAAA,SACAC,MAAA,SACAC,YAAA,SACAC,KAAA,SACAC,SAAA,SACAC,QAAA,SACAC,UAAA,SACAC,OAAA,SACAC,MAAA,SACAC,MAAA,SACAC,SAAA,SACAC,cAAA,SACAC,UAAA,SACAC,aAAA,SACAC,UAAA,SACAC,WAAA,SACAC,UAAA,SACAC,qBAAA,SACAC,UAAA,SACAC,WAAA,SACAC,UAAA,SACAC,UAAA,SACAC,YAAA,SACAC,cAAA,SACAC,aAAA,SACAC,eAAA,MACAC,eAAA,MACAC,eAAA,SACAC,YAAA,SACAC,KAAA,MACAC,UAAA,SACAC,MAAA,SACAC,QAAA,MACAC,OAAA,SACAC,iBAAA,SACAC,WAAA,SACAC,aAAA,SACAC,aAAA,SACAC,eAAA,SACAC,gBAAA,SACAC,kBAAA,SACAC,gBAAA,SACAC,gBAAA,SACAC,aAAA,SACAC,UAAA,SACAC,UAAA,SACAC,SAAA,SACAC,YAAA,SACAC,KAAA,SACAC,QAAA,SACAC,MAAA,SACAC,UAAA,SACAC,OAAA,SACAC,UAAA,SACAC,OAAA,SACAC,cAAA,SACAC,UAAA,SACAC,cAAA,SACAC,cAAA,SACAC,WAAA,SACAC,UAAA,SACAC,KAAA,SACAC,KAAA,SACAC,KAAA,SACAC,WAAA,SACAC,OAAA,SACAC,IAAA,MACAC,UAAA,SACAC,UAAA,SACAC,YAAA,SACAC,OAAA,SACAC,WAAA,SACAC,SAAA,SACAC,SAAA,SACAC,OAAA,SACAC,OAAA,SACAC,QAAA,SACAC,UAAA,SACAC,UAAA,SACAC,UAAA,SACAC,KAAA,SACAC,YAAA,SACAC,UAAA,SACAC,IAAA,SACAC,KAAA,SACAC,QAAA,SACAC,OAAA,SACAC,UAAA,SACAC,OAAA,SACAC,MAAA,SACAC,MAAA,MACAC,WAAA,SACAC,OAAA,MACAC,YAAA,UAIAC,EAlKA,SAAAC,GACA,IAAAC,KACA,IAAA,IAAA5K,KAAA2K,EACAA,EAAAE,eAAA7K,KACA4K,EAAAD,EAAA3K,IAAAA,GAGA,OAAA4K,EA2JAE,CAAAzJ,GAEA0J,GACAC,SAAA,SAAAC,GACA,IAAAC,EAAAD,EAAAC,EAAA,IACAC,EAAAF,EAAAE,EACAC,EAAAH,EAAAG,EACAC,OAAA,EACAC,OAAA,EACAC,OAAA,EAqBA,OAnBAD,EADAF,GAAA,GACAA,GAAAD,EAAA,GAEAC,EAAAD,EAAAC,EAAAD,EAEAE,EAAA,EAAAD,EAAAE,EACAC,GACAC,EAAAhO,KAAAiO,SAAAJ,EAAAC,EAAAJ,EAAA,EAAA,GACAQ,EAAAlO,KAAAiO,SAAAJ,EAAAC,EAAAJ,GACAS,EAAAnO,KAAAiO,SAAAJ,EAAAC,EAAAJ,EAAA,EAAA,SAEA,IAAAD,EAAAW,IACAL,EAAAK,EAAAX,EAAAW,GAEA,IAAAX,EAAAG,IACAG,EAAAL,EAAAD,EAAAC,GAEA,IAAAD,EAAAG,IACAG,EAAAL,EAAAD,EAAAC,GAEAK,GAGAE,SAAA,SAAAJ,EAAAC,EAAAJ,GACA,IAAAW,OAAA,EAeA,OAdAX,EAAA,EACAA,GAAA,EACAA,EAAA,IACAA,GAAA,GAGAW,EADA,EAAAX,EAAA,EACAG,GAAAC,EAAAD,GAAAH,EAAA,EACA,EAAAA,EAAA,EACAI,EACA,EAAAJ,EAAA,EACAG,GAAAC,EAAAD,IAAA,EAAA,EAAAH,GAAA,EAEAG,EAEA3M,KAAAC,MAAA,IAAAkN,IAGAC,SAAA,SAAAP,GACA,IAAAC,EAAAD,EAAAC,EAAA,IACAE,EAAAH,EAAAG,EAAA,IACAC,EAAAJ,EAAAI,EAAA,IACAI,EAAArN,KAAAqN,IAAAP,EAAAE,EAAAC,GACAK,EAAAtN,KAAAsN,IAAAR,EAAAE,EAAAC,GACAM,EAAAD,EAAAD,EACAG,EAAAF,EAAAD,EACAX,EAAA,GAAAc,EACAhB,OAAA,EACAC,OAAA,EAmBA,OAhBAD,EADAa,IAAAC,EACA,EACAR,IAAAQ,EACA,IAAAN,EAAAC,GAAAM,EAAA,IACAP,IAAAM,EACA,IAAAL,EAAAH,GAAAS,EAAA,IAEA,IAAAT,EAAAE,GAAAO,EAAA,IAGAd,EADA,IAAAc,EACA,EACAb,GAAA,GACAa,EAAAC,EAEAD,GAAA,EAAAC,IAIAhB,EAAAxM,KAAAC,MAAAuM,GAAA,IACAC,EAAAA,EACAC,EAAAA,IAIAe,SAAA,SAAAZ,GACA,IAAAxN,GAAAwN,EAAAC,EAAAY,SAAA,IAAAb,EAAAG,EAAAU,SAAA,IAAAb,EAAAI,EAAAS,SAAA,KAOA,OALAlN,EAAAG,QAAAgN,KAAAtO,EAAA,SAAAuO,EAAAjO,GACA,IAAAA,EAAAH,SACAH,EAAAuO,GAAA,IAAAjO,KAGA,IAAAN,EAAAwO,KAAA,KAGAC,SAAA,SAAAvB,GACA,IAAAM,EAAA/N,KAAAwN,SAAAC,GACA,OAAAzN,KAAA2O,SAAAZ,IAGAkB,SAAA,SAAAC,GACA,IAAAnB,EAAA/N,KAAAmP,SAAAD,GACA,OAAAlP,KAAA2O,SAAAZ,IAGAqB,SAAA,SAAArB,GACA,IAAAC,EAAAD,EAAAC,EAAA,IACAE,EAAAH,EAAAG,EAAA,IACAC,EAAAJ,EAAAI,EAAA,IACAK,EAAAtN,KAAAsN,IAAAR,EAAAE,EAAAC,GACAI,EAAArN,KAAAqN,IAAAP,EAAAE,EAAAC,GACAT,OAAA,EACAC,OAAA,EACAU,EAAAG,EACAC,EAAAD,EAAAD,EAEA,GADAZ,EAAA,IAAAa,EAAA,EAAAC,EAAAD,EACAA,IAAAD,EACAb,EAAA,MACA,CACA,OAAAc,GACA,KAAAR,EACAN,GAAAQ,EAAAC,GAAAM,GAAAP,EAAAC,EAAA,EAAA,GACA,MAEA,KAAAD,EACAR,GAAAS,EAAAH,GAAAS,EAAA,EACA,MAEA,KAAAN,EACAT,GAAAM,EAAAE,GAAAO,EAAA,EAOAf,GAAA,EAGA,OACAA,EAAAxM,KAAAC,MAAA,IAAAuM,GACAC,EAAAA,EACAU,EAAAA,IAIAc,SAAA,SAAAD,GACA,IAAAlB,OAAA,EACAE,OAAA,EACAC,OAAA,EACAT,EAAAwB,EAAAxB,EAAA,IAAA,GACAC,EAAAuB,EAAAvB,EACAU,EAAAa,EAAAb,EACAgB,EAAAhB,EAAAV,EACA2B,EAAAD,GAAA,EAAAnO,KAAAqO,IAAA7B,EAAA,EAAA,IAEAM,EAAAE,EAAAC,EAAAE,EAAAgB,EAGArB,IAAAqB,EAAAC,EAAA,EAAA,EAAAA,EAAAD,GAFA3B,IAAAA,GAGAQ,IAAAoB,EAAAD,EAAAA,EAAAC,EAAA,EAAA,GAAA5B,GACAS,IAAA,EAAA,EAAAmB,EAAAD,EAAAA,EAAAC,GAAA5B,GAEA,IAAAK,GACAC,EAAA9M,KAAAC,MAAA,IAAA6M,GACAE,EAAAhN,KAAAC,MAAA,IAAA+M,GACAC,EAAAjN,KAAAC,MAAA,IAAAgN,IAeA,YAZA,IAAAe,EAAAd,IACAL,EAAAK,EAAAc,EAAAd,GAGA,IAAAc,EAAAb,IACAN,EAAAL,EAAAwB,EAAAxB,GAGA,IAAAwB,EAAAb,GAAA,IAAAa,EAAAvB,IACAI,EAAAL,EAAAwB,EAAAxB,GAGAK,GAGAyB,SAAA,SAAAjP,GAIA,OAHA,IAAAA,EAAAG,SACAH,EAAAD,EAAAC,KAGAyN,EAAApN,EAAAL,EAAAE,OAAA,EAAA,IACAyN,EAAAtN,EAAAL,EAAAE,OAAA,EAAA,IACA0N,EAAAvN,EAAAL,EAAAE,OAAA,EAAA,MAIAgP,OAAA,SAAAC,GACA,QAAA7L,EAAAwJ,eAAAqC,IAMAC,UAAA,SAAAC,GACA,OAAA/L,EAAAwJ,eAAAuC,GACA,IAAA/L,EAAA+L,GAEA,MAGAC,UAAA,SAAAD,GACA,IAAArP,EAAAP,KAAA2P,UAAAC,GAEA,OAAArP,EACAP,KAAAwP,SAAAjP,GAEA,MAGAuP,QAAA,SAAA/B,GACA,IAAAxN,EAAAP,KAAA2O,SAAAZ,GAQA,OAJA,KAFAxN,EAAAI,EAAAJ,IAEAC,QAAA,OACAD,EAAAA,EAAAE,OAAA,MAGAyM,EAAAG,eAAA9M,IACA2M,EAAA3M,IAKAwP,UAAA,SAAAhC,GACA,IAAAiC,EAAAhQ,KAAA8P,QAAA/B,GACA,OAAAiC,GAIA,OAMAC,EAAA,6CAEAC,EACA,cACAD,EACA,aACAA,EACA,aACAA,EACA,WACAE,EACA,cACAF,EACA,aACAA,EACA,aACAA,EACA,aACAA,EACA,WAEAG,GACA9M,KACA+M,MAAA,IAAAC,OAAA,OAAAJ,EAAA,IAAA,KACAK,MAAA,SAAAC,GACA,OACAxC,EAAAjN,EAAAyP,EAAA,IACAvP,EAAAuP,EAAA,IACA1P,SAAA0P,EAAA,GAAA,IACAtC,EAAAnN,EAAAyP,EAAA,IACAvP,EAAAuP,EAAA,IACA1P,SAAA0P,EAAA,GAAA,IACArC,EAAApN,EAAAyP,EAAA,IACAvP,EAAAuP,EAAA,IACA1P,SAAA0P,EAAA,GAAA,IACApC,EAAA,IAGAqC,GAAA,SAAAC,GACA,MAAA,OAAAA,EAAA1C,EAAA,KAAA0C,EAAAxC,EAAA,KAAAwC,EAAAvC,EAAA,MAGAwC,MACAN,MAAA,IAAAC,OAAA,QAAAH,EAAA,IAAA,KACAI,MAAA,SAAAC,GACA,OACAxC,EAAAjN,EAAAyP,EAAA,IACAvP,EAAAuP,EAAA,IACA1P,SAAA0P,EAAA,GAAA,IACAtC,EAAAnN,EAAAyP,EAAA,IACAvP,EAAAuP,EAAA,IACA1P,SAAA0P,EAAA,GAAA,IACArC,EAAApN,EAAAyP,EAAA,IACAvP,EAAAuP,EAAA,IACA1P,SAAA0P,EAAA,GAAA,IACApC,EAAArN,EAAAyP,EAAA,IACAnP,EAAAmP,EAAA,IACAlP,WAAAkP,EAAA,GAAA,MAGAC,GAAA,SAAAC,GACA,MACA,QACAA,EAAA1C,EACA,KACA0C,EAAAxC,EACA,KACAwC,EAAAvC,EACA,KACAuC,EAAAtC,EACA,MAIA7K,KACA8M,MAAA,IAAAC,OAAA,OAAAJ,EAAA,IAAA,KACAK,MAAA,SAAAC,GACA,IAAA/C,GACAC,GAAA8C,EAAA,GAAA,IAAA,KAAA,IACA7C,EAAA5M,EAAAyP,EAAA,IACAnP,EAAAmP,EAAA,IACAlP,WAAAkP,EAAA,GAAA,IACA5C,EAAA7M,EAAAyP,EAAA,IACAnP,EAAAmP,EAAA,IACAlP,WAAAkP,EAAA,GAAA,IACApC,EAAA,GAGA,OAAAb,EAAAC,SAAAC,IAEAgD,GAAA,SAAAC,GACA,IAAAjD,EAAAF,EAAAe,SAAAoC,GACA,MACA,OACA5P,SAAA2M,EAAAC,EAAA,IACA,KACAxM,KAAAC,MAAA,IAAAsM,EAAAE,GACA,MACAzM,KAAAC,MAAA,IAAAsM,EAAAG,GACA,OAIAgD,MACAP,MAAA,IAAAC,OAAA,QAAAH,EAAA,IAAA,KACAI,MAAA,SAAAC,GACA,IAAAK,GACAnD,GAAA8C,EAAA,GAAA,IAAA,KAAA,IACA7C,EAAA5M,EAAAyP,EAAA,IACAnP,EAAAmP,EAAA,IACAlP,WAAAkP,EAAA,GAAA,IACA5C,EAAA7M,EAAAyP,EAAA,IACAnP,EAAAmP,EAAA,IACAlP,WAAAkP,EAAA,GAAA,IACApC,EAAArN,EAAAyP,EAAA,IACAnP,EAAAmP,EAAA,IACAlP,WAAAkP,EAAA,GAAA,KAGA,OAAAjD,EAAAC,SAAAqD,IAEAJ,GAAA,SAAAC,GACA,IAAAjD,EAAAF,EAAAe,SAAAoC,GACA,MACA,QACA5P,SAAA2M,EAAAC,EAAA,IACA,KACAxM,KAAAC,MAAA,IAAAsM,EAAAE,GACA,MACAzM,KAAAC,MAAA,IAAAsM,EAAAG,GACA,MACA8C,EAAAtC,EACA,MAIA5K,KACA6M,MAAA,gCACAE,MAAA,SAAAC,GACA,IAAAjQ,EAAAiQ,EAAA,GACAzC,EAAAR,EAAAiC,SAAAjP,GACA,OACAyN,EAAAD,EAAAC,EACAE,EAAAH,EAAAG,EACAC,EAAAJ,EAAAI,EACAC,EAAA,IAGAqC,GAAA,SAAAC,EAAAvQ,GACA,IAAAI,EAAAgN,EAAAoB,SAAA+B,GAEA,GAAAvQ,EAAA,CACA,GAAAA,EAAA2Q,QAAA3N,WAAA,CACA,IAAA6M,EAAAzC,EAAAuC,QAAAY,GACA,GAAAV,EACA,OAAAA,EAGA7P,EAAA2Q,QAAA5N,aACA3C,EAAAI,EAAAJ,IAGA,MAAA,GAAAA,IAGAwQ,aACAV,MAAA,iBACAE,MAAA,WACA,OACAvC,EAAA,EACAE,EAAA,EACAC,EAAA,EACAC,EAAA,IAGAqC,GAAA,WACA,MAAA,gBAGAO,MACAX,MAAA,SACAE,MAAA,SAAAC,GACA,IAAAzC,EAAAR,EAAAsC,UAAAW,EAAA,IACA,OAAAzC,GAEAC,EAAAD,EAAAC,EACAE,EAAAH,EAAAG,EACAC,EAAAJ,EAAAI,EACAC,EAAA,GAIA,MAEAqC,GAAA,SAAAC,EAAAvQ,GACA,IAAAyP,EAAArC,EAAAwC,UAAAW,GAEA,OAAAd,GAIAQ,EAAAjQ,EAAA2Q,QAAApN,gBAAAuN,eAAAR,GACAC,MAOAQ,OAAA/O,UAAAgP,WACAD,OAAA/O,UAAAgP,SAAA,SAAAC,EAAAC,GAOA,MAJA,iBAAAA,IACAA,EAAA,KAGAA,EAAAD,EAAA1Q,OAAAV,KAAAU,UAGA,IAAAV,KAAAQ,QAAA4Q,EAAAC,KAIA,IAAAtR,EAAA,WACA,SAAAA,EAAA2P,EAAAoB,GACA5Q,EAAAF,KAAAD,GAIA,iBADA,IAAA2P,EAAA,YAAA3N,EAAA2N,UAEA,IAAAoB,IAEAA,EAAApB,EACAA,OAAA4B,GAEA,iBAAAR,IACAA,GACA7N,OAAA6N,IAGA9Q,KAAA8Q,QAAApP,EAAAG,QAAA0P,QAAA,KAAAvO,EAAA8N,GACA9Q,KAAAyB,OACAuM,EAAA,EACAE,EAAA,EACAC,EAAA,EACAT,EAAA,EACAC,EAAA,EACAU,EAAA,EACAD,EAAA,GAEApO,KAAAwR,SAAA,EACAxR,KAAAyR,aAAA,MACAzR,KAAA0R,QAAA,EAEA1R,KAAA2R,KAAAjC,UAGAtN,EACArC,IAGA8C,IAAA,OACApB,MAAA,SAAAiO,GAGA,OAFA1P,KAAAiD,OAAAjD,KAAA8Q,QAAA7N,QACAjD,KAAA4R,WAAAlC,GACA1P,QAIA6C,IAAA,UACApB,MAAA,WACA,OAAAzB,KAAA0R,UAIA7O,IAAA,MACApB,MAAA,SAAAA,GACA,YAAA,IAAAA,EACAzB,KAAA4O,YAEA5O,KAAA4R,WAAAnQ,GACAzB,SAIA6C,IAAA,QACApB,MAAA,SAAAA,GACA,YAAA,IAAAA,GAAAoQ,MAAApQ,GACAzB,KAAAyB,MAAA2M,IAGA3M,EAAAH,WAAAG,IAEA,EACAA,EAAA,EACAA,EAAA,IACAA,EAAA,GAEAzB,KAAAyB,MAAA2M,EAAA3M,EACAzB,SAIA6C,IAAA,cACApB,MAAA,SAAAiO,GACA,OAAA3P,EAAA+R,YAAApC,MAIA7M,IAAA,aACApB,MAAA,SAAAiO,EAAAqC,GACA,GAAA,iBAAArC,EAAA,CACAA,EAAAhO,EAAAG,QAAAmQ,KAAAtC,GACA,IAAAuC,EAAA,KACAlE,OAAA,EACA/N,KAAA0R,QAAA,EACA,IAAA,IAAAlP,KAAA4N,EACA,GAAA,QAAA6B,EAAA7B,EAAA5N,GAAA6N,MAAA6B,KAAAxC,MACA3B,EAAAqC,EAAA5N,GAAA+N,MAAA0B,IAEA,CACAjS,KAAAmS,IAAApE,GACA,gBAAAvL,IACAA,EAAA,OAEAxC,KAAAyR,aAAAjP,GACA,IAAAuP,GACA/R,KAAAiD,OAAAT,GAEA,WAOA,iBAFA,IAAAkN,EACA,YACA3N,EAAA2N,KAEA1P,KAAAmS,IAAAzC,GAEA,OAAA1P,QAIA6C,IAAA,SACApB,MAAA,SAAA+P,GAcA,MAZA,iBAAAA,IACAA,EAAAA,EAAAP,qBACA,IAAAb,EAAAoB,GAGAxR,KAAAwR,QADA,gBAAAA,EACAA,EAEA,OAEA,IAAAA,IACAxR,KAAAwR,SAAA,IAEA,IAAAxR,KAAAwR,QACAxR,KAAAyR,aAEAzR,KAAAwR,WAIA3O,IAAA,SACApB,MAAA,WACA,OAAA2O,EAAAO,KAAAF,GAAAzQ,KAAAyB,MAAAzB,SAIA6C,IAAA,QACApB,MAAA,WACA,OAAA2O,EAAA9M,IAAAmN,GAAAzQ,KAAAyB,MAAAzB,SAIA6C,IAAA,SACApB,MAAA,WACA,OAAA2O,EAAAQ,KAAAH,GAAAzQ,KAAAyB,MAAAzB,SAIA6C,IAAA,QACApB,MAAA,WACA,OAAA2O,EAAA7M,IAAAkN,GAAAzQ,KAAAyB,MAAAzB,SAIA6C,IAAA,QACApB,MAAA,WACA,OAAA2O,EAAA5M,IAAAiN,GAAAzQ,KAAAyB,MAAAzB,SAIA6C,IAAA,SACApB,MAAA,WACA,OAAA2O,EAAAY,KAAAP,GAAAzQ,KAAAyB,MAAAzB,SAIA6C,IAAA,KACApB,MAAA,SAAAwB,GACA,MACA,iBAAAA,IACAA,EAAAA,EAAAgO,qBACA,IAAAb,EAAAnN,GAEAmN,EAAAnN,GAAAwN,GAAAzQ,KAAAyB,MAAAzB,MAEAA,KAAA4O,cAIA/L,IAAA,WACApB,MAAA,WACA,IAAAA,EAAAzB,KAAAyB,MACA,IAAAzB,KAAA0R,QAGA,iBAFAjQ,EAAAzB,KAAA8Q,QAAAnN,cAGA,OAAAlC,EAIA,GAAA,IAAAA,EAAA2M,GAAApO,KAAA8Q,QAAAlN,uBACA,OAAAwM,EAAAW,YAAAN,GAAAhP,EAAAzB,MAGA,IAAAiD,OAAA,EAOA,GALAA,GADA,IAAAjD,KAAAwR,QACAxR,KAAAyR,aAEAzR,KAAAwR,QAGAxR,KAAA8Q,QAAA1N,aAAA,IAAA3B,EAAA2M,EACA,OAAAnL,GACA,IAAA,OACAA,EAAA,MACA,MACA,IAAA,OACAA,EAAA,q+BA5NA", "file": "jquery-asColor.min.js", "sourcesContent": ["/**\n* jQuery asColor v0.3.6\n* https://github.com/amazingSurge/asColor\n*\n* Copyright (c) amazingSurge\n* Released under the LGPL-3.0 license\n*/\nimport $ from 'jquery';\n\nvar DEFAULTS = {\n  format: false,\n  shortenHex: false,\n  hexUseName: false,\n  reduceAlpha: false,\n  alphaConvert: { // or false will disable convert\n    'RGB': 'RGBA',\n    'HSL': 'HSLA',\n    'HEX': 'RGBA',\n    'NAMESPACE': 'RGBA',\n  },\n  nameDegradation: 'HEX',\n  invalidValue: '',\n  zeroAlphaAsTransparent: true\n};\n\nfunction expandHex(hex) {\n  if (hex.indexOf('#') === 0) {\n    hex = hex.substr(1);\n  }\n  if (!hex) {\n    return null;\n  }\n  if (hex.length === 3) {\n    hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];\n  }\n  return hex.length === 6 ? `#${hex}` : null;\n}\n\nfunction shrinkHex(hex) {\n  if (hex.indexOf('#') === 0) {\n    hex = hex.substr(1);\n  }\n  if (hex.length === 6 && hex[0] === hex[1] && hex[2] === hex[3] && hex[4] === hex[5]) {\n    hex = hex[0] + hex[2] + hex[4];\n  }\n  return `#${hex}`;\n}\n\nfunction parseIntFromHex(val) {\n  return parseInt(val, 16);\n}\n\nfunction isPercentage(n) {\n  return typeof n === 'string' && n.indexOf('%') === n.length - 1;\n}\n\nfunction conventPercentageToRgb(n) {\n  return parseInt(Math.round(n.slice(0, -1) * 2.55), 10);\n}\n\nfunction convertPercentageToFloat(n) {\n  return parseFloat(n.slice(0, -1) / 100, 10);\n}\n\nfunction flip(o) {\n  const flipped = {};\n  for (const i in o) {\n    if (o.hasOwnProperty(i)) {\n      flipped[o[i]] = i;\n    }\n  }\n  return flipped;\n}\n\nvar NAMES = {\n  aliceblue: 'f0f8ff',\n  antiquewhite: 'faebd7',\n  aqua: '0ff',\n  aquamarine: '7fffd4',\n  azure: 'f0ffff',\n  beige: 'f5f5dc',\n  bisque: 'ffe4c4',\n  black: '000',\n  blanchedalmond: 'ffebcd',\n  blue: '00f',\n  blueviolet: '8a2be2',\n  brown: 'a52a2a',\n  burlywood: 'deb887',\n  burntsienna: 'ea7e5d',\n  cadetblue: '5f9ea0',\n  chartreuse: '7fff00',\n  chocolate: 'd2691e',\n  coral: 'ff7f50',\n  cornflowerblue: '6495ed',\n  cornsilk: 'fff8dc',\n  crimson: 'dc143c',\n  cyan: '0ff',\n  darkblue: '00008b',\n  darkcyan: '008b8b',\n  darkgoldenrod: 'b8860b',\n  darkgray: 'a9a9a9',\n  darkgreen: '006400',\n  darkgrey: 'a9a9a9',\n  darkkhaki: 'bdb76b',\n  darkmagenta: '8b008b',\n  darkolivegreen: '556b2f',\n  darkorange: 'ff8c00',\n  darkorchid: '9932cc',\n  darkred: '8b0000',\n  darksalmon: 'e9967a',\n  darkseagreen: '8fbc8f',\n  darkslateblue: '483d8b',\n  darkslategray: '2f4f4f',\n  darkslategrey: '2f4f4f',\n  darkturquoise: '00ced1',\n  darkviolet: '9400d3',\n  deeppink: 'ff1493',\n  deepskyblue: '00bfff',\n  dimgray: '696969',\n  dimgrey: '696969',\n  dodgerblue: '1e90ff',\n  firebrick: 'b22222',\n  floralwhite: 'fffaf0',\n  forestgreen: '228b22',\n  fuchsia: 'f0f',\n  gainsboro: 'dcdcdc',\n  ghostwhite: 'f8f8ff',\n  gold: 'ffd700',\n  goldenrod: 'daa520',\n  gray: '808080',\n  green: '008000',\n  greenyellow: 'adff2f',\n  grey: '808080',\n  honeydew: 'f0fff0',\n  hotpink: 'ff69b4',\n  indianred: 'cd5c5c',\n  indigo: '4b0082',\n  ivory: 'fffff0',\n  khaki: 'f0e68c',\n  lavender: 'e6e6fa',\n  lavenderblush: 'fff0f5',\n  lawngreen: '7cfc00',\n  lemonchiffon: 'fffacd',\n  lightblue: 'add8e6',\n  lightcoral: 'f08080',\n  lightcyan: 'e0ffff',\n  lightgoldenrodyellow: 'fafad2',\n  lightgray: 'd3d3d3',\n  lightgreen: '90ee90',\n  lightgrey: 'd3d3d3',\n  lightpink: 'ffb6c1',\n  lightsalmon: 'ffa07a',\n  lightseagreen: '20b2aa',\n  lightskyblue: '87cefa',\n  lightslategray: '789',\n  lightslategrey: '789',\n  lightsteelblue: 'b0c4de',\n  lightyellow: 'ffffe0',\n  lime: '0f0',\n  limegreen: '32cd32',\n  linen: 'faf0e6',\n  magenta: 'f0f',\n  maroon: '800000',\n  mediumaquamarine: '66cdaa',\n  mediumblue: '0000cd',\n  mediumorchid: 'ba55d3',\n  mediumpurple: '9370db',\n  mediumseagreen: '3cb371',\n  mediumslateblue: '7b68ee',\n  mediumspringgreen: '00fa9a',\n  mediumturquoise: '48d1cc',\n  mediumvioletred: 'c71585',\n  midnightblue: '191970',\n  mintcream: 'f5fffa',\n  mistyrose: 'ffe4e1',\n  moccasin: 'ffe4b5',\n  navajowhite: 'ffdead',\n  navy: '000080',\n  oldlace: 'fdf5e6',\n  olive: '808000',\n  olivedrab: '6b8e23',\n  orange: 'ffa500',\n  orangered: 'ff4500',\n  orchid: 'da70d6',\n  palegoldenrod: 'eee8aa',\n  palegreen: '98fb98',\n  paleturquoise: 'afeeee',\n  palevioletred: 'db7093',\n  papayawhip: 'ffefd5',\n  peachpuff: 'ffdab9',\n  peru: 'cd853f',\n  pink: 'ffc0cb',\n  plum: 'dda0dd',\n  powderblue: 'b0e0e6',\n  purple: '800080',\n  red: 'f00',\n  rosybrown: 'bc8f8f',\n  royalblue: '4169e1',\n  saddlebrown: '8b4513',\n  salmon: 'fa8072',\n  sandybrown: 'f4a460',\n  seagreen: '2e8b57',\n  seashell: 'fff5ee',\n  sienna: 'a0522d',\n  silver: 'c0c0c0',\n  skyblue: '87ceeb',\n  slateblue: '6a5acd',\n  slategray: '708090',\n  slategrey: '708090',\n  snow: 'fffafa',\n  springgreen: '00ff7f',\n  steelblue: '4682b4',\n  tan: 'd2b48c',\n  teal: '008080',\n  thistle: 'd8bfd8',\n  tomato: 'ff6347',\n  turquoise: '40e0d0',\n  violet: 'ee82ee',\n  wheat: 'f5deb3',\n  white: 'fff',\n  whitesmoke: 'f5f5f5',\n  yellow: 'ff0',\n  yellowgreen: '9acd32'\n};\n\n/* eslint no-bitwise: \"off\" */\nconst hexNames = flip(NAMES);\n\nvar Converter = {\n  HSLtoRGB: function(hsl) {\n    const h = hsl.h / 360;\n    const s = hsl.s;\n    const l = hsl.l;\n    let m1;\n    let m2;\n    let rgb;\n    if (l <= 0.5) {\n      m2 = l * (s + 1);\n    } else {\n      m2 = l + s - (l * s);\n    }\n    m1 = l * 2 - m2;\n    rgb = {\n      r: this.hueToRGB(m1, m2, h + 1 / 3),\n      g: this.hueToRGB(m1, m2, h),\n      b: this.hueToRGB(m1, m2, h - 1 / 3)\n    };\n    if (typeof hsl.a !== 'undefined') {\n      rgb.a = hsl.a;\n    }\n    if (hsl.l === 0) {\n      rgb.h = hsl.h;\n    }\n    if (hsl.l === 1) {\n      rgb.h = hsl.h;\n    }\n    return rgb;\n  },\n\n  hueToRGB: function(m1, m2, h) {\n    let v;\n    if (h < 0) {\n      h += 1;\n    } else if (h > 1) {\n      h -= 1;\n    }\n    if ((h * 6) < 1) {\n      v = m1 + (m2 - m1) * h * 6;\n    } else if ((h * 2) < 1) {\n      v = m2;\n    } else if ((h * 3) < 2) {\n      v = m1 + (m2 - m1) * (2 / 3 - h) * 6;\n    } else {\n      v = m1;\n    }\n    return Math.round(v * 255);\n  },\n\n  RGBtoHSL: function(rgb) {\n    const r = rgb.r / 255;\n    const g = rgb.g / 255;\n    const b = rgb.b / 255;\n    const min = Math.min(r, g, b);\n    const max = Math.max(r, g, b);\n    const diff = max - min;\n    const add = max + min;\n    const l = add * 0.5;\n    let h;\n    let s;\n\n    if (min === max) {\n      h = 0;\n    } else if (r === max) {\n      h = (60 * (g - b) / diff) + 360;\n    } else if (g === max) {\n      h = (60 * (b - r) / diff) + 120;\n    } else {\n      h = (60 * (r - g) / diff) + 240;\n    }\n    if (diff === 0) {\n      s = 0;\n    } else if (l <= 0.5) {\n      s = diff / add;\n    } else {\n      s = diff / (2 - add);\n    }\n\n    return {\n      h: Math.round(h) % 360,\n      s,\n      l\n    };\n  },\n\n  RGBtoHEX: function(rgb) {\n    let hex = [rgb.r.toString(16), rgb.g.toString(16), rgb.b.toString(16)];\n\n    $.each(hex, (nr, val) => {\n      if (val.length === 1) {\n        hex[nr] = `0${val}`;\n      }\n    });\n    return `#${hex.join('')}`;\n  },\n\n  HSLtoHEX: function(hsl) {\n    const rgb = this.HSLtoRGB(hsl);\n    return this.RGBtoHEX(rgb);\n  },\n\n  HSVtoHEX: function(hsv) {\n    const rgb = this.HSVtoRGB(hsv);\n    return this.RGBtoHEX(rgb);\n  },\n\n  RGBtoHSV: function(rgb) {\n    const r = rgb.r / 255;\n    const g = rgb.g / 255;\n    const b = rgb.b / 255;\n    const max = Math.max(r, g, b);\n    const min = Math.min(r, g, b);\n    let h;\n    let s;\n    const v = max;\n    const diff = max - min;\n    s = (max === 0) ? 0 : diff / max;\n    if (max === min) {\n      h = 0;\n    } else {\n      switch (max) {\n        case r: {\n          h = (g - b) / diff + (g < b ? 6 : 0);\n          break;\n        }\n        case g: {\n          h = (b - r) / diff + 2;\n          break;\n        }\n        case b: {\n          h = (r - g) / diff + 4;\n          break;\n        }\n        default: {\n          break;\n        }\n      }\n      h /= 6;\n    }\n\n    return {\n      h: Math.round(h * 360),\n      s,\n      v\n    };\n  },\n\n  HSVtoRGB: function(hsv) {\n    let r;\n    let g;\n    let b;\n    let h = (hsv.h % 360) / 60;\n    const s = hsv.s;\n    const v = hsv.v;\n    const c = v * s;\n    const x = c * (1 - Math.abs(h % 2 - 1));\n\n    r = g = b = v - c;\n    h = ~~h;\n\n    r += [c, x, 0, 0, x, c][h];\n    g += [x, c, c, x, 0, 0][h];\n    b += [0, 0, x, c, c, x][h];\n\n    let rgb = {\n      r: Math.round(r * 255),\n      g: Math.round(g * 255),\n      b: Math.round(b * 255)\n    };\n\n    if (typeof hsv.a !== 'undefined') {\n      rgb.a = hsv.a;\n    }\n\n    if (hsv.v === 0) {\n      rgb.h = hsv.h;\n    }\n\n    if (hsv.v === 1 && hsv.s === 0) {\n      rgb.h = hsv.h;\n    }\n\n    return rgb;\n  },\n\n  HEXtoRGB: function(hex) {\n    if (hex.length === 4) {\n      hex = expandHex(hex);\n    }\n    return {\n      r: parseIntFromHex(hex.substr(1, 2)),\n      g: parseIntFromHex(hex.substr(3, 2)),\n      b: parseIntFromHex(hex.substr(5, 2))\n    };\n  },\n\n  isNAME: function(string) {\n    if (NAMES.hasOwnProperty(string)) {\n      return true;\n    }\n    return false;\n  },\n\n  NAMEtoHEX: function(name) {\n    if (NAMES.hasOwnProperty(name)) {\n      return `#${NAMES[name]}`;\n    }\n    return null;\n  },\n\n  NAMEtoRGB: function(name) {\n    const hex = this.NAMEtoHEX(name);\n\n    if (hex) {\n      return this.HEXtoRGB(hex);\n    }\n    return null;\n  },\n\n  hasNAME: function(rgb) {\n    let hex = this.RGBtoHEX(rgb);\n\n    hex = shrinkHex(hex);\n\n    if (hex.indexOf('#') === 0) {\n      hex = hex.substr(1);\n    }\n\n    if (hexNames.hasOwnProperty(hex)) {\n      return hexNames[hex];\n    }\n    return false;\n  },\n\n  RGBtoNAME: function(rgb) {\n    const hasName = this.hasNAME(rgb);\n    if (hasName) {\n      return hasName;\n    }\n\n    return null;\n  }\n};\n\nconst CSS_INTEGER = '[-\\\\+]?\\\\d+%?';\nconst CSS_NUMBER = '[-\\\\+]?\\\\d*\\\\.\\\\d+%?';\nconst CSS_UNIT = `(?:${CSS_NUMBER})|(?:${CSS_INTEGER})`;\n\nconst PERMISSIVE_MATCH3 = `[\\\\s|\\\\(]+(${CSS_UNIT})[,|\\\\s]+(${CSS_UNIT})[,|\\\\s]+(${CSS_UNIT})\\\\s*\\\\)`;\nconst PERMISSIVE_MATCH4 = `[\\\\s|\\\\(]+(${CSS_UNIT})[,|\\\\s]+(${CSS_UNIT})[,|\\\\s]+(${CSS_UNIT})[,|\\\\s]+(${CSS_UNIT})\\\\s*\\\\)`;\n\nconst ColorStrings = {\n  RGB: {\n    match: new RegExp(`^rgb${PERMISSIVE_MATCH3}$`, 'i'),\n    parse: function(result) {\n      return {\n        r: isPercentage(result[1]) ? conventPercentageToRgb(result[1]) : parseInt(result[1], 10),\n        g: isPercentage(result[2]) ? conventPercentageToRgb(result[2]) : parseInt(result[2], 10),\n        b: isPercentage(result[3]) ? conventPercentageToRgb(result[3]) : parseInt(result[3], 10),\n        a: 1\n      };\n    },\n    to: function(color) {\n      return `rgb(${color.r}, ${color.g}, ${color.b})`;\n    }\n  },\n  RGBA: {\n    match: new RegExp(`^rgba${PERMISSIVE_MATCH4}$`, 'i'),\n    parse: function(result) {\n      return {\n        r: isPercentage(result[1]) ? conventPercentageToRgb(result[1]) : parseInt(result[1], 10),\n        g: isPercentage(result[2]) ? conventPercentageToRgb(result[2]) : parseInt(result[2], 10),\n        b: isPercentage(result[3]) ? conventPercentageToRgb(result[3]) : parseInt(result[3], 10),\n        a: isPercentage(result[4]) ? convertPercentageToFloat(result[4]) : parseFloat(result[4], 10)\n      };\n    },\n    to: function(color) {\n      return `rgba(${color.r}, ${color.g}, ${color.b}, ${color.a})`;\n    }\n  },\n  HSL: {\n    match: new RegExp(`^hsl${PERMISSIVE_MATCH3}$`, 'i'),\n    parse: function(result) {\n      const hsl = {\n        h: ((result[1] % 360) + 360) % 360,\n        s: isPercentage(result[2]) ? convertPercentageToFloat(result[2]) : parseFloat(result[2], 10),\n        l: isPercentage(result[3]) ? convertPercentageToFloat(result[3]) : parseFloat(result[3], 10),\n        a: 1\n      };\n\n      return Converter.HSLtoRGB(hsl);\n    },\n    to: function(color) {\n      const hsl = Converter.RGBtoHSL(color);\n      return `hsl(${parseInt(hsl.h, 10)}, ${Math.round(hsl.s * 100)}%, ${Math.round(hsl.l * 100)}%)`;\n    }\n  },\n  HSLA: {\n    match: new RegExp(`^hsla${PERMISSIVE_MATCH4}$`, 'i'),\n    parse: function(result) {\n      const hsla = {\n        h: ((result[1] % 360) + 360) % 360,\n        s: isPercentage(result[2]) ? convertPercentageToFloat(result[2]) : parseFloat(result[2], 10),\n        l: isPercentage(result[3]) ? convertPercentageToFloat(result[3]) : parseFloat(result[3], 10),\n        a: isPercentage(result[4]) ? convertPercentageToFloat(result[4]) : parseFloat(result[4], 10)\n      };\n\n      return Converter.HSLtoRGB(hsla);\n    },\n    to: function(color) {\n      const hsl = Converter.RGBtoHSL(color);\n      return `hsla(${parseInt(hsl.h, 10)}, ${Math.round(hsl.s * 100)}%, ${Math.round(hsl.l * 100)}%, ${color.a})`;\n    }\n  },\n  HEX: {\n    match: /^#([a-f0-9]{6}|[a-f0-9]{3})$/i,\n    parse: function(result) {\n      const hex = result[0];\n      const rgb = Converter.HEXtoRGB(hex);\n      return {\n        r: rgb.r,\n        g: rgb.g,\n        b: rgb.b,\n        a: 1\n      };\n    },\n    to: function(color, instance) {\n      let hex = Converter.RGBtoHEX(color);\n\n      if (instance) {\n        if (instance.options.hexUseName) {\n          const hasName = Converter.hasNAME(color);\n          if (hasName) {\n            return hasName;\n          }\n        }\n        if (instance.options.shortenHex) {\n          hex = shrinkHex(hex);\n        }\n      }\n      return `${hex}`;\n    }\n  },\n  TRANSPARENT: {\n    match: /^transparent$/i,\n    parse: function() {\n      return {\n        r: 0,\n        g: 0,\n        b: 0,\n        a: 0\n      };\n    },\n    to: function() {\n      return 'transparent';\n    }\n  },\n  NAME: {\n    match: /^\\w+$/i,\n    parse: function(result) {\n      const rgb = Converter.NAMEtoRGB(result[0]);\n      if(rgb) {\n        return {\n          r: rgb.r,\n          g: rgb.g,\n          b: rgb.b,\n          a: 1\n        };\n      }\n\n      return null;\n    },\n    to: function(color, instance) {\n      let name = Converter.RGBtoNAME(color);\n\n      if(name) {\n        return name;\n      }\n\n      return ColorStrings[instance.options.nameDegradation.toUpperCase()].to(color);\n    }\n  }\n};\n\n/* eslint no-extend-native: \"off\" */\nif (!String.prototype.includes) {\n  String.prototype.includes = function(search, start) {\n    'use strict';\n    if (typeof start !== 'number') {\n      start = 0;\n    }\n\n    if (start + search.length > this.length) {\n      return false;\n    }\n    return this.indexOf(search, start) !== -1;\n  };\n}\n\nclass AsColor {\n  constructor(string, options) {\n    if (typeof string === 'object' && typeof options === 'undefined') {\n      options = string;\n      string = undefined;\n    }\n    if(typeof options === 'string'){\n      options = {\n        format: options\n      };\n    }\n    this.options = $.extend(true, {}, DEFAULTS, options);\n    this.value = {\n      r: 0,\n      g: 0,\n      b: 0,\n      h: 0,\n      s: 0,\n      v: 0,\n      a: 1\n    };\n    this._format = false;\n    this._matchFormat = 'HEX';\n    this._valid = true;\n\n    this.init(string);\n  }\n\n  init(string) {\n    this.format(this.options.format);\n    this.fromString(string);\n    return this;\n  }\n\n  isValid() {\n    return this._valid;\n  }\n\n  val(value) {\n    if (typeof value === 'undefined') {\n      return this.toString();\n    }\n    this.fromString(value);\n    return this;\n  }\n\n  alpha(value) {\n    if (typeof value === 'undefined' || isNaN(value)) {\n      return this.value.a;\n    }\n\n    value = parseFloat(value);\n\n    if (value > 1) {\n      value = 1;\n    } else if (value < 0) {\n      value = 0;\n    }\n    this.value.a = value;\n    return this;\n  }\n\n  matchString(string) {\n    return AsColor.matchString(string);\n  }\n\n  fromString(string, updateFormat) {\n    if (typeof string === 'string') {\n      string = $.trim(string);\n      let matched = null;\n      let rgb;\n      this._valid = false;\n      for (let i in ColorStrings) {\n        if ((matched = ColorStrings[i].match.exec(string)) !== null) {\n          rgb = ColorStrings[i].parse(matched);\n\n          if (rgb) {\n            this.set(rgb);\n            if(i === 'TRANSPARENT'){\n              i = 'HEX';\n            }\n            this._matchFormat = i;\n            if (updateFormat === true) {\n              this.format(i);\n            }\n            break;\n          }\n        }\n      }\n    } else if (typeof string === 'object') {\n      this.set(string);\n    }\n    return this;\n  }\n\n  format(format) {\n    if (typeof format === 'string' && (format = format.toUpperCase()) && typeof ColorStrings[format] !== 'undefined') {\n      if (format !== 'TRANSPARENT') {\n        this._format = format;\n      } else {\n        this._format = 'HEX';\n      }\n    } else if(format === false) {\n      this._format = false;\n    }\n    if(this._format === false){\n      return this._matchFormat;\n    }\n    return this._format;\n  }\n\n  toRGBA() {\n    return ColorStrings.RGBA.to(this.value, this);\n  }\n\n  toRGB() {\n    return ColorStrings.RGB.to(this.value, this);\n  }\n\n  toHSLA() {\n    return ColorStrings.HSLA.to(this.value, this);\n  }\n\n  toHSL() {\n    return ColorStrings.HSL.to(this.value, this);\n  }\n\n  toHEX() {\n    return ColorStrings.HEX.to(this.value, this);\n  }\n\n  toNAME() {\n    return ColorStrings.NAME.to(this.value, this);\n  }\n\n  to(format) {\n    if (typeof format === 'string' && (format = format.toUpperCase()) && typeof ColorStrings[format] !== 'undefined') {\n      return ColorStrings[format].to(this.value, this);\n    }\n    return this.toString();\n  }\n\n  toString() {\n    let value = this.value;\n    if (!this._valid) {\n      value = this.options.invalidValue;\n\n      if (typeof value === 'string') {\n        return value;\n      }\n    }\n\n    if (value.a === 0 && this.options.zeroAlphaAsTransparent) {\n      return ColorStrings.TRANSPARENT.to(value, this);\n    }\n\n    let format;\n    if(this._format === false){\n      format = this._matchFormat;\n    } else {\n      format = this._format;\n    }\n\n    if (this.options.reduceAlpha && value.a === 1) {\n      switch (format) {\n        case 'RGBA':\n          format = 'RGB';\n          break;\n        case 'HSLA':\n          format = 'HSL';\n          break;\n        default:\n          break;\n      }\n    }\n\n    if (value.a !== 1 && format!=='RGBA' && format !=='HSLA' && this.options.alphaConvert){\n      if(typeof this.options.alphaConvert === 'string'){\n        format = this.options.alphaConvert;\n      }\n      if(typeof this.options.alphaConvert[format] !== 'undefined'){\n        format = this.options.alphaConvert[format];\n      }\n    }\n    return ColorStrings[format].to(value, this);\n  }\n\n  get() {\n    return this.value;\n  }\n\n  set(color) {\n    this._valid = true;\n    let fromRgb = 0;\n    let fromHsv = 0;\n    let hsv;\n    let rgb;\n\n    for (const i in color) {\n      if ('hsv'.includes(i)) {\n        fromHsv++;\n        this.value[i] = color[i];\n      } else if ('rgb'.includes(i)) {\n        fromRgb++;\n        this.value[i] = color[i];\n      } else if (i === 'a') {\n        this.value.a = color.a;\n      }\n    }\n    if (fromRgb > fromHsv) {\n      hsv = Converter.RGBtoHSV(this.value);\n      if (this.value.r === 0 && this.value.g === 0 && this.value.b === 0) {\n        // this.value.h = color.h;\n      } else {\n        this.value.h = hsv.h;\n      }\n\n      this.value.s = hsv.s;\n      this.value.v = hsv.v;\n    } else if (fromHsv > fromRgb) {\n      rgb = Converter.HSVtoRGB(this.value);\n      this.value.r = rgb.r;\n      this.value.g = rgb.g;\n      this.value.b = rgb.b;\n    }\n    return this;\n  }\n\n  static matchString(string) {\n    if (typeof string === 'string') {\n      string = $.trim(string);\n      let matched = null;\n      let rgb;\n      for (const i in ColorStrings) {\n        if ((matched = ColorStrings[i].match.exec(string)) !== null) {\n          rgb = ColorStrings[i].parse(matched);\n\n          if (rgb) {\n            return true;\n          }\n        }\n      }\n    }\n    return false;\n  }\n\n  static setDefaults(options) {\n    $.extend(true, DEFAULTS, $.isPlainObject(options) && options);\n  }\n}\n\nvar info = {\n  version:'0.3.6'\n};\n\nconst OtherAsColor = $.asColor;\n\nconst jQueryAsColor = function(...args) {\n  return new AsColor(...args);\n};\n\n$.asColor = jQueryAsColor;\n$.asColor.Constructor = AsColor;\n\n$.extend($.asColor, {\n  matchString: AsColor.matchString,\n  setDefaults: AsColor.setDefaults,\n  noConflict: function() {\n    $.asColor = OtherAsColor;\n    return jQueryAsColor;\n  }\n}, Converter, info);\n\nvar main = $.asColor;\n\nexport default main;\n"]}