<?php

namespace App\Http\Middleware;

use Illuminate\Auth\Middleware\Authenticate as Middleware;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class Authenticate extends Middleware
{
    /**
     * Get the path the user should be redirected to when they are not authenticated.
     */
    // protected function redirectTo(Request $request): ?string
    // {
    //     return $request->expectsJson() ? null : route('admin.loginpage');
    // }
    protected function redirectTo(Request $request): ?string
    {
        Log::info('Checking authentication guards.');
    
        // Check if the request expects JSON
        if ($request->expects<PERSON>son()) {
            return null; 
        }
    
        // Redirect based on guard status
        if (Auth::guard('admin')->check()) {
            return route('admin.dashboard'); // Change this to the appropriate admin route
        } elseif (Auth::guard('web')->check()) {
            return route('ulb.dashboard'); // Redirect to ULB dashboard if authenticated
        }
    
        return route('ulb.loginpage'); // Redirect to ULB login page if no guard is authenticated
    }
    
}
