<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class HandpumInfo extends Model
{
    use HasFactory;
    public function ulb()
    {
        return $this->belongsTo(User::class,'ulb_id');
    }
    public function division()
    {
        return $this->belongsTo(MasterDivision::class, 'division_id');
    }
    protected $fillable = [
        'ulb_id',
        'dateInput',
        'unique_id',
        'total_handpump',
        'handpump_working',
        'handpump_not_working',
        'total_borewell',
        'borewell_working',
        'borewell_not_working',
    ];
}
