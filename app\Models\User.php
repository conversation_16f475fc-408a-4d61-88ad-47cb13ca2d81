<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string> 
     */
    protected $fillable = [
        'name',
        'email',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
    ];

    public function division()
    {
        return $this->belongsTo(MasterDivision::class, 'division_id');
    }
    public function district()
    {
        return $this->belongsTo(MasterDistrict::class, 'district_id');
    }
    public function ulb()
    {
        return $this->belongsTo(User::class, 'id');  // Make sure 'ulb_id' exists in your `users` table
    }
    
    public function basicDetails()
    {
        return $this->hasOne(UlbBasicDetail::class, 'ulb_id');
    }
    
    public function administrativeDetails()
    {
        return $this->hasOne(UlbAdministrativeDetail::class, 'ulb_id');
    }
    
    public function financialDetails()
    {
        return $this->hasOne(UlbFinancialDetail::class, 'ulb_id');
    }
    
    public function infrastructureServices()
    {
        return $this->hasOne(UlbInfrastructureService::class, 'ulb_id');
    }
    
    public function developmentProjects()
    {
        return $this->hasOne(UlbDevelopmentProject::class, 'ulb_id');
    }
    
    public function digitalGovernance()
    {
        return $this->hasOne(UlbDigitalGovernance::class, 'ulb_id');
    }
    
    public function citizenParticipation()
    {
        return $this->hasOne(CitizenParticipation::class, 'ulb_id');
    }
    
    public function environmentalInitiative()
    {
        return $this->hasOne(EnvironmentalInitiative::class, 'ulb_id');
    }
    
    public function recreationalFacilities()
    {
        return $this->hasMany(RecreationalFacility::class, 'ulb_id');
    }
    
    public function infrastructureWork()
    {
        return $this->hasOne(InfrastructureWork::class, 'ulb_id');
    }
    
    public function digitalSmartFeature()
    {
        return $this->hasOne(DigitalSmartFeature::class, 'ulb_id');
    }
}
