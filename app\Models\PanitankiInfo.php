<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PanitankiInfo extends Model
{
    use HasFactory;
    public function ulb()
    {
        return $this->belongsTo(User::class,'ulb_id');
    }
    public function division()
    {
        return $this->belongsTo(MasterDivision::class, 'division_id');
    }
    protected $fillable = [
        'unique_id',
        'ulb_id',
        'dateInput',
        'ward_no',
        'ward_name',
        'panitaki_address',
        'panitaki_capacity',
        'latitude',
        'longitude',
    ];
}
