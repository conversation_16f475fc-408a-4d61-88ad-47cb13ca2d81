<?php

namespace App\Http\Controllers;

use App\Models\Admin;
use App\Models\AnyaKarya;
use App\Models\AppatnidhiMarammat;
use App\Models\Bar;
use App\Models\ChungiMadhVyay;
use App\Models\Complexinformation;
use App\Models\ElectricityBill;
use App\Models\Employee;
use App\Models\User;
use App\Models\RevenueCollection;
use App\Models\EmployeeRegistration;
use App\Models\JalPradayGirh;
use App\Models\LambitVetan;
use App\Models\LeaveApply;
use App\Models\MahapaurAdhyakshNidhi;
use App\Models\MissionCleanCity;
use App\Models\MudrankSulak;
use App\Models\NewMeterRegistration;
use App\Models\ParshadNidhi;
use App\Models\PeyjalkastNivaran;
use App\Models\RevenueDemand;
use App\Models\SalaryDetail;
use App\Models\SalaryDetailPlacement;
use App\Models\SwachCommando;
use App\Models\TaxType;
use App\Models\Utpadkar;
use App\Models\YatriKar;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use RealRashid\SweetAlert\Facades\Alert;
use App\Models\EmployeePost;
use App\Models\EmployeeType;
use App\Models\GardenInfo;
use App\Models\GovtBuilding;
use App\Models\HandpumInfo;
use App\Models\IncomeExpenditure;
use App\Models\jdempregistration;
use App\Models\Muktidham;
use App\Models\PanitankiInfo;
use App\Models\Payscale;
use App\Models\PeyjalInfo;
use App\Models\PlaygroundInfo;
use App\Models\StreetlightInfo;
use App\Models\TalabInformation;
use App\Models\Jaljanitbimari;
use App\Models\Jd_Info;
use App\Models\RainWaterHarvesting;
use App\Models\TrafficLight;
use Illuminate\Support\Facades\Hash;


class JdController extends Controller
{
    public function jddashboard()
    {
        $loggedInUser = Auth::guard('admin')->user();
        $divisionId =  $loggedInUser->division_id;
        $nikay = Auth::guard('web')->user();

        // Count the number of ULBs with the same division_id
        $ulbCount = User::where('division_id', $divisionId)->count();
        $employeeCount = Employee::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })->count();
        $jdmployeeCount = jdempregistration::whereHas('jdname', function ($query) use ($divisionId) {
            $query->where('division_id', $divisionId); // Assuming current_jd_id stores division_id
        })->count();
        $leaveCount = LeaveApply::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })->count();
        $salaryCount = SalaryDetail::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })->count();
        $meterCount = NewMeterRegistration::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })->count();

        return view('jddashboard/jddashboard', compact('ulbCount', 'jdmployeeCount', 'employeeCount', 'leaveCount', 'salaryCount', 'meterCount'));
    }

    // public function jdprofile()
    // {
    //     $loggedInUser = Auth::guard('admin')->user();
    //     $item = User::where('id', $loggedInUser->id)->get();
    //     $userdata = Admin::find($loggedInUser->id);
    //     $office_photo = $userdata->office_photo;
    // //    dd($officephoto);
    //     return view('jddashboard/jdprofile', compact('item','office_photo'));
    // }
    public function jdprofile()
    {
        // Get the currently logged-in admin user
        $loggedInUser = Auth::guard('admin')->user();
        $item = Admin::where('id', $loggedInUser->id)->get();

        $admin = Admin::find($loggedInUser->id);
        // $office_photo = $admin ? $admin->office_photo : null; 
        // Return the view with user and office photo data
        return view('jddashboard/jdprofile', compact('item'));
    }

    public function JdPasswordUpdate(Request $request, $id)
    {
        // Validate incoming request data
        $validatedData = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email',
            'new_password' => 'nullable|string|min:6', // Made password optional
            // 'office_address' => 'required|string|max:255',
            // 'office_photo' => 'nullable|image|mimes:jpeg,jpg,png|max:50',
        ]);

        // Fetch the ULB registration instance by ID
        $ulbRegistration = Admin::find($id);
        if (!$ulbRegistration) {
            return back()->withErrors(['jdname' => 'jd not found'])->withInput();
        }

        // Update fields
        $ulbRegistration->name = $validatedData['name'];
        $ulbRegistration->email = $validatedData['email'];
        // $ulbRegistration->office_address = $validatedData['office_address'];

        // Update password only if provided
        if (!empty($validatedData['new_password'])) {
            $ulbRegistration->password = Hash::make($validatedData['new_password']);
        }
        try {
            $ulbRegistration->save();
        } catch (\Exception $e) {
            return back()->withErrors(['save' => 'Failed to update profile: ' . $e->getMessage()])->withInput();
        }

        Alert::success('success', 'Profile updated successfully');
        return back();
    }
    public function jdempregistration()
    {

        $loggedInUser = Auth::guard('admin')->user();
        $divisionId = $loggedInUser->division_id;
        $ulbs = User::all();
        $jds = Jd_Info::all();
        // $ulbs ->push((object)[
        //     'id' => 500,
        //     'name'=>'Directorate',  
        //    ]);
        $employeeposts = EmployeePost::all();
        $employeetypes = EmployeeType::all();
        $payscale = Payscale::all();
        // $qualifications = Qualification::all();
        return view('jddashboard.jdemployeeregistration', compact('divisionId', 'ulbs', 'jds', 'employeeposts', 'employeetypes', 'payscale'));
    }

    // public function jdempregistrationsubmit(Request $request)
    // {
    //     // dd($request);
    //     $validatedData = $request->validate([
    //         'employee_photo' => 'required|image|mimes:jpeg,jpg,png|max:50',
    //         'current_jd' => 'required|integer',
    //         'post_name' => 'required|integer',
    //         // 'post_number' => 'nullable|integer',
    //         'payscale' => 'required|integer',
    //         'employee_name' => 'required|string|max:255',
    //         'father_name' => 'required|string|max:255',
    //         'employee_type' => 'required|integer',
    //         'birth_date' => 'required|date',
    //         'gender' => 'required|in:male,female,other',
    //         'caste' => 'required|string|max:255',
    //         'qualification' => 'required|string',
    //         'work_start_date' => 'required|date',
    //         'current_designation' => 'required|string',
    //         // 'designation_date' => 'required|date',
    //         'joining_date' => 'required|date',
    //         // 'suspension_status' => 'required|string|max:255',
    //         'original_ulb_name' => 'required|string',
    //         'department_name' => 'required|string',
    //         // 'vacant_post_count' => 'nullable|integer',
    //         // 'phone_number' => 'nullable|string|max:15',
    //         'mobile_number' => 'required|string|max:10',
    //         'email' => 'required|email',
    //         'temporary_address' => 'required|string',
    //         'permanent_address' => 'required|string',
    //         'employee_cl' => 'nullable',
    //         'employee_ol' => 'nullable',
    //         'employee_el' => 'nullable',
    //         'employee_ml' => 'nullable',
    //         'e_hrms_email' => 'required|string',
    //         'remarks' => 'nullable|string',
    //         'reference_id' => [
    //             'nullable',
    //             'string',
    //             'size:12',
    //             'unique:jdempregistrations,reference_id',
    //         ]
    //     ]);

    //     if ($request->hasFile('employee_photo')) {
    //         $file = $request->file('employee_photo');
    //         $filename = time() . '_' . $file->getClientOriginalName();
    //         $filePath = $file->storeAs('jduploads/employee_photos', $filename, 'public'); // Store file in 'storage/app/public/uploads/employee_photos'
    //         $validatedData['employee_photo'] = $filePath;
    //     }
    //     $loggedInUser = Auth::guard('admin')->user();

    //     $jdcode = $loggedInUser->division_id;
    //     do {
    //         $randomNumber = rand(10, 9999);
    //         $registrationNo = 'JD0' . $jdcode . $randomNumber;
    //     } while (jdempregistration::where('current_jd', $loggedInUser->id)->where('registration_no', $registrationNo)->exists());

    //     $validatedData['registration_no'] = $registrationNo;

    //     $employee = jdempregistration::create($validatedData);
    //     // dd($employee); 
    //     // dd($validatedData);
    //     Alert::success('Success!', 'Employee data submitted successfully!');

    //     return back();
    // }

    public function jdempregistrationsubmit(Request $request)
    {
        // dd($request);
        $validatedData = $request->validate([
            'employee_photo' => 'required|image|mimes:jpeg,jpg,png|max:50',
            'current_jd' => 'required|integer',
            'post_name' => 'required|integer',
            // 'post_number' => 'nullable|integer',
            'payscale' => 'required|integer',
            'employee_name' => 'required|string|max:255',
            'father_name' => 'required|string|max:255',
            'employee_type' => 'required|integer',
            'birth_date' => 'required|date',
            'gender' => 'required|in:male,female,other',
            'caste' => 'required|string|max:255',
            'qualification' => 'required|string',
            'work_start_date' => 'required|date',
            'current_designation' => 'required|string',
            'joining_date' => 'required|date',
            'original_ulb_name' => 'nullable|integer',
            'other_office' => 'nullable|string',
            'department_name' => 'required|string',
            'mobile_number' => 'required|string|max:10',
            'email' => 'required|email',
            'temporary_address' => 'required|string',
            'permanent_address' => 'required|string',
            'employee_cl' => 'nullable',
            'employee_ol' => 'nullable',
            'employee_el' => 'nullable',
            'employee_ml' => 'nullable',
            'e_hrms_email' => 'required|string',
            'remarks' => 'nullable|string',
            'reference_id' => [
                'nullable',
                'string',
                'size:12',
                'unique:jdempregistrations,reference_id',
            ]
        ]);

        if (!$request->filled('original_ulb_name') && !$request->filled('other_office')) {
            return back()->withErrors([
                'original_ulb_name' => 'कृपया निकाय चुनें या अन्य कार्यालय का नाम दर्ज करें।',
                'other_office' => 'कृपया निकाय चुनें या अन्य कार्यालय का नाम दर्ज करें।',
            ])->withInput();
        }

        if ($request->hasFile('employee_photo')) {
            $file = $request->file('employee_photo');
            $filename = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('jduploads/employee_photos', $filename, 'public'); // Store file in 'storage/app/public/uploads/employee_photos'
            $validatedData['employee_photo'] = $filePath;
        }
        $loggedInUser = Auth::guard('admin')->user();

        $jdcode = $loggedInUser->division_id;
        do {
            $randomNumber = rand(10, 9999);
            $registrationNo = 'JD0' . $jdcode . $randomNumber;
        } while (jdempregistration::where('current_jd', $loggedInUser->id)->where('registration_no', $registrationNo)->exists());

        $validatedData['registration_no'] = $registrationNo;

        $employee = jdempregistration::create($validatedData);
        // dd($employee); 
        // dd($validatedData);
        Alert::success('Success!', 'Employee data submitted successfully!');

        return back();
    }
    public function jdempreport(Request $request)
    {
        $loggedInUser = Auth::guard('admin')->user();
        $divisionId = $loggedInUser->id;

        $items = jdempregistration::where('current_jd', $divisionId)->orderBy('id') // Optional: Sort by ID
            ->paginate(50); // Paginate with 50 records per page

        return view('jddashboard/jdempreport', compact('items', 'divisionId'));
    }

    public function jdempedit($id)
    {
        $loggedInUser = Auth::guard('admin')->user();
        $divisionId = $loggedInUser->division_id;
        $items = jdempregistration::findOrFail($id);
        $ulbs = User::all();
        $admins = Admin::all();
        $employeeposts = EmployeePost::all();
        $employeetypes = EmployeeType::all();
        $payscale = Payscale::all();
        // $qualifications = Qualification::all();

        return view('jddashboard.jdeditemployee', compact(
            'items',
            'admins',
            'ulbs',
            'employeeposts',
            'employeetypes',
            'payscale',
            'divisionId',
        ));
    }


    // public function jdempupdate(Request $request, $id)
    // {
    //     $employee = jdempregistration::find($id);

    //     $validatedData = $request->validate([
    //         'post_name' => 'required|integer',
    //         'employee_photo' => 'nullable|image|mimes:jpeg,jpg,png|max:50',
    //         'payscale' => 'required|integer',
    //         'employee_name' => 'required|string|max:255',
    //         'father_name' => 'required|string|max:255',
    //         'gender' => 'required|in:male,female,other',
    //         'employee_type' => 'required|integer',
    //         'birth_date' => 'required|date',
    //         'caste' => 'required|string|max:255',
    //         'qualification' => 'required|string',
    //         'work_start_date' => 'required|date',
    //         'current_designation' => 'required|string',
    //         'joining_date' => 'required|date',
    //         'original_ulb_name' => 'nullable|integer',
    //         'other_office' => 'nullable|string',
    //         'department_name' => 'required|string',
    //         'mobile_number' => 'required|string|max:15',
    //         'email' => 'required|email',
    //         'temporary_address' => 'required|string',
    //         'permanent_address' => 'required|string',
    //         'employee_cl' => 'nullable',
    //         'employee_ol' => 'nullable',
    //         'employee_el' => 'nullable',
    //         'employee_ml' => 'nullable',
    //         'e_hrms_email' => 'required|string',
    //         'remarks' => 'nullable|string',
    //         'reference_id' => 'nullable|string',
    //     ]);

    //     if ($request->hasFile('employee_photo')) {
    //         $file = $request->file('employee_photo');
    //         $filename = time() . '_' . $file->getClientOriginalName();
    //         $filePath = $file->storeAs('jduploads/employee_photos', $filename, 'public'); // Store file in 'storage/app/public/uploads/employee_photos'
    //         $validatedData['employee_photo'] = $filePath;  // Update the photo path
    //     } else {
    //         // Retain the old photo path if no new photo is uploaded
    //         $filePath = $employee->employee_photo;
    //     }
    //     $newUlbId = $request->input('current_jd');
    //     if ($employee->current_jd != $newUlbId) {
    //         $employee->previous_jd = $employee->current_jd;
    //         $employee->is_jd_verify = false;
    //     }
    //     $employee->employee_photo = $filePath;
    //     $employee->current_jd = $newUlbId;
    //     $employee->post_name = $request->input('post_name');
    //     // $employee->post_number = $request->input('post_number');
    //     $employee->payscale = $request->input('payscale');
    //     $employee->employee_name = $request->input('employee_name');
    //     $employee->father_name = $request->input('father_name');
    //     $employee->gender = $request->input('gender');
    //     $employee->employee_type = $request->input('employee_type');
    //     $employee->birth_date = $request->input('birth_date');
    //     $employee->caste = $request->input('caste');
    //     $employee->qualification = $request->input('qualification');
    //     $employee->work_start_date = $request->input('work_start_date');
    //     $employee->current_designation = $request->input('current_designation');
    //     // $employee->designation_date = $request->input('designation_date');
    //     $employee->joining_date = $request->input('joining_date');
    //     // $employee->suspension_status = $request->input('suspension_status');
    //     $employee->original_ulb_name = $request->input('original_ulb_name');
    //     $employee->department_name = $request->input('department_name');
    //     $employee->phone_number = $request->input('phone_number');
    //     $employee->mobile_number = $request->input('mobile_number');
    //     $employee->email = $request->input('email');
    //     $employee->temporary_address = $request->input('temporary_address');
    //     $employee->permanent_address = $request->input('permanent_address');
    //     $employee->remarks = $request->input('remarks');
    //     $employee->reference_id = $request->input('reference_id');
    //     if (in_array($employee->employee_type, [1, 5])) {
    //         $employee->employee_cl = $request->input('employee_cl');
    //         $employee->employee_ol = $request->input('employee_ol');
    //         $employee->employee_el = $request->input('employee_el');
    //         $employee->employee_ml = $request->input('employee_ml');
    //     }

    //     $employee->save();
    //     Alert::success('Success!', 'Application Updated successfully');
    //     return redirect()->route('jdemp.report')->with('success', 'Employee data updated successfully!');
    // }
    public function jdempupdate(Request $request, $id)
    {
        $employee = jdempregistration::find($id);

        $validatedData = $request->validate([
            'post_name' => 'required|integer',
            'employee_photo' => 'nullable|image|mimes:jpeg,jpg,png|max:50',
            'payscale' => 'required|integer',
            'employee_name' => 'required|string|max:255',
            'father_name' => 'required|string|max:255',
            'gender' => 'required|in:male,female,other',
            'employee_type' => 'required|integer',
            'birth_date' => 'required|date',
            'caste' => 'required|string|max:255',
            'qualification' => 'required|string',
            'work_start_date' => 'required|date',
            'current_designation' => 'required|string',
            'joining_date' => 'required|date',
            'original_ulb_name' => 'nullable|integer',
            'other_office' => 'nullable|string|max:255',
            'department_name' => 'required|string',
            'mobile_number' => 'required|string|max:15',
            'email' => 'required|email',
            'temporary_address' => 'required|string',
            'permanent_address' => 'required|string',
            'employee_cl' => 'nullable',
            'employee_ol' => 'nullable',
            'employee_el' => 'nullable',
            'employee_ml' => 'nullable',
            'e_hrms_email' => 'required|string',
            'remarks' => 'nullable|string',
            'reference_id' => 'nullable|string',
        ]);

        // ✅ Validate that at least one of the two fields is present
        if (!$request->filled('original_ulb_name') && !$request->filled('other_office')) {
            return back()->withErrors([
                'original_ulb_name' => 'कृपया निकाय चुनें या अन्य कार्यालय का नाम दर्ज करें।',
                'other_office' => 'कृपया निकाय चुनें या अन्य कार्यालय का नाम दर्ज करें।',
            ])->withInput();
        }

        if ($request->hasFile('employee_photo')) {
            $file = $request->file('employee_photo');
            $filename = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('jduploads/employee_photos', $filename, 'public');
            $validatedData['employee_photo'] = $filePath;
        } else {
            $filePath = $employee->employee_photo;
        }

        $newUlbId = $request->input('current_jd');
        if ($employee->current_jd != $newUlbId) {
            $employee->previous_jd = $employee->current_jd;
            $employee->is_jd_verify = false;
        }

        // ✅ Assign values
        $employee->employee_photo = $filePath;
        $employee->current_jd = $newUlbId;
        $employee->post_name = $request->input('post_name');
        $employee->payscale = $request->input('payscale');
        $employee->employee_name = $request->input('employee_name');
        $employee->father_name = $request->input('father_name');
        $employee->gender = $request->input('gender');
        $employee->employee_type = $request->input('employee_type');
        $employee->birth_date = $request->input('birth_date');
        $employee->caste = $request->input('caste');
        $employee->qualification = $request->input('qualification');
        $employee->work_start_date = $request->input('work_start_date');
        $employee->current_designation = $request->input('current_designation');
        $employee->joining_date = $request->input('joining_date');
        if ($request->input('toggle_office_type') === 'nikay') {
            $employee->original_ulb_name = $request->input('original_ulb_name');
            $employee->other_office = null;
        } elseif ($request->input('toggle_office_type') === 'other') {
            $employee->original_ulb_name = null;
            $employee->other_office = $request->input('other_office');
        }

        $employee->department_name = $request->input('department_name');
        $employee->phone_number = $request->input('phone_number');
        $employee->mobile_number = $request->input('mobile_number');
        $employee->email = $request->input('email');
        $employee->temporary_address = $request->input('temporary_address');
        $employee->permanent_address = $request->input('permanent_address');
        $employee->remarks = $request->input('remarks');
        $employee->reference_id = $request->input('reference_id');
        $employee->e_hrms_email = $request->input('e_hrms_email');

        if (in_array($employee->employee_type, [1, 5])) {
            $employee->employee_cl = $request->input('employee_cl');
            $employee->employee_ol = $request->input('employee_ol');
            $employee->employee_el = $request->input('employee_el');
            $employee->employee_ml = $request->input('employee_ml');
        }

        $employee->save();

        Alert::success('Success!', 'Application Updated successfully');
        return redirect()->route('jdemp.report')->with('success', 'Employee data updated successfully!');
    }


    public function rajasvasulidetail()
    {
        // Get the logged-in admin user details
        $loggedInUser = Auth::guard('admin')->user();
        $divisionId = $loggedInUser->division_id;

        // Fetch all tax types
        $taxTypes = TaxType::all();
        $groupedData = [];


        $revenueCollections = RevenueCollection::whereHas('ulb', function ($query) use ($divisionId) {
            // Filter by division_id
            $query->where('division_id', $divisionId);
        })
            ->with('taxtype') // Eager load tax types
            ->orderBy('created_at', 'desc')
            ->get();
        $groupedData = $revenueCollections->groupBy('ulb_id');

        return view('jddashboard/rajasvasulidetail', compact('groupedData', 'taxTypes', 'revenueCollections'));
    }

    // public function rajasvasuliulbnotsubmitted()
    // {
    //     $loggedInUser = Auth::guard('admin')->user();
    //     $divisionId = $loggedInUser->division_id;

    //     // Get aggregated revenue collection data for ULBs in this division
    //     $items = RevenueCollection::selectRaw('
    //         ulb_id,
    //         month,
    //         SUM(current_demand) as total_current_demand,
    //         SUM(current_collection) as total_current_collection,
    //         SUM(pending_demand) as total_pending_demand,
    //         SUM(pending_collection) as total_pending_collection
    //     ')
    //         ->whereHas('ulb', function ($query) use ($divisionId) {
    //             $query->where('division_id', $divisionId);
    //         })
    //         ->groupBy('ulb_id', 'month')
    //         ->orderBy('ulb_id', 'asc')
    //         ->get();

    //     return view('jddashboard.rajasvasuliulbnotsubmitted', compact('items'));
    // }

    // public function rajasvasuliulbnotsubmitted()
    // {
    //     $loggedInUser = Auth::guard('admin')->user();
    //     $divisionId = $loggedInUser->division_id;

    //     // Get the latest month available in the division
    //     $latestMonth = RevenueCollection::whereHas('ulb', function ($query) use ($divisionId) {
    //         $query->where('division_id', $divisionId);
    //     })
    //         ->max('month'); // e.g., "2025-06"

    //     // Get summed data for the latest month, grouped by ulb
    //     $items = RevenueCollection::selectRaw('
    //         ulb_id,
    //         month,
    //         SUM(current_demand) as total_current_demand,
    //         SUM(current_collection) as total_current_collection,
    //         SUM(pending_demand) as total_pending_demand,
    //         SUM(pending_collection) as total_pending_collection
    //     ')
    //         ->where('month', $latestMonth)
    //         ->whereHas('ulb', function ($query) use ($divisionId) {
    //             $query->where('division_id', $divisionId);
    //         })
    //         ->groupBy('ulb_id', 'month') // will sum multiple rows of same ULB+month
    //         ->with('ulb') // get ULB name
    //         ->orderBy('ulb_id')
    //         ->get();

    //     return view('jddashboard.rajasvasuliulbnotsubmitted', compact('items', 'latestMonth'));
    // }

    public function rajasvasuliulbwise()
    {
        $divisionId = Auth::guard('admin')->user()->division_id;

        // Step 1: Get summed data grouped by ulb_id + month
        $groupedData = RevenueCollection::selectRaw('
        ulb_id,
        month,
        SUM(current_demand) as total_current_demand,
        SUM(current_collection) as total_current_collection,
            SUM(pending_demand) as total_pending_demand,
            SUM(pending_collection) as total_pending_collection')
            ->whereHas('ulb', function ($query) use ($divisionId) {
                $query->where('division_id', $divisionId);
            })
            ->groupBy('ulb_id', 'month')
            ->orderBy('month', 'desc') // important: so latest month comes first
            ->get();

        // Step 2: Keep only the latest month per ulb_id
        $latestPerUlb = $groupedData->groupBy('ulb_id')->map(function ($items) {
            return $items->sortByDesc('month')->first(); // get the latest month for each ULB
        })->values(); // convert map result to collection

        return view('jddashboard.rajasvasuliulbwise', [
            'items' => $latestPerUlb
        ]);
    }


    public function verifyData(Request $request)
    {
        $validated = $request->validate([
            'unique_no' => 'required|exists:revenue_collections,unique_no', // Validate unique_no existence
        ]);
        $data = RevenueCollection::where('unique_no', $validated['unique_no'])->first();

        // Get the division ID of the logged-in user
        $loggedInUser = Auth::guard('admin')->user();
        $divisionId = $loggedInUser->division_id;

        if ($data) {
            if ($data->ulb && $data->ulb->division_id == $divisionId) {
                $data->is_jd_verify = true;
                $data->save();
                Alert::success('Success!', 'Data successfully verified.');
            } else {

                Alert::error('Error!', 'Record not found.');
            }
        } else {
            Alert::error('Error!', 'Record not found.');
        }
        return redirect()->route('rajasvasuli.detail');
    }

    public function verifyallRajasvasuliData(Request $request)
    {
        $validated = $request->validate([
            'unique_nos' => 'required|string',
        ]);
        $unique_nos = explode(',', $validated['unique_nos']);
        $emp = RevenueCollection::whereIn('unique_no', $unique_nos)->get();
        $loggedInUser = Auth::guard('admin')->user();
        $divisionId = $loggedInUser->division_id;

        foreach ($emp as $data) {
            if ($data->ulb && $data->ulb->division_id == $divisionId) {
                $data->is_jd_verify = 1;
                $data->save();
            }
        }
        Alert::success('Success!', 'All Records Verified.');

        return redirect()->route('rajasvasuli.detail');
    }


    //  {{--  Employee registration --}}

    public function jdemployeedetail(Request $request)
    {
        $nikay = Auth::guard('web')->user();
        $items = Employee::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })
            ->with('ulb', 'ulbnew')
            ->orderBy('id') // Optional: Sort by ID
            ->paginate(100); // Paginate with 50 records per page

        return view('jddashboard/jdemployeedetail', compact('items'));
    }


    public function verifyEmpData(Request $request)
    {
        $validated = $request->validate([
            'id' => 'required|exists:employees,id',
        ]);
        $data = Employee::find($validated['id']);
        if ($data) {

            $data->is_jd_verify = true;
            $data->save();
            Alert::success('Success!', ' successfully verified.');
        } else {
            Alert::error('Error!', 'Record not found.');
        }
        return redirect()->route('jdemployee.detail');
    }
    public function verifyallEmpData(Request $request)
    {

        $validated = $request->validate([
            'ids' => 'required|string',
        ]);
        $ids = explode(',', $validated['ids']);
        $emp = Employee::whereIn('id', $ids)->get();

        // Loop through each salary record and update the 'is_jd_verify' field to 1
        foreach ($emp as $data) {
            $data->is_jd_verify = 1;  // Set the 'is_jd_verify' field to 1
            $data->save();  // Save the updated record
        }

        // Return a success message and redirect back to the salary details page
        Alert::success('Success!', 'All Records Verified.');

        return redirect()->route('jdemployee.detail');
    }


    public function jdsuspensionreport(Request $request)
    {
        $nikay = Auth::guard('web')->user();

        $items = Employee::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })
            ->whereNotNull('suspension_order_no')  // Add this condition to check if suspension_order_no is not null
            ->where('suspension_order_no', '!=', '')  // Optionally, check if it's not an empty string
            ->with('ulb', 'ulbnew')
            ->get();

        return view('jddashboard/jdsuspensionreport', compact('items'));
    }

    //  {{--  Leave detail --}}


    public function jdleavedetail()
    {
        $nikay = Auth::guard('web')->user();

        $items = LeaveApply::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })
            ->with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                // For each group, get the most recent record
                return $ulbGroup->sortByDesc('updated_at')->first(); // Sort by updated_at and get the first record
            });
        return view('jddashboard/jdleavedetail', compact('items'));
    }

    public function verifyleavedata(Request $request)
    {
        $validated = $request->validate([
            'id' => 'required|exists:leave_applies,id',
        ]);
        $data = LeaveApply::find($validated['id']);
        if ($data) {

            $data->is_jd_verify = true;
            $data->save();
            Alert::success('Success!', ' successfully verified.');
        } else {
            Alert::error('Error!', 'Record not found.');
        }
        return redirect()->route('jdleave.detail');
    }

    public function verifyAllleavedata(Request $request)
    {

        $validated = $request->validate([
            'ids' => 'required|string',
        ]);
        $ids = explode(',', $validated['ids']);
        $emp = LeaveApply::whereIn('id', $ids)->get();

        // Loop through each salary record and update the 'is_jd_verify' field to 1
        foreach ($emp as $data) {
            $data->is_jd_verify = 1;  // Set the 'is_jd_verify' field to 1
            $data->save();  // Save the updated record
        }

        // Return a success message and redirect back to the salary details page
        Alert::success('Success!', 'All Records Verified.');

        return redirect()->route('jdleave.detail');
    }
    // {{--  Salary Detail --}}


    // public function jdsalarydetails()
    // {
    //     $nikay = Auth::guard('web')->user();

    //     $items = SalaryDetail::whereHas('ulb', function ($query) use ($nikay) {
    //         $query->where('users.division_id', Auth::user()->division_id);
    //     })
    //         ->with('ulb')
    //         ->get()
    //         ->groupBy('ulb_id')
    //         ->map(function ($ulbGroup) {
    //             return $ulbGroup->sortByDesc('updated_at')->first();
    //         });

    //     $salary = SalaryDetailPlacement::whereHas('ulb', function ($query) use ($nikay) {
    //         $query->where('users.division_id', Auth::user()->division_id);
    //     })
    //         ->with('ulb')
    //         ->get()
    //         ->groupBy('ulb_id')
    //         ->map(function ($ulbGroup) {
    //             return $ulbGroup->sortByDesc('updated_at')->first();
    //         });
    //     return view('jddashboard/jdsalarydetails', compact('items', 'salary'));
    // }

    public function jdsalarydetails()
    {
        $jd = Auth::guard('admin')->user(); // JD user from 'admin' guard
        $divisionId = $jd->division_id;

        // Step 1: Fetch all ULBs under the JD's division
        $ulbs = User::whereHas('district', function ($query) use ($divisionId) {
            $query->where('division_id', $divisionId);
        })->get();

        // Step 2: Loop and count salary entries
        $ulbData = $ulbs->map(function ($ulb) {
            return [
                'ulb_id' => $ulb->id,
                'ulb_name' => $ulb->name,
                'regular_count' => SalaryDetail::where('ulb_id', $ulb->id)->count(),
                'placement_count' => SalaryDetailPlacement::where('ulb_id', $ulb->id)->count(),
            ];
        });

        return view('jddashboard.jdsalarydetails', compact('ulbData'));
    }


    public function jdregularsalary($ulb_id)
    {
        $ulb = User::findOrFail($ulb_id);
        $salaries = SalaryDetail::where('ulb_id', $ulb_id)->with('ulb')->get();

        return view('jddashboard.jdregularsalary', compact('ulb', 'salaries'));
    }

    public function jdplacementsalary($ulb_id)
    {
        $ulb = User::findOrFail($ulb_id);
        $salaries = SalaryDetailPlacement::where('ulb_id', $ulb_id)->with('ulb')->get();

        return view('jddashboard.jdplacementsalary', compact('ulb', 'salaries'));
    }

    public function verifysalaryData(Request $request)
    {

        $validated = $request->validate([
            'id' => 'required|exists:salary_details,id',
        ]);
        $data = SalaryDetail::find($validated['id']);
        if ($data) {
            $data->is_jd_verify = true;
            $data->save();
            Alert::success('Success!', ' successfully verified.');
        } else {

            Alert::error('Error!', 'Record not found.');
        }
        return redirect()->back();
    }

    public function verifyallsalaryData(Request $request)
    {

        $validated = $request->validate([
            'ids' => 'required|string',
        ]);
        $ids = explode(',', $validated['ids']);
        $salarys = SalaryDetail::whereIn('id', $ids)->get();

        // Loop through each salary record and update the 'is_jd_verify' field to 1
        foreach ($salarys as $data) {
            $data->is_jd_verify = 1;  // Set the 'is_jd_verify' field to 1
            $data->save();  // Save the updated record
        }

        // Return a success message and redirect back to the salary details page
        Alert::success('Success!', 'All Records Verified.');

        return redirect()->back();
    }

    //  {{--  Placement salary --}}

    public function verifyplacementsalaryData(Request $request)
    {

        $validated = $request->validate([
            'id' => 'required|exists:salary_detail_placements,id',
        ]);
        $data = SalaryDetailPlacement::find($validated['id']);
        if ($data) {
            $data->is_jd_verify = true;
            $data->save();
            Alert::success('Success!', ' successfully verified.');
        } else {

            Alert::error('Error!', 'Record not found.');
        }
        return redirect()->back();
    }

    public function verifyallplacementsalaryData(Request $request)
    {

        $validated = $request->validate([
            'ids' => 'required|string',
        ]);
        $ids = explode(',', $validated['ids']);
        $salaryDetails = SalaryDetailPlacement::whereIn('id', $ids)->get();

        // Loop through each salary record and update the 'is_jd_verify' field to 1
        foreach ($salaryDetails as $data) {
            $data->is_jd_verify = 1;  // Set the 'is_jd_verify' field to 1
            $data->save();  // Save the updated record
        }

        // Return a success message and redirect back to the salary details page
        Alert::success('Success!', 'All Records Verified.');

        return redirect()->back();
    }


    //  {{--  Electricity Bill --}}



    // public function jdelectricitybill()
    // {
    //     $jd = Auth::guard('admin')->user(); // Use 'admin' guard for JD
    //     $divisionId = $jd->division_id;

    //     // Step 1: Get all ULBs in this division
    //     $ulbs = User::where('division_id', $divisionId)->get();

    //     // Step 2: Get latest electricity bill per ULB
    //     $bills = ElectricityBill::whereHas('ulb', function ($query) use ($divisionId) {
    //         $query->where('division_id', $divisionId);
    //     })
    //         ->with('ulb')
    //         ->get()
    //         ->groupBy('ulb_id')
    //         ->map(function ($group) {
    //             return $group->sortByDesc('updated_at')->first();
    //         });

    //     // Step 3: Sort ULBs with data first
    //     $sortedUlbs = $ulbs->sortByDesc(function ($ulb) use ($bills) {
    //         return isset($bills[$ulb->id]);
    //     });

    //     return view('jddashboard.jdelectricitybill', [
    //         'ulbs' => $sortedUlbs,
    //         'bills' => $bills,
    //     ]);
    // }

    public function jdelectricitybill()
    {
        $jd = Auth::guard('admin')->user();
        $divisionId = $jd->division_id;

        // Step 1: Get all ULBs in this division
        $ulbs = User::whereHas('district', function ($query) use ($divisionId) {
            $query->where('division_id', $divisionId);
        })->get();

        // Step 2: Count electricity bills per ULB
        $ulbData = $ulbs->map(function ($ulb) {
            $billCount = ElectricityBill::where('ulb_id', $ulb->id)->count();

            return [
                'ulb_id' => $ulb->id,
                'ulb_name' => $ulb->name,
                'bill_count' => $billCount,
            ];
        });

        return view('jddashboard.jdelectricitybill', compact('ulbData'));
    }


    public function jdelectricitydetails($ulb_id)
    {
        $ulb = User::findOrFail($ulb_id);
        $bills = ElectricityBill::where('ulb_id', $ulb_id)->get();

        return view('jddashboard.jdelectricitydetails', compact('ulb', 'bills'));
    }


    public function verifyelectricitybill(Request $request)
    {

        $validated = $request->validate([
            'id' => 'required|exists:electricity_bills,id',
        ]);
        $data = ElectricityBill::find($validated['id']);
        if ($data) {
            $data->is_jd_verify = true;
            $data->save();
            Alert::success('Success!', ' successfully verified.');
        } else {

            Alert::error('Error!', 'Record not found.');
        }
        return redirect()->back();
    }
    public function verifyAllelectricitybill(Request $request)
    {

        $validated = $request->validate([
            'ids' => 'required|string',
        ]);
        $ids = explode(',', $validated['ids']);
        $salaryDetails = ElectricityBill::whereIn('id', $ids)->get();

        // Loop through each salary record and update the 'is_jd_verify' field to 1
        foreach ($salaryDetails as $data) {
            $data->is_jd_verify = 1;  // Set the 'is_jd_verify' field to 1
            $data->save();  // Save the updated record
        }

        // Return a success message and redirect back to the salary details page
        Alert::success('Success!', 'All Records Verified.');

        return redirect()->back();
    }

    //  {{-- New Meter --}}


    public function jdnewmeter()
    {
        $nikay = Auth::guard('web')->user();

        $items = NewMeterRegistration::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })
            ->with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        return view('jddashboard/jdnewmeter', compact('items'));
    }

    public function verifynewmeter(Request $request)
    {

        $validated = $request->validate([
            'id' => 'required|exists:new_meter_registrations,id',
        ]);
        $data = NewMeterRegistration::find($validated['id']);

        if ($data) {

            $data->is_jd_verify = true;
            $data->save();
            Alert::success('Success!', ' successfully verified.');
        } else {
            Alert::error('Error!', 'Record not found.');
        }
        return redirect()->route('jdnew.meter');
    }

    public function verifyAllnewmeter(Request $request)
    {

        $validated = $request->validate([
            'ids' => 'required|string',
        ]);
        $ids = explode(',', $validated['ids']);
        $salaryDetails = NewMeterRegistration::whereIn('id', $ids)->get();

        // Loop through each salary record and update the 'is_jd_verify' field to 1
        foreach ($salaryDetails as $data) {
            $data->is_jd_verify = 1;  // Set the 'is_jd_verify' field to 1
            $data->save();  // Save the updated record
        }

        // Return a success message and redirect back to the salary details page
        Alert::success('Success!', 'All Records Verified.');

        return redirect()->route('jdnew.meter');
    }




    // public function jdincomeexpenditurereport()
    // {
    //     $nikay = Auth::guard('web')->user();

    //     $items = IncomeExpenditure::whereHas('ulb', function ($query) use ($nikay) {
    //         $query->where('users.division_id', Auth::user()->division_id);
    //     })
    //         ->with('ulb')
    //         ->get()
    //         ->groupBy('ulb_id')
    //         ->map(function ($ulbGroup) {
    //             return $ulbGroup->sortByDesc('updated_at')->first();
    //         });

    //     return view('jddashboard/jdincomeexpenditurereport', compact('items'));
    // }
    public function jdincomeexpenditurereport()
    {
        $jd = Auth::guard('admin')->user();
        $divisionId = $jd->division_id;

        // Step 1: Get all ULBs in the JD's division from users table
        $ulbs = User::where('division_id', $divisionId)->get();

        // Step 2: Get latest income/expenditure data per ULB if it exists
        $expenditures = IncomeExpenditure::whereHas('ulb', function ($query) use ($divisionId) {
            $query->where('division_id', $divisionId);
        })
            ->with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($records) {
                return $records->sortByDesc('updated_at')->first(); // latest record
            });
        // Sort ulbs so that those with data appear first
        $sortedUlbs = $ulbs->sortByDesc(function ($ulb) use ($expenditures) {
            return isset($expenditures[$ulb->id]);
        });

        return view('jddashboard.jdincomeexpenditurereport', [
            'ulbs' => $sortedUlbs,
            'expenditures' => $expenditures,
        ]);
    }


    public function verifyincomeexpData(Request $request)
    {

        $validated = $request->validate([
            'id' => 'required|exists:income_expenditures,id',
        ]);
        $data = IncomeExpenditure::find($validated['id']);
        if ($data) {
            $data->is_jd_verify = true;
            $data->save();
            Alert::success('Success!', ' successfully verified.');
        } else {

            Alert::error('Error!', 'Record not found.');
        }
        return redirect()->back();
    }

    public function verifyallincomeexpData(Request $request)
    {

        $validated = $request->validate([
            'ids' => 'required|string',
        ]);
        $ids = explode(',', $validated['ids']);
        $salaryDetails = IncomeExpenditure::whereIn('id', $ids)->get();

        // Loop through each salary record and update the 'is_jd_verify' field to 1
        foreach ($salaryDetails as $data) {
            $data->is_jd_verify = 1;  // Set the 'is_jd_verify' field to 1
            $data->save();  // Save the updated record
        }

        // Return a success message and redirect back to the salary details page
        Alert::success('Success!', 'All Records Verified.');

        return redirect()->back();
    }
    //  {{--  ULB allotment --}}


    public function jdulballotment()
    {
        $nikay = Auth::guard('web')->user();
        // $ulbId = $nikay->id;

        // Calculate the total amount released for ChungiMadhVyay
        $items = ChungiMadhVyay::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })
            ->with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        $totalAmountReleased = [];
        foreach ($items as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleased[$ulbId] = ChungiMadhVyay::where('ulb_id', $ulbId)->sum('amount_released');
        }

        // AppatnidhiMarammat Total Amount Released
        $appatnidhi = AppatnidhiMarammat::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })
            ->with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        $totalAmountReleasedtwo = [];
        foreach ($appatnidhi as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasedtwo[$ulbId] = AppatnidhiMarammat::where('ulb_id', $ulbId)->sum('amount_released');
        }

        // MahapaurAdhyakshNidhi Total Amount Released
        $mahapaurnidhi = MahapaurAdhyakshNidhi::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })
            ->with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        $totalAmountReleasedthree = [];
        foreach ($mahapaurnidhi as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasedthree[$ulbId] = MahapaurAdhyakshNidhi::where('ulb_id', $ulbId)->sum('amount_released');
        }

        // ParshadNidhi Total Amount Released
        $parshadnidhi = ParshadNidhi::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })
            ->with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        $totalAmountReleasedfour = [];
        foreach ($parshadnidhi as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasedfour[$ulbId] = ParshadNidhi::where('ulb_id', $ulbId)->sum('amount_released');
        }

        // SwachCommando Total Amount Released
        $swachcommando = SwachCommando::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })
            ->with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        $totalAmountReleasedfive = [];
        foreach ($swachcommando as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasedfive[$ulbId] = SwachCommando::where('ulb_id', $ulbId)->sum('amount_released');
        }

        // MissionCleanCity Total Amount Released
        $cleancity = MissionCleanCity::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })
            ->with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        $totalAmountReleasedsix = [];
        foreach ($cleancity as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasedsix[$ulbId] = MissionCleanCity::where('ulb_id', $ulbId)->sum('amount_released');
        }

        // LambitVetan Total Amount Released
        $lambitvetan = LambitVetan::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })
            ->with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        $totalAmountReleasedseven = [];
        foreach ($lambitvetan as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasedseven[$ulbId] = LambitVetan::where('ulb_id', $ulbId)->sum('amount_released');
        }

        // AnyaKarya Total Amount Released
        $anyakarya = AnyaKarya::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })
            ->with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        $totalAmountReleasedeight = [];
        foreach ($anyakarya as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasedeight[$ulbId] = AnyaKarya::where('ulb_id', $ulbId)->sum('amount_released');
        }

        // PeyjalkastNivaran Total Amount Released
        $peyjalnivaran = PeyjalkastNivaran::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })
            ->with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        $totalAmountReleasednine = [];
        foreach ($peyjalnivaran as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasednine[$ulbId] = PeyjalkastNivaran::where('ulb_id', $ulbId)->sum('amount_released');
        }

        // JalPradayGirh Total Amount Released
        $jalpradaygirh = JalPradayGirh::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })
            ->with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        $totalAmountReleasedten = [];
        foreach ($jalpradaygirh as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasedten[$ulbId] = JalPradayGirh::where('ulb_id', $ulbId)->sum('amount_released');
        }

        // MudrankSulak Total Amount Released
        $mudranksulak = MudrankSulak::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })
            ->with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        $totalAmountReleasedeleven = [];
        foreach ($mudranksulak as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasedeleven[$ulbId] = MudrankSulak::where('ulb_id', $ulbId)->sum('amount_released');
        }

        // Bar Total Amount Released
        $bar = Bar::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })
            ->with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        $totalAmountReleasedtwelve = [];
        foreach ($bar as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasedtwelve[$ulbId] = Bar::where('ulb_id', $ulbId)->sum('amount_released');
        }

        // YatriKar Total Amount Released
        $yatrikar = YatriKar::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })
            ->with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        $totalAmountReleasedthirteen = [];
        foreach ($yatrikar as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasedthirteen[$ulbId] = YatriKar::where('ulb_id', $ulbId)->sum('amount_released');
        }

        // Utpadkar Total Amount Released
        $utpadkar = Utpadkar::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })
            ->with('ulb')
            ->get()
            ->groupBy('ulb_id')
            ->map(function ($ulbGroup) {
                return $ulbGroup->sortByDesc('updated_at')->first();
            });

        $totalAmountReleasedfourteen = [];
        foreach ($utpadkar as $item) {
            $ulbId = $item->ulb_id;
            $totalAmountReleasedfourteen[$ulbId] = Utpadkar::where('ulb_id', $ulbId)->sum('amount_released');
        }

        return view('jddashboard/jdulballotment', compact(
            'totalAmountReleased',
            'totalAmountReleasedtwo',
            'totalAmountReleasedthree',
            'totalAmountReleasedfour',
            'totalAmountReleasedfive',
            'totalAmountReleasedsix',
            'totalAmountReleasedseven',
            'totalAmountReleasedeight',
            'totalAmountReleasednine',
            'totalAmountReleasedten',
            'totalAmountReleasedeleven',
            'totalAmountReleasedtwelve',
            'totalAmountReleasedthirteen',
            'totalAmountReleasedfourteen',
            'items',
            'appatnidhi',
            'mahapaurnidhi',
            'parshadnidhi',
            'swachcommando',
            'cleancity',
            'lambitvetan',
            'anyakarya',
            'peyjalnivaran',
            'jalpradaygirh',
            'mudranksulak',
            'bar',
            'yatrikar',
            'utpadkar'
        ));
    }

    public function verifychungimadh(Request $request)
    {

        $validated = $request->validate([
            'id' => 'required|exists:chungi_madh_vyays,id',
        ]);
        $data = ChungiMadhVyay::find($validated['id']);

        if ($data) {

            $data->is_jd_verify = true;
            $data->save();
            Alert::success('Success!', ' successfully verified.');
        } else {
            Alert::error('Error!', 'Record not found.');
        }
        return redirect()->route('jd.ulballotment');
    }
    public function verifyAllchungimadh(Request $request)
    {

        $validated = $request->validate([
            'ids' => 'required|string',
        ]);
        $ids = explode(',', $validated['ids']);
        $salaryDetails = ChungiMadhVyay::whereIn('id', $ids)->get();

        // Loop through each salary record and update the 'is_jd_verify' field to 1
        foreach ($salaryDetails as $data) {
            $data->is_jd_verify = 1;  // Set the 'is_jd_verify' field to 1
            $data->save();  // Save the updated record
        }

        // Return a success message and redirect back to the salary details page
        Alert::success('Success!', 'All Records Verified.');

        return redirect()->route('jd.ulballotment');
    }


    public function verifyappatnidhi(Request $request)
    {

        $validated = $request->validate([
            'id' => 'required|exists:appatnidhi_marammats,id',
        ]);
        $data = AppatnidhiMarammat::find($validated['id']);

        if ($data) {

            $data->is_jd_verify = true;
            $data->save();
            Alert::success('Success!', ' successfully verified.');
        } else {
            Alert::error('Error!', 'Record not found.');
        }
        return redirect()->route('jd.ulballotment');
    }
    public function verifyAllappatnidhi(Request $request)
    {

        $validated = $request->validate([
            'ids' => 'required|string',
        ]);
        $ids = explode(',', $validated['ids']);
        $salaryDetails = AppatnidhiMarammat::whereIn('id', $ids)->get();

        // Loop through each salary record and update the 'is_jd_verify' field to 1
        foreach ($salaryDetails as $data) {
            $data->is_jd_verify = 1;  // Set the 'is_jd_verify' field to 1
            $data->save();  // Save the updated record
        }

        // Return a success message and redirect back to the salary details page
        Alert::success('Success!', 'All Records Verified.');

        return redirect()->route('jd.ulballotment');
    }

    public function verifymahapaurnidhi(Request $request)
    {

        $validated = $request->validate([
            'id' => 'required|exists:mahapaur_adhyaksh_nidhis,id',
        ]);
        $data = MahapaurAdhyakshNidhi::find($validated['id']);

        if ($data) {

            $data->is_jd_verify = true;
            $data->save();
            Alert::success('Success!', ' successfully verified.');
        } else {
            Alert::error('Error!', 'Record not found.');
        }
        return redirect()->route('jd.ulballotment');
    }
    public function verifyAllmahapaurnidhi(Request $request)
    {

        $validated = $request->validate([
            'ids' => 'required|string',
        ]);
        $ids = explode(',', $validated['ids']);
        $salaryDetails = MahapaurAdhyakshNidhi::whereIn('id', $ids)->get();

        // Loop through each salary record and update the 'is_jd_verify' field to 1
        foreach ($salaryDetails as $data) {
            $data->is_jd_verify = 1;  // Set the 'is_jd_verify' field to 1
            $data->save();  // Save the updated record
        }

        // Return a success message and redirect back to the salary details page
        Alert::success('Success!', 'All Records Verified.');

        return redirect()->route('jd.ulballotment');
    }


    public function verifyparshadnidhi(Request $request)
    {

        $validated = $request->validate([
            'id' => 'required|exists:parshad_nidhis,id',
        ]);
        $data = ParshadNidhi::find($validated['id']);

        if ($data) {

            $data->is_jd_verify = true;
            $data->save();
            Alert::success('Success!', ' successfully verified.');
        } else {
            Alert::error('Error!', 'Record not found.');
        }
        return redirect()->route('jd.ulballotment');
    }
    public function verifyAllparshadnidhi(Request $request)
    {

        $validated = $request->validate([
            'ids' => 'required|string',
        ]);
        $ids = explode(',', $validated['ids']);
        $salaryDetails = ParshadNidhi::whereIn('id', $ids)->get();

        // Loop through each salary record and update the 'is_jd_verify' field to 1
        foreach ($salaryDetails as $data) {
            $data->is_jd_verify = 1;  // Set the 'is_jd_verify' field to 1
            $data->save();  // Save the updated record
        }

        // Return a success message and redirect back to the salary details page
        Alert::success('Success!', 'All Records Verified.');

        return redirect()->route('jd.ulballotment');
    }


    public function verifyswachcommando(Request $request)
    {

        $validated = $request->validate([
            'id' => 'required|exists:swach_commandos,id',
        ]);
        $data = SwachCommando::find($validated['id']);

        if ($data) {

            $data->is_jd_verify = true;
            $data->save();
            Alert::success('Success!', ' successfully verified.');
        } else {
            Alert::error('Error!', 'Record not found.');
        }
        return redirect()->route('jd.ulballotment');
    }
    public function verifyAllswachcommando(Request $request)
    {

        $validated = $request->validate([
            'ids' => 'required|string',
        ]);
        $ids = explode(',', $validated['ids']);
        $salaryDetails = SwachCommando::whereIn('id', $ids)->get();

        // Loop through each salary record and update the 'is_jd_verify' field to 1
        foreach ($salaryDetails as $data) {
            $data->is_jd_verify = 1;  // Set the 'is_jd_verify' field to 1
            $data->save();  // Save the updated record
        }

        // Return a success message and redirect back to the salary details page
        Alert::success('Success!', 'All Records Verified.');

        return redirect()->route('jd.ulballotment');
    }


    public function verifycleancity(Request $request)
    {

        $validated = $request->validate([
            'id' => 'required|exists:mission_clean_cities,id',
        ]);
        $data = MissionCleanCity::find($validated['id']);

        if ($data) {

            $data->is_jd_verify = true;
            $data->save();
            Alert::success('Success!', ' successfully verified.');
        } else {
            Alert::error('Error!', 'Record not found.');
        }
        return redirect()->route('jd.ulballotment');
    }
    public function verifyAllcleancity(Request $request)
    {

        $validated = $request->validate([
            'ids' => 'required|string',
        ]);
        $ids = explode(',', $validated['ids']);
        $salaryDetails = MissionCleanCity::whereIn('id', $ids)->get();

        // Loop through each salary record and update the 'is_jd_verify' field to 1
        foreach ($salaryDetails as $data) {
            $data->is_jd_verify = 1;  // Set the 'is_jd_verify' field to 1
            $data->save();  // Save the updated record
        }

        // Return a success message and redirect back to the salary details page
        Alert::success('Success!', 'All Records Verified.');

        return redirect()->route('jd.ulballotment');
    }


    public function verifylambitvetan(Request $request)
    {

        $validated = $request->validate([
            'id' => 'required|exists:lambit_vetans,id',
        ]);
        $data = LambitVetan::find($validated['id']);

        if ($data) {

            $data->is_jd_verify = true;
            $data->save();
            Alert::success('Success!', ' successfully verified.');
        } else {
            Alert::error('Error!', 'Record not found.');
        }
        return redirect()->route('jd.ulballotment');
    }
    public function verifyAlllambitvetan(Request $request)
    {

        $validated = $request->validate([
            'ids' => 'required|string',
        ]);
        $ids = explode(',', $validated['ids']);
        $salaryDetails = LambitVetan::whereIn('id', $ids)->get();

        // Loop through each salary record and update the 'is_jd_verify' field to 1
        foreach ($salaryDetails as $data) {
            $data->is_jd_verify = 1;  // Set the 'is_jd_verify' field to 1
            $data->save();  // Save the updated record
        }

        // Return a success message and redirect back to the salary details page
        Alert::success('Success!', 'All Records Verified.');

        return redirect()->route('jd.ulballotment');
    }


    public function verifyanyakarya(Request $request)
    {

        $validated = $request->validate([
            'id' => 'required|exists:anya_karyas,id',
        ]);
        $data = AnyaKarya::find($validated['id']);

        if ($data) {

            $data->is_jd_verify = true;
            $data->save();
            Alert::success('Success!', ' successfully verified.');
        } else {
            Alert::error('Error!', 'Record not found.');
        }
        return redirect()->route('jd.ulballotment');
    }
    public function verifyAllanyakarya(Request $request)
    {

        $validated = $request->validate([
            'ids' => 'required|string',
        ]);
        $ids = explode(',', $validated['ids']);
        $salaryDetails = AnyaKarya::whereIn('id', $ids)->get();

        // Loop through each salary record and update the 'is_jd_verify' field to 1
        foreach ($salaryDetails as $data) {
            $data->is_jd_verify = 1;  // Set the 'is_jd_verify' field to 1
            $data->save();  // Save the updated record
        }

        // Return a success message and redirect back to the salary details page
        Alert::success('Success!', 'All Records Verified.');

        return redirect()->route('jd.ulballotment');
    }


    public function verifypeyjalnivaran(Request $request)
    {

        $validated = $request->validate([
            'id' => 'required|exists:peyjalkast_nivarans,id',
        ]);
        $data = PeyjalkastNivaran::find($validated['id']);

        if ($data) {

            $data->is_jd_verify = true;
            $data->save();
            Alert::success('Success!', ' successfully verified.');
        } else {
            Alert::error('Error!', 'Record not found.');
        }
        return redirect()->route('jd.ulballotment');
    }
    public function verifyAllpeyjalnivaran(Request $request)
    {

        $validated = $request->validate([
            'ids' => 'required|string',
        ]);
        $ids = explode(',', $validated['ids']);
        $salaryDetails = PeyjalkastNivaran::whereIn('id', $ids)->get();

        // Loop through each salary record and update the 'is_jd_verify' field to 1
        foreach ($salaryDetails as $data) {
            $data->is_jd_verify = 1;  // Set the 'is_jd_verify' field to 1
            $data->save();  // Save the updated record
        }

        // Return a success message and redirect back to the salary details page
        Alert::success('Success!', 'All Records Verified.');

        return redirect()->route('jd.ulballotment');
    }


    public function verifyjalpradaygirh(Request $request)
    {

        $validated = $request->validate([
            'id' => 'required|exists:jal_praday_girhs,id',
        ]);
        $data = JalPradayGirh::find($validated['id']);

        if ($data) {

            $data->is_jd_verify = true;
            $data->save();
            Alert::success('Success!', ' successfully verified.');
        } else {
            Alert::error('Error!', 'Record not found.');
        }
        return redirect()->route('jd.ulballotment');
    }
    public function verifyAlljalpradaygirh(Request $request)
    {

        $validated = $request->validate([
            'ids' => 'required|string',
        ]);
        $ids = explode(',', $validated['ids']);
        $salaryDetails = JalPradayGirh::whereIn('id', $ids)->get();

        // Loop through each salary record and update the 'is_jd_verify' field to 1
        foreach ($salaryDetails as $data) {
            $data->is_jd_verify = 1;  // Set the 'is_jd_verify' field to 1
            $data->save();  // Save the updated record
        }

        // Return a success message and redirect back to the salary details page
        Alert::success('Success!', 'All Records Verified.');

        return redirect()->route('jd.ulballotment');
    }


    public function verifymudranksulak(Request $request)
    {

        $validated = $request->validate([
            'id' => 'required|exists:mudrank_sulaks,id',
        ]);
        $data = MudrankSulak::find($validated['id']);

        if ($data) {

            $data->is_jd_verify = true;
            $data->save();
            Alert::success('Success!', ' successfully verified.');
        } else {
            Alert::error('Error!', 'Record not found.');
        }
        return redirect()->route('jd.ulballotment');
    }
    public function verifyAllmudranksulak(Request $request)
    {

        $validated = $request->validate([
            'ids' => 'required|string',
        ]);
        $ids = explode(',', $validated['ids']);
        $salaryDetails = MudrankSulak::whereIn('id', $ids)->get();

        // Loop through each salary record and update the 'is_jd_verify' field to 1
        foreach ($salaryDetails as $data) {
            $data->is_jd_verify = 1;  // Set the 'is_jd_verify' field to 1
            $data->save();  // Save the updated record
        }

        // Return a success message and redirect back to the salary details page
        Alert::success('Success!', 'All Records Verified.');

        return redirect()->route('jd.ulballotment');
    }


    public function verifybar(Request $request)
    {

        $validated = $request->validate([
            'id' => 'required|exists:bars,id',
        ]);
        $data = Bar::find($validated['id']);

        if ($data) {

            $data->is_jd_verify = true;
            $data->save();
            Alert::success('Success!', ' successfully verified.');
        } else {
            Alert::error('Error!', 'Record not found.');
        }
        return redirect()->route('jd.ulballotment');
    }
    public function verifyAllbar(Request $request)
    {

        $validated = $request->validate([
            'ids' => 'required|string',
        ]);
        $ids = explode(',', $validated['ids']);
        $salaryDetails = Bar::whereIn('id', $ids)->get();

        // Loop through each salary record and update the 'is_jd_verify' field to 1
        foreach ($salaryDetails as $data) {
            $data->is_jd_verify = 1;  // Set the 'is_jd_verify' field to 1
            $data->save();  // Save the updated record
        }

        // Return a success message and redirect back to the salary details page
        Alert::success('Success!', 'All Records Verified.');

        return redirect()->route('jd.ulballotment');
    }


    public function verifyyatrikar(Request $request)
    {

        $validated = $request->validate([
            'id' => 'required|exists:yatri_kars,id',
        ]);
        $data = YatriKar::find($validated['id']);

        if ($data) {

            $data->is_jd_verify = true;
            $data->save();
            Alert::success('Success!', ' successfully verified.');
        } else {
            Alert::error('Error!', 'Record not found.');
        }
        return redirect()->route('jd.ulballotment');
    }
    public function verifyAllyatrikar(Request $request)
    {

        $validated = $request->validate([
            'ids' => 'required|string',
        ]);
        $ids = explode(',', $validated['ids']);
        $salaryDetails = YatriKar::whereIn('id', $ids)->get();

        // Loop through each salary record and update the 'is_jd_verify' field to 1
        foreach ($salaryDetails as $data) {
            $data->is_jd_verify = 1;  // Set the 'is_jd_verify' field to 1
            $data->save();  // Save the updated record
        }

        // Return a success message and redirect back to the salary details page
        Alert::success('Success!', 'All Records Verified.');

        return redirect()->route('jd.ulballotment');
    }


    public function verifyutpadkar(Request $request)
    {

        $validated = $request->validate([
            'id' => 'required|exists:utpadkars,id',
        ]);
        $data = Utpadkar::find($validated['id']);

        if ($data) {

            $data->is_jd_verify = true;
            $data->save();
            Alert::success('Success!', ' successfully verified.');
        } else {
            Alert::error('Error!', 'Record not found.');
        }
        return redirect()->route('jd.ulballotment');
    }
    public function verifyAllutpadkar(Request $request)
    {

        $validated = $request->validate([
            'ids' => 'required|string',
        ]);
        $ids = explode(',', $validated['ids']);
        $salaryDetails = Utpadkar::whereIn('id', $ids)->get();

        // Loop through each salary record and update the 'is_jd_verify' field to 1
        foreach ($salaryDetails as $data) {
            $data->is_jd_verify = 1;  // Set the 'is_jd_verify' field to 1
            $data->save();  // Save the updated record
        }

        // Return a success message and redirect back to the salary details page
        Alert::success('Success!', 'All Records Verified.');

        return redirect()->route('jd.ulballotment');
    }
    public function ulblist()
    {
        $adminUser = Auth::guard('admin')->user();
        if ($adminUser && $adminUser->division_id) {

            $items = User::whereHas('district', function ($query) use ($adminUser) {
                $query->where('division_id', $adminUser->division_id);
            })
                ->with('ulb')
                ->get();
            return view('jddashboard/ulblist', compact('items'));
        }

        // If the user is not authenticated or doesn't have the division_id, you can redirect or show an error.
        // return redirect()->route('login')->with('error', 'You must be logged in to view the ULB list.');
    }


    public function jdotherinformation()
    {

        $loggedInUser = Auth::guard('admin')->user();
        $divisionId =  $loggedInUser->division_id;
        $nikay = Auth::guard('web')->user();

        $muktidhamCount = Muktidham::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })->count();
        $talabCount = TalabInformation::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })->count();
        $GradenCount = GardenInfo::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })->count();
        $Complexccount = Complexinformation::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })->count();
        $StreetlightCount = StreetlightInfo::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })->count();
        $playgroundCount = PlaygroundInfo::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })->count();
        $handpumpCount = HandpumInfo::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })->count();
        $panitankiCount = PanitankiInfo::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })->count();
        $peyjalCount = PeyjalInfo::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })->count();
        $jaljanitCount = Jaljanitbimari::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })->count();
        $trafficlightCount = TrafficLight::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })->count();
        $govtbuildingCount = GovtBuilding::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })->count();
        $rainwaterCount = RainWaterHarvesting::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })->count();

        return view('jddashboard.jdotherinformation', compact('muktidhamCount', 'talabCount', 'GradenCount', 'Complexccount', 'StreetlightCount', 'playgroundCount', 'handpumpCount', 'panitankiCount', 'peyjalCount', 'jaljanitCount', 'trafficlightCount', 'govtbuildingCount', 'rainwaterCount'));
    }

    public function jdmuktidham()
    {
        $nikay = Auth::guard('web')->user();
        $items = Muktidham::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })
            ->with('ulb')
            ->get();
        return view('jddashboard/jdmuktidham', compact('items'));
    }
    public function jdtalabinformation()
    {
        $nikay = Auth::guard('web')->user();
        $items = TalabInformation::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })
            ->with('ulb')
            ->get();
        return view('jddashboard/jdtalabinformation', compact('items'));
    }
    public function jdgardeninformation()
    {
        $nikay = Auth::guard('web')->user();
        $items = GardenInfo::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })
            ->with('ulb')
            ->get();
        return view('jddashboard/jdgardeninformation', compact('items'));
    }
    public function jdcomplexinformation()
    {
        $nikay = Auth::guard('web')->user();
        $items = Complexinformation::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })
            ->with('ulb')
            ->get();
        return view('jddashboard/jdcomplexinformation', compact('items'));
    }
    public function jdstreetlightinformation()
    {
        $nikay = Auth::guard('web')->user();
        $items = StreetlightInfo::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })
            ->with('ulb')
            ->get();
        return view('jddashboard/jdstreetlightinformation', compact('items'));
    }
    public function jdplaygroundinformation()
    {
        $nikay = Auth::guard('web')->user();
        $items = PlaygroundInfo::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })
            ->with('ulb')
            ->get();
        return view('jddashboard/jdplaygroundinformation', compact('items'));
    }
    public function jdhandpumpinformation()
    {
        $nikay = Auth::guard('web')->user();
        $items = HandpumInfo::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })
            ->with('ulb')
            ->get();
        return view('jddashboard/jdhandpumpinformation', compact('items'));
    }
    public function jdpanitankiinformation()
    {
        $nikay = Auth::guard('web')->user();
        $items = PanitankiInfo::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })
            ->with('ulb')
            ->get();
        return view('jddashboard/jdpanitankiinformation', compact('items'));
    }
    public function jdpayjalinformation()
    {
        $nikay = Auth::guard('web')->user();
        $items = PeyjalInfo::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })
            ->with('ulb')
            ->get();
        return view('jddashboard/jdpayjalinformation', compact('items'));
    }
    public function jdjaljanitbimari()
    {
        $nikay = Auth::guard('web')->user();
        $items = Jaljanitbimari::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })
            ->with('ulb')
            ->get();
        return view('jddashboard/jdjaljanitbimari', compact('items'));
    }
    public function jdtrafficlight()
    {
        $nikay = Auth::guard('web')->user();
        $items = TrafficLight::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })
            ->with('ulb')
            ->get();
        return view('jddashboard/jdtrafficlight', compact('items'));
    }
    public function jdgovtbuilding()
    {
        $nikay = Auth::guard('web')->user();
        $items = GovtBuilding::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })
            ->with('ulb')
            ->get();
        return view('jddashboard/jdgovtbuilding', compact('items'));
    }
    public function jdrainwaterharvesting()
    {
        $nikay = Auth::guard('web')->user();
        $items = RainWaterHarvesting::whereHas('ulb', function ($query) use ($nikay) {
            $query->where('users.division_id', Auth::user()->division_id);
        })
            ->with('ulb')
            ->get();
        return view('jddashboard/jdrainwaterharvesting', compact('items'));
    }
}
