<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RevenueCollection extends Model
{
    use HasFactory;

    public function ulb()
    {
        return $this->belongsTo(User::class, 'ulb_id');
    }
    public function taxType()
    {
        return $this->belongsTo(TaxType::class, 'taxtype_id');
    }
    public function division()
    {
        return $this->belongsTo(MasterDivision::class, 'division_id');
    }

    public function district()
    {
        return $this->belongsTo(MasterDistrict::class, 'district_id');
    }
    protected $fillable = [
    'unique_no',
    'month',
    'tax_type_id',
    'current_demand',
    'current_collection',
    'pending_demand',
    'pending_collection',
];
}
