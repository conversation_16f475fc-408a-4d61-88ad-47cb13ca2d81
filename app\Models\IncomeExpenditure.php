<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class IncomeExpenditure extends Model
{
    use HasFactory;

    protected $fillable = [
        'ulb_id',
        'unique_id',
        'month',
        'monthly_chungi',
        'utpadkar',
        'yatrikar',
        'bar_license',
        'mudrank_shulk',
        'own_revenue',
        'anya_madh',
        'permanent_employee_salary',
        'placement_employee_salary',
        'padhadhikari_mandeya',
        'gpf_cpf',
        'travel_allowance',
        'avkash_nakdikaran',
        'electricity_bill',
        'diesal_petrol',
        'telephone_stationary',
        'arrears',
        'advertising_expense',
        'vividh_vyay',
        'jalkarya_sandharan',   
        'bhawan_anughya_rashi_bhugtan',
        'freehold_madh_vyay',
        'nikay_nidhi_nirman_karya_vyay',

        'total_income','total_expenditure',
        'balance_amount',
        'remark',
        'attempts',
        'is_jd_verify',
    ];

    public function ulb()
    {

        return $this->belongsTo(User::class,'ulb_id');
    }
    public function district()
    {
        return $this->belongsTo(MasterDistrict::class, 'district_id');
    }
    public function division()
    {
        return $this->belongsTo(MasterDivision::class, 'division_id');
    }
}
