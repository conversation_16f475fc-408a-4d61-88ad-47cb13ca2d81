{"version": 3, "sources": ["jquery.contextMenu.css", "jquery.contextMenu.min.css"], "names": [], "mappings": "iBACA;;;;;;;;;;;;;;AAeA,2BACE,GACE,kBAAA,iBAAA,UACA,UAAA,iBAAA,UCEF,KDCE,kBAAA,iBAAA,eANJ,UAAA,iBAAA,gBCUA,sBACE,GDPA,kBAAA,iBAAA,UACE,aAAA,iBAAA,UACA,UAAA,iBAAA,UANJ,KACE,kBAAA,iBAAA,eACE,aAAA,iBAAA,eACA,UAAA,iBAAA,gBCmBJ,mBACE,GACE,kBDlBA,iBAAA,UCmBK,aAAc,iBAAiB,UDjBxC,UAAA,iBAAA,UAEE,KACA,kBAAA,iBAAA,eACA,aAAmB,iBAAA,eACnB,UAAoB,iBAAA,gBCsBtB,WDjBA,YAAA,mBACE,WAAA,OCmBA,YAAa,IDhBb,IAAA,uCCmBA,IAAK,6CAA+C,2BAA2B,CAAE,yCAA2C,eAAe,CAAE,wCAA0C,cAAc,CAAE,uCAAyC,mBAGlP,8BDjBA,QAAA,QAGA,+BACE,QAAA,QCqBF,8BACE,QAAS,QAGX,iCDjBA,QAAA,QCqBA,+BDjBE,QAAA,QCqBF,kCDjBE,QAAA,QCqBF,gCACE,QDnBA,QCsBF,+BDpBE,QAAA,QAGF,2BACE,SAAA,SCsBA,IAAK,IDpBP,KAAA,EACE,MAAA,ICsBA,YAAa,mBDpBf,UAAA,IACE,WAAA,OCsBA,YDtBA,ICuBA,YDvBA,ECwBA,MAAO,QDtBT,WAAA,OACE,kBAAkB,iBAClB,cAAA,iBACA,aAAA,iBACA,UAAA,iBCyBA,uBDvBE,YCwBF,wBDvBoB,UC0BtB,6CACE,MDvBE,KC0BJ,iDACE,MDvBE,KC0BJ,oDACE,kBD1BE,QAAA,GAAA,SACA,aAAA,QAAA,GAAA,SACA,UAAY,QAAA,GAAA,SC6BhB,yCACE,QD1BE,UC2BF,YAAa,QDzBf,YAAA,QC4BA,iDDzBE,SAAA,SACA,IAAA,IC2BA,KD1BE,EC2BF,MD1BE,IC2BF,YD1BE,YC2BF,UD1BE,IACF,WAAA,OC2BA,YD1Ba,IACb,YAAA,EC2BA,MD1BE,QC2BF,WAAY,ODzBd,kBAAA,iBACE,cAAgB,iBAChB,aAAA,iBACA,UAAA,iBC4BA,uBD3BA,YACA,wBAAqB,UC6BvB,mED1BE,MAAA,KC6BF,uED1BE,MAAA,KC8BF,0CD1BA,QAAA,UACE,YAAA,QACA,YAAA,QC6BF,4CD7ByB,8CACvB,SAAA,SACA,IAAA,KACA,KAAA,KACA,MAAA,QC+BF,+DD/BmB,iECgCjB,MAAO,KD9BT,kEAAA,oEACE,MAAA,KCmCF,mBD/BA,SAAA,SCiCE,QAAS,aD/BT,UAAA,KCiCA,UDjCA,KCkCA,QDlCA,MAAA,ECmCA,ODnCA,KCoCA,YAAa,QDlCf,UAAA,QACE,gBAAA,KACA,WAAW,KACX,OAAA,IAAA,MAAiB,QCoCjB,cAAe,KDlCjB,mBAAA,EAAA,IAAA,IAAA,eACE,WAAA,EAAsB,IAAA,IAAA,eCsCxB,mBDlCA,SAAA,SACE,mBAAe,YACf,gBAAgB,YCoCR,WAAY,YDlCtB,QAAA,KAAA,IACE,MAAA,QACA,oBAAmB,KACnB,iBAAA,KACA,gBAAA,KACA,YAAS,KACT,iBAAkB,KCsCpB,wBACE,QDpCA,ECqCA,ODrCA,MAAA,ECsCA,cDtCA,IAAA,MAAA,QCyCF,+BDrCA,kCCuCE,oBAAqB,KDrCrB,iBAAA,KACF,gBAAA,KACE,YAAoB,KAGtB,sCACE,MAAA,KCuCA,OAAQ,QDrCV,iBAAA,QCyCA,yCDrCE,MAAA,KACA,OAAA,QCuCA,iBAAkB,KAGpB,uCDrCA,MAAA,QCuCE,OAAQ,QAGV,4BACE,SDvCA,SCwCA,IAAK,IDvCL,MAAA,KACA,QAAA,ECyCA,MAAO,EDvCT,OAAA,EACE,QAAA,GCyCA,aAAc,YAAY,YAAY,YAAY,QDvCpD,aAAA,MACE,aAAa,MAAA,EAAA,MAAA,MACb,kBAAA,iBACA,cAAY,iBACZ,aAAW,iBCyCH,UAAW,iBAMrB,sCACE,QAAS,KAAK,KAIhB,4BACE,eAAgB,IAIlB,+CACA,4CACE,SAAU,SACV,IAAK,MACL,aAAc,KAGhB,0BACE,OAAQ,EAGV,0BACA,2CAEA,iCADA,mCAEE,QAAS,MACT,MAAO,KACP,mBAAoB,WACjB,gBAAiB,WACZ,WAAY,WAGtB,mCACE,OAAQ,IAGV,sCACE,IAAK,KAEL,MAAO,MACP,QAAS,KAGX,2DACE,QAAS,MAGX,wBACE,gBAAiB", "file": "jquery.contextMenu.min.css", "sourcesContent": ["@charset \"UTF-8\";\n/*!\r\n * jQuery contextMenu - Plugin for simple contextMenu handling\r\n *\r\n * Version: v2.9.2\r\n *\r\n * Authors: <AUTHORS>\n * Web: http://swisnl.github.io/jQuery-contextMenu/\r\n *\r\n * Copyright (c) 2011-2020 SWIS BV and contributors\r\n *\r\n * Licensed under\r\n *   MIT License http://www.opensource.org/licenses/mit-license\r\n *\r\n * Date: 2020-05-13T13:55:37.023Z\r\n */\n@keyframes cm-spin {\n  0% {\n    -webkit-transform: translateY(-50%) rotate(0deg);\n    transform: translateY(-50%) rotate(0deg); }\n  100% {\n    -webkit-transform: translateY(-50%) rotate(359deg);\n    transform: translateY(-50%) rotate(359deg); } }\n\n@font-face {\n  font-family: \"context-menu-icons\";\n  src: url(\"font/context-menu-icons.eot?33lxn\");\n  src: url(\"font/context-menu-icons.eot?33lxn#iefix\") format(\"embedded-opentype\"), url(\"font/context-menu-icons.woff2?33lxn\") format(\"woff2\"), url(\"font/context-menu-icons.woff?33lxn\") format(\"woff\"), url(\"font/context-menu-icons.ttf?33lxn\") format(\"truetype\");\n  font-weight: normal;\n  font-style: normal; }\n\n.context-menu-icon-add:before {\n  content: \"\\EA01\"; }\n\n.context-menu-icon-copy:before {\n  content: \"\\EA02\"; }\n\n.context-menu-icon-cut:before {\n  content: \"\\EA03\"; }\n\n.context-menu-icon-delete:before {\n  content: \"\\EA04\"; }\n\n.context-menu-icon-edit:before {\n  content: \"\\EA05\"; }\n\n.context-menu-icon-loading:before {\n  content: \"\\EA06\"; }\n\n.context-menu-icon-paste:before {\n  content: \"\\EA07\"; }\n\n.context-menu-icon-quit:before {\n  content: \"\\EA08\"; }\n\n.context-menu-icon::before {\n  color: #2980B9;\n  font-family: \"context-menu-icons\";\n  font-style: normal;\n  font-weight: normal;\n  font-size: 1em;\n  left: 0;\n  line-height: 1;\n  position: absolute;\n  text-align: center;\n  top: 50%;\n  transform: translateY(-50%);\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  width: 2em; }\n\n.context-menu-icon.context-menu-hover:before {\n  color: #FFF; }\n\n.context-menu-icon.context-menu-disabled::before {\n  color: #bbbbbb; }\n\n.context-menu-icon.context-menu-icon-loading:before {\n  animation: cm-spin 2s infinite; }\n\n.context-menu-icon.context-menu-icon--fa {\n  display: list-item;\n  font-family: inherit;\n  line-height: inherit; }\n  .context-menu-icon.context-menu-icon--fa::before {\n    color: #2980B9;\n    font-family: FontAwesome;\n    font-style: normal;\n    font-weight: normal;\n    font-size: 1em;\n    left: 0;\n    line-height: 1;\n    position: absolute;\n    text-align: center;\n    top: 50%;\n    transform: translateY(-50%);\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n    width: 2em; }\n  .context-menu-icon.context-menu-icon--fa.context-menu-hover:before {\n    color: #FFF; }\n  .context-menu-icon.context-menu-icon--fa.context-menu-disabled::before {\n    color: #bbbbbb; }\n\n.context-menu-icon.context-menu-icon--fa5 {\n  display: list-item;\n  font-family: inherit;\n  line-height: inherit; }\n  .context-menu-icon.context-menu-icon--fa5 i, .context-menu-icon.context-menu-icon--fa5 svg {\n    color: #2980B9;\n    left: 0.5em;\n    position: absolute;\n    top: 0.3em; }\n  .context-menu-icon.context-menu-icon--fa5.context-menu-hover > i, .context-menu-icon.context-menu-icon--fa5.context-menu-hover > svg {\n    color: #FFF; }\n  .context-menu-icon.context-menu-icon--fa5.context-menu-disabled i, .context-menu-icon.context-menu-icon--fa5.context-menu-disabled svg {\n    color: #bbbbbb; }\n\n.context-menu-list {\n  background: #FFF;\n  border: 1px solid #bebebe;\n  border-radius: 0.2em;\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.5);\n  display: inline-block;\n  font-family: inherit;\n  font-size: inherit;\n  list-style-type: none;\n  margin: 0.3em;\n  max-width: 26em;\n  min-width: 13em;\n  padding: 0.25em 0;\n  position: absolute; }\n\n.context-menu-item {\n  background-color: #FFF;\n  box-sizing: content-box;\n  color: #2F2F2F;\n  padding: 0.2em 2em;\n  position: relative;\n  user-select: none; }\n\n.context-menu-separator {\n  border-bottom: 1px solid #e6e6e6;\n  margin: 0.35em 0;\n  padding: 0; }\n\n.context-menu-item > label > input,\n.context-menu-item > label > textarea {\n  user-select: text; }\n\n.context-menu-item.context-menu-hover {\n  background-color: #2980B9;\n  color: #FFF;\n  cursor: pointer; }\n\n.context-menu-item.context-menu-disabled {\n  background-color: #FFF;\n  color: #bbbbbb;\n  cursor: default; }\n\n.context-menu-input.context-menu-hover {\n  cursor: default;\n  color: #2F2F2F; }\n\n.context-menu-submenu:after {\n  content: '';\n  border-style: solid;\n  border-width: .25em 0 .25em .25em;\n  border-color: transparent transparent transparent #2F2F2F;\n  height: 0;\n  position: absolute;\n  right: .5em;\n  top: 50%;\n  transform: translateY(-50%);\n  width: 0;\n  z-index: 1; }\n\n/**\r\n * Inputs\r\n */\n.context-menu-item.context-menu-input {\n  padding: .3em .6em; }\n\n/* vertically align inside labels */\n.context-menu-input > label > * {\n  vertical-align: top; }\n\n/* position checkboxes and radios as icons */\n.context-menu-input > label > input[type=\"checkbox\"],\n.context-menu-input > label > input[type=\"radio\"] {\n  margin-right: .4em;\n  position: relative;\n  top: .12em; }\n\n.context-menu-input > label {\n  margin: 0; }\n\n.context-menu-input > label,\n.context-menu-input > label > input[type=\"text\"],\n.context-menu-input > label > textarea,\n.context-menu-input > label > select {\n  box-sizing: border-box;\n  display: block;\n  width: 100%; }\n\n.context-menu-input > label > textarea {\n  height: 7em; }\n\n.context-menu-item > .context-menu-list {\n  display: none;\n  /* re-positioned by js */\n  right: -.3em;\n  top: .3em; }\n\n.context-menu-item.context-menu-visible > .context-menu-list {\n  display: block; }\n\n.context-menu-accesskey {\n  text-decoration: underline; }\n", "@charset \"UTF-8\";/*!\r\n * jQuery contextMenu - Plugin for simple contextMenu handling\r\n *\r\n * Version: v2.9.0\r\n *\r\n * Authors: <AUTHORS>\n * Web: http://swisnl.github.io/jQuery-contextMenu/\r\n *\r\n * Copyright (c) 2011-2020 SWIS BV and contributors\r\n *\r\n * Licensed under\r\n *   MIT License http://www.opensource.org/licenses/mit-license\r\n *\r\n * Date: 2020-05-06T18:55:06.258Z\r\n */@-webkit-keyframes cm-spin{0%{-webkit-transform:translateY(-50%) rotate(0);transform:translateY(-50%) rotate(0)}100%{-webkit-transform:translateY(-50%) rotate(359deg);transform:translateY(-50%) rotate(359deg)}}@-o-keyframes cm-spin{0%{-webkit-transform:translateY(-50%) rotate(0);-o-transform:translateY(-50%) rotate(0);transform:translateY(-50%) rotate(0)}100%{-webkit-transform:translateY(-50%) rotate(359deg);-o-transform:translateY(-50%) rotate(359deg);transform:translateY(-50%) rotate(359deg)}}@keyframes cm-spin{0%{-webkit-transform:translateY(-50%) rotate(0);-o-transform:translateY(-50%) rotate(0);transform:translateY(-50%) rotate(0)}100%{-webkit-transform:translateY(-50%) rotate(359deg);-o-transform:translateY(-50%) rotate(359deg);transform:translateY(-50%) rotate(359deg)}}@font-face{font-family:context-menu-icons;font-style:normal;font-weight:400;src:url(font/context-menu-icons.eot?33lxn);src:url(font/context-menu-icons.eot?33lxn#iefix) format(\"embedded-opentype\"),url(font/context-menu-icons.woff2?33lxn) format(\"woff2\"),url(font/context-menu-icons.woff?33lxn) format(\"woff\"),url(font/context-menu-icons.ttf?33lxn) format(\"truetype\")}.context-menu-icon-add:before{content:\"\\EA01\"}.context-menu-icon-copy:before{content:\"\\EA02\"}.context-menu-icon-cut:before{content:\"\\EA03\"}.context-menu-icon-delete:before{content:\"\\EA04\"}.context-menu-icon-edit:before{content:\"\\EA05\"}.context-menu-icon-loading:before{content:\"\\EA06\"}.context-menu-icon-paste:before{content:\"\\EA07\"}.context-menu-icon-quit:before{content:\"\\EA08\"}.context-menu-icon::before{position:absolute;top:50%;left:0;width:2em;font-family:context-menu-icons;font-size:1em;font-style:normal;font-weight:400;line-height:1;color:#2980b9;text-align:center;-webkit-transform:translateY(-50%);-ms-transform:translateY(-50%);-o-transform:translateY(-50%);transform:translateY(-50%);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.context-menu-icon.context-menu-hover:before{color:#fff}.context-menu-icon.context-menu-disabled::before{color:#bbb}.context-menu-icon.context-menu-icon-loading:before{-webkit-animation:cm-spin 2s infinite;-o-animation:cm-spin 2s infinite;animation:cm-spin 2s infinite}.context-menu-icon.context-menu-icon--fa{display:list-item;font-family:inherit;line-height:inherit}.context-menu-icon.context-menu-icon--fa::before{position:absolute;top:50%;left:0;width:2em;font-family:FontAwesome;font-size:1em;font-style:normal;font-weight:400;line-height:1;color:#2980b9;text-align:center;-webkit-transform:translateY(-50%);-ms-transform:translateY(-50%);-o-transform:translateY(-50%);transform:translateY(-50%);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.context-menu-icon.context-menu-icon--fa.context-menu-hover:before{color:#fff}.context-menu-icon.context-menu-icon--fa.context-menu-disabled::before{color:#bbb}.context-menu-icon.context-menu-icon--fa5{display:list-item;font-family:inherit;line-height:inherit}.context-menu-icon.context-menu-icon--fa5 i,.context-menu-icon.context-menu-icon--fa5 svg{position:absolute;top:.3em;left:.5em;color:#2980b9}.context-menu-icon.context-menu-icon--fa5.context-menu-hover>i,.context-menu-icon.context-menu-icon--fa5.context-menu-hover>svg{color:#fff}.context-menu-icon.context-menu-icon--fa5.context-menu-disabled i,.context-menu-icon.context-menu-icon--fa5.context-menu-disabled svg{color:#bbb}.context-menu-list{position:absolute;display:inline-block;min-width:13em;max-width:26em;padding:.25em 0;margin:.3em;font-family:inherit;font-size:inherit;list-style-type:none;background:#fff;border:1px solid #bebebe;border-radius:.2em;-webkit-box-shadow:0 2px 5px rgba(0,0,0,.5);box-shadow:0 2px 5px rgba(0,0,0,.5)}.context-menu-item{position:relative;-webkit-box-sizing:content-box;-moz-box-sizing:content-box;box-sizing:content-box;padding:.2em 2em;color:#2f2f2f;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;background-color:#fff}.context-menu-separator{padding:0;margin:.35em 0;border-bottom:1px solid #e6e6e6}.context-menu-item>label>input,.context-menu-item>label>textarea{-webkit-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text}.context-menu-item.context-menu-hover{color:#fff;cursor:pointer;background-color:#2980b9}.context-menu-item.context-menu-disabled{color:#bbb;cursor:default;background-color:#fff}.context-menu-input.context-menu-hover{color:#2f2f2f;cursor:default}.context-menu-submenu:after{position:absolute;top:50%;right:.5em;z-index:1;width:0;height:0;content:'';border-color:transparent transparent transparent #2f2f2f;border-style:solid;border-width:.25em 0 .25em .25em;-webkit-transform:translateY(-50%);-ms-transform:translateY(-50%);-o-transform:translateY(-50%);transform:translateY(-50%)}.context-menu-item.context-menu-input{padding:.3em .6em}.context-menu-input>label>*{vertical-align:top}.context-menu-input>label>input[type=checkbox],.context-menu-input>label>input[type=radio]{position:relative;top:.12em;margin-right:.4em}.context-menu-input>label{margin:0}.context-menu-input>label,.context-menu-input>label>input[type=text],.context-menu-input>label>select,.context-menu-input>label>textarea{display:block;width:100%;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}.context-menu-input>label>textarea{height:7em}.context-menu-item>.context-menu-list{top:.3em;right:-.3em;display:none}.context-menu-item.context-menu-visible>.context-menu-list{display:block}.context-menu-accesskey{text-decoration:underline}\r\n/*# sourceMappingURL=jquery.contextMenu.min.css.map */\r\n"]}