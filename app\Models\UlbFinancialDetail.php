<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UlbFinancialDetail extends Model
{
    use HasFactory;
    
    protected $table = 'ulb_financial_details';
    
    protected $fillable = [
        'ulb_id',
        'annual_budget',
        'property_tax_demand',
        'property_tax_collection',
        'water_charges_demand',
        'water_charges_collection',
        'total_households',
        'total_water_tap_connections',
        'water_tap_coverage_percentage',
        'water_crisis_ward_numbers',
        'total_overhead_tanks',
        'overhead_tanks_capacity',
        'total_wtp',
        'wtp_capacity',
        'user_fees_demand',
        'user_fees_collection',
        'total_households_user_fees',
        'total_households_door_to_door',
        'total_households_as_per_demand',
        'total_households_as_per_census',
        'door_to_door_expenditure',
        'grants_from_government',
        'other_revenue_sources',
        'expenditure_salaries',
        'expenditure_infrastructure',
        'expenditure_sanitation',
        'expenditure_water_supply',
        'other_expenditures',
        'attempts'
    ];
    
    public function ulb()
    {
        return $this->belongsTo(User::class, 'ulb_id');
    }
}
