<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Gym extends Model
{
    use HasFactory;
    public function ulb()
    {
        return $this->belongsTo(User::class,'ulb_id');
    }
    public function division()
    {
        return $this->belongsTo(MasterDivision::class, 'division_id');
    }
    protected $fillable = [
        'dateInput',
        'gyms_type',
        'gyms_total_capacity',
        'gyms_latitude',
        'gyms_longitude',
        'gyms_remark',
        'unique_id',
        'ward_no',
        'ward_name'
    ];
}

