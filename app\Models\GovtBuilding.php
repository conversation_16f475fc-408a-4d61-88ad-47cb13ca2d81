<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class GovtBuilding extends Model
{
    use HasFactory;
    protected $fillable = [
        'ulb_id',
        'unique_id',
        'financial_year',
        'dateInput',
        'ward_no',
        'ward_name',
        'type_of_building',
        'name_of_building',
        'year_of_establisment',
        'longitude',
        'latitude',
        'floor_area',
        'no_of_floors',
        'parking_availability',
        'parking_capacity',
        'elevator_availability',
        'accessibility_features',
        'power_backup',
        'drinking_water',
        'washroom',
        'status',
        'attempt',
        'is_jd_verify',
    ];
    public function ulb()
    {
        return $this->belongsTo(User::class,'ulb_id');
    }
    public function division()
    {
        return $this->belongsTo(MasterDivision::class, 'division_id');
    }

}
