<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class jdempregistration extends Model
{
    use HasFactory;
    protected $fillable = [
        'current_jd', 'gender','employee_photo','post_name', 'post_number', 'payscale', 'employee_name', 'father_name','employee_type', 
        'birth_date', 'caste', 'qualification', 'work_start_date', 'current_designation', 
        'designation_date', 'joining_date', 'suspension_status', 'original_ulb_name', 'department_name',
        'vacant_post_count', 'phone_number', 'mobile_number', 'email', 'temporary_address', 
        'permanent_address', 'remarks', 'reference_id','registration_no','employee_ml','employee_el','employee_ol','employee_cl','suspension_date','suspension_order_no',
        'suspension_letter','suspension_remark','bahal_date','bahal_order_no','bahal_letter','bahal_remark','e_hrms_email','other_office'
    ];


    public function ulb()
    {
        return $this->belongsTo(User::class, 'current_ulb');
    }
    public function ulbnew()
    {
        return $this->belongsTo(User::class, 'original_ulb_name');
    }
   
    public function post()
    {
        return $this->belongsTo(EmployeePost::class, 'post_name');
    }

    public function payscalename()
    {
        return $this->belongsTo(Payscale::class, 'payscale');
    }

    public function employeeType()
    {
        return $this->belongsTo(EmployeeType::class, 'employee_type');
    }

    public function qualificationtype()
    {
        return $this->belongsTo(Qualification::class, 'qualification');
    }
    public function jdname()
    {
        return $this->belongsTo(Admin::class, 'current_jd','id');
    }
    public function jdnameprevious()
    {
        return $this->belongsTo(Admin::class, 'previous_jd');
    }
    
}
