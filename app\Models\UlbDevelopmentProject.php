<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UlbDevelopmentProject extends Model
{
    use HasFactory;

    protected $fillable = [
        'ulb_id',
        'finance_total_works',
        'finance_completed',
        'finance_in_progress',
        'finance_not_started',
        'finance_tender_floated',
        'finance_tender_not_floated',
        'adho_total_works',
        'adho_completed',
        'adho_in_progress',
        'adho_not_started',
        'adho_tender_floated',
        'adho_tender_not_floated',
        'rajya_total_works',
        'rajya_completed',
        'rajya_in_progress',
        'rajya_not_started',
        'rajya_tender_floated',
        'rajya_tender_not_floated',
        'completed_projects',
        'has_smart_city_projects',
        'smart_city_projects_details',
        'has_amrut_projects',
        'amrut_projects_details',
        'has_pmay_projects',
        'pmay_projects_details',
        'has_swachh_bharat_projects',
        'swachh_bharat_projects_details',
        'attempts',
    ];

    protected $casts = [
        'has_smart_city_projects' => 'boolean',
        'has_amrut_projects' => 'boolean',
        'has_pmay_projects' => 'boolean',
        'has_swachh_bharat_projects' => 'boolean',
    ];

    /**
     * Get the user that owns the record.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'ulb_id');
    }
}
