<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Log;

use App\Models\AnyaKarya;
use App\Models\AppatnidhiMarammat;
use App\Models\BakayaRashi;
use App\Models\Bar;
use App\Models\BpVerification;
use App\Models\ChungiMadhVyay;
use App\Models\Complexinformation;
use App\Models\ElectricityBill;
use App\Models\ElectricityYearlyData;
use App\Models\Employee;
use App\Models\EmployeePost;
use App\Models\EmployeeType;
use App\Models\GardenInfo;
use App\Models\HandpumInfo;
use App\Models\JalPradayGirh;
use App\Models\LambitVetan;
use App\Models\RevenueCollection;
use App\Models\LeaveApply;
use App\Models\MahapaurAdhyakshNidhi;
use App\Models\MissionCleanCity;
use App\Models\MudrankSulak;
use App\Models\Muktidham;
use App\Models\ParshadNidhi;
use App\Models\PeyjalkastNivaran;
use App\Models\SalaryDetail;
use App\Models\User;
use App\Models\Utpadkar;
use App\Models\YatriKar;
use App\Models\NewMeterRegistration;
use App\Models\PanitankiInfo;
use App\Models\Payscale;
use App\Models\PeyjalInfo;
use App\Models\PlaygroundInfo;
use App\Models\Qualification;
use App\Models\RevenueDemand;
use App\Models\SalaryDetailPlacement;
use App\Models\StreetlightInfo;
use App\Models\SwachCommando;
use App\Models\TalabInformation;
use App\Models\Jaljanitbimari;
use App\Models\TaxType;
use App\Models\UlbBasicDetail;
use App\Models\UlbAdministrativeDetail;
use App\Models\UlbFinancialDetail;
use App\Models\UlbInfrastructureService;
use App\Models\UlbDevelopmentProject;
use App\Models\UlbDigitalGovernance;
use App\Models\CitizenParticipation;
use App\Models\EnvironmentalInitiative;
use App\Models\RecreationalFacility;
use App\Models\InfrastructureWork;
use App\Models\InfrastructureWorkDetail;
use App\Models\DigitalSmartFeature;
use App\Models\GouthanGovdhamKanjiHouse;
use App\Models\GovtBuilding;
use App\Models\Gym;
use App\Models\IncomeExpenditure;
use App\Models\RainWaterHarvesting;
use App\Models\SlrmCenterBhawan;
use App\Models\Stp;
use App\Models\Toilet;
use App\Models\TrafficLight;
use App\Models\Wtp;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use RealRashid\SweetAlert\Facades\Alert;
use Illuminate\support\Facades\Validator;
use Illuminate\Support\Facades\Hash;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;


use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class UlbController extends Controller
{
    public function Ulbdashboard()
    {
        $loggedInUser = Auth::guard('web')->user();

        $ulbId = $loggedInUser->id;
        $employeeCountRegular = Employee::where('current_ulb', $ulbId)
            ->whereHas('employeeType', function ($query) {
                $query->where('type', 'नियमित');
            })->count();
        $employeeCountPlacement = Employee::where('current_ulb', $ulbId)
            ->whereHas('employeeType', function ($query) {
                $query->where('type', 'एजेंसी के माध्यम से');
            })->count();
        $leavecount = LeaveApply::where('ulb_id', $ulbId)->count();
        $regularsalarycount = SalaryDetail::where('ulb_id', $ulbId)->count();
        $placementsalarycount = SalaryDetailPlacement::where('ulb_id', $ulbId)->count();
        $metercount = NewMeterRegistration::where('ulb_id', $ulbId)->where('meter_status', 'चालू')->count();
        $metercounttwo = NewMeterRegistration::where('ulb_id', $ulbId)->where('meter_status', 'बंद')->count();
        $previousPaidBillAmount = ElectricityBill::where('ulb_id', $ulbId)
            ->latest('created_at')
            ->value('total_amount') ?? 0;
        $previousPaidYearlyBillAmount = ElectricityYearlyData::where('ulb_id', $ulbId)
            ->latest('created_at')
            ->value('total_amount') ?? 0;
        $previoussalaryamount = SalaryDetail::where('ulb_id', $ulbId)
            ->orderBy('created_at', 'desc')->first();
        $previousSalaryPaidAmount = $previoussalaryamount && is_numeric($previoussalaryamount->total_amount_paid)
            ? (float)$previoussalaryamount->total_amount_paid
            : 0;
        $totalcurrentdemand = RevenueDemand::where('ulb_id', $ulbId)
            ->sum('current_demand');
        $totalpendingdemand = RevenueDemand::where('ulb_id', $ulbId)
            ->sum('pending_demand');
        $totalcurrentcollections = RevenueCollection::where('ulb_id', $ulbId)
            ->whereIn('id', function ($query) use ($ulbId) {
                $query->selectRaw('max(id)')
                    ->from('revenue_collections')
                    ->where('ulb_id', $ulbId)
                    ->groupBy('taxtype_id');
            })
            ->sum('current_collection');


        $totalpendingcollections = RevenueCollection::where('ulb_id', $ulbId)
            ->whereIn('id', function ($query) use ($ulbId) {
                $query->selectRaw('max(id)')
                    ->from('revenue_collections')
                    ->where('ulb_id', $ulbId)
                    ->groupBy('taxtype_id');
            })
            ->sum('pending_collection');

        // $totalChungiAmountReleased = ChungiMadhVyay::where('ulb_id', $ulbId)
        //     ->sum('amount_released');
        $totalChungiAmountReleased = ChungiMadhVyay::where('ulb_id', $ulbId)
            ->whereNotNull('month')
            ->sum('amount_released');

        $totalChungiExpenditure = ChungiMadhVyay::where('ulb_id', $ulbId)
            ->latest('updated_at')
            ->value('expenditure_amount') ?? 0;

        $totalChungiBalance = ChungiMadhVyay::where('ulb_id', $ulbId)
            ->latest('updated_at')
            ->value('balance_amount') ?? 0;
        // $totalChungiBalance = ChungiMadhVyay::where('ulb_id', $ulbId)
        //     ->sum('balance_amount');


        return view('ulbdashboard/Ulbdashboard', compact(
            'leavecount',
            'regularsalarycount',
            'placementsalarycount',
            'previousPaidBillAmount',
            'previousPaidYearlyBillAmount',
            'previoussalaryamount',
            'previousSalaryPaidAmount',
            'totalcurrentdemand',
            'totalpendingdemand',
            'totalcurrentcollections',
            'totalpendingcollections',
            'metercount',
            'metercounttwo',
            'totalChungiExpenditure',
            'totalChungiAmountReleased',
            'totalChungiBalance',
            'employeeCountPlacement',
            'employeeCountRegular'
        ));
    }

    public function leaveapplication()
    {
        $nikays = User::where('status', 'active')->get();
        $nikay = Auth::guard('web')->user();
        $item = LeaveApply::where('ulb_id', $nikay->id)->get();
        // // $officerTypes = Employee::distinct()->pluck('employee_type');
        // $employeePostIds = Employee::distinct()->pluck('post_name');

        // // Get the employee_type names from employee_types table
        // $employeePost = EmployeePost::whereIn('post_name', $employeePostIds)->get();


        // $employeePosts = Employee::with('employeePost') // Eager load employee's post
        //     ->distinct()          // Get unique posts
        //     ->pluck('employees.post_name');

        $employeePostIds = Employee::pluck('post_name')->unique();
        $employeePosts = EmployeePost::whereIn('id', $employeePostIds)->get();

        return view('ulbdashboard/leaveapplication', compact('nikay', 'item', 'nikays', 'employeePosts'));
    }

    public function ulbprofile()
    {
        $nikay = Auth::guard('web')->user();
        $item = User::where('id', $nikay->id)->get();
        $userdata = User::find($nikay->id);
        $office_photo = $userdata->office_photo;
        //    dd($officephoto);
        return view('ulbdashboard/ulbprofile', compact('item', 'office_photo'));
    }

    public function UlbPasswordUpdate(Request $request, $id)
    {
        // Validate incoming request data
        $validatedData = $request->validate([
            'cmo_name' => 'required|string|max:255',
            'cmo_mobile' => 'required|digits:10',
            'new_password' => 'nullable|string|min:6', // Made password optional
            'office_address' => 'required|string|max:255',
            'office_photo' => 'nullable|image|mimes:jpeg,jpg,png|max:50',
        ]);

        // Fetch the ULB registration instance by ID
        $ulbRegistration = User::find($id);
        if (!$ulbRegistration) {
            return back()->withErrors(['ulb' => 'ULB not found'])->withInput();
        }

        // Update fields
        $ulbRegistration->cmo_name = $validatedData['cmo_name'];
        $ulbRegistration->cmo_mobile = $validatedData['cmo_mobile'];
        $ulbRegistration->office_address = $validatedData['office_address'];

        // Update password only if provided
        if (!empty($validatedData['new_password'])) {
            $ulbRegistration->password = Hash::make($validatedData['new_password']);
        }

        // Handle file upload
        if ($request->hasFile('office_photo')) { // Corrected field name
            $imagePath = $request->file('office_photo')->store('images', 'public');
            $ulbRegistration->office_photo = $imagePath;
        }

        // Save updated data
        try {
            $ulbRegistration->save();
        } catch (\Exception $e) {
            return back()->withErrors(['save' => 'Failed to update profile: ' . $e->getMessage()])->withInput();
        }

        Alert::success('success', 'Profile updated successfully');
        return back();
    }
    public function leaveapplicationsubmit(Request $request)
    {
        $leave_number = mt_rand(1000, 9999);
        $rules = ['leave_number' => 'unique:leave_applies,leave_number'];
        $validator = Validator::make(['leave_number' => $leave_number], $rules);
        if ($validator->fails()) {
            return $this->submit($request);
        }
        $request->validate([

            'type_of_officer' => 'required',
            'name_of_officer' => 'required',
            // 'post' => 'required',
            'date_inwhich_leave_applied' => 'required|date',
            'leave_type' => 'required',
            'leave_date_from' => 'required',
            'leave_date_to' => 'required',
            'name_of_officer_approvedleave' => 'required',
            'post_of_officer_approvedleave' => 'required',
            'charged_officer_name' => 'required',
            'charged_officer_post' => 'required',
        ]);

        if (strtotime($request->input('date_inwhich_leave_applied')) < strtotime(date('Y-m-d'))) {
            return redirect()->back()->withErrors(['date_inwhich_leave_applied' => 'The date must be today or later.']);
        }
        $nikay = Auth::guard('web')->user();

        $data = new LeaveApply();

        $data->ulb_id = $nikay->id;
        $data->leave_number = $leave_number;
        $data->type_of_officer = $request->input('type_of_officer');
        $data->name_of_officer = $request->input('name_of_officer');
        // $data->post = $request->input('post');
        $data->date_inwhich_leave_applied = $request->input('date_inwhich_leave_applied');
        $data->leave_type = $request->input('leave_type');
        $data->leave_date_from = $request->input('leave_date_from');
        $data->leave_date_to = $request->input('leave_date_to');
        $data->name_of_officer_approvedleave = $request->input('name_of_officer_approvedleave');
        $data->post_of_officer_approvedleave = $request->input('post_of_officer_approvedleave');
        $data->charged_officer_name = $request->input('charged_officer_name');
        $data->charged_officer_post = $request->input('charged_officer_post');

        $data->save();
        Alert::success('success', 'Data submitted successfully');

        return back();
    }

    public function employementregister()
    {

        $loggedInUlbId = Auth::guard('web')->user()->id;
        $ulbs = User::all();
        $employeeposts = EmployeePost::all();
        $employeetypes = EmployeeType::all();
        $payscale = Payscale::all();
        // $qualifications = Qualification::all();
        return view('ulbdashboard.employementregister', compact('loggedInUlbId', 'ulbs', 'employeeposts', 'employeetypes', 'payscale'));
    }


    public function employementregistersubmit(Request $request)
    {
        // dd($request);
        $validatedData = $request->validate([
            'employee_photo' => 'required|image|mimes:jpeg,jpg,png|max:50',
            'current_ulb' => 'required|integer',
            'post_name' => 'required|integer',
            // 'post_number' => 'nullable|integer',
            'payscale' => 'required|integer',
            'employee_name' => 'required|string|max:255',
            'father_name' => 'required|string|max:255',
            'employee_type' => 'required|integer',
            'birth_date' => 'required|date',
            'gender' => 'required|in:male,female,other',
            'caste' => 'required|string|max:255',
            'qualification' => 'required|string',
            'work_start_date' => 'required|date',
            'current_designation' => 'required|string',
            'joining_date' => 'required|date',
            'original_ulb_name' => 'nullable|integer',
            'other_office' => 'nullable|string',
            'department_name' => 'required|string',
            'mobile_number' => 'required|string|max:10',
            'email' => 'required|email',
            'temporary_address' => 'required|string',
            'permanent_address' => 'required|string',
            'employee_cl' => 'nullable',
            'employee_ol' => 'nullable',
            'employee_el' => 'nullable',
            'employee_ml' => 'nullable',
            'e_hrms_email' => 'required|string',
            'remarks' => 'nullable|string',
            'reference_id' => [
                'nullable',
                'string',
                'size:12',
                'unique:employees,reference_id',
            ]
        ]);

        if (!$request->filled('original_ulb_name') && !$request->filled('other_office')) {
            return back()->withErrors([
                'original_ulb_name' => 'कृपया निकाय चुनें या अन्य कार्यालय का नाम दर्ज करें।',
                'other_office' => 'कृपया निकाय चुनें या अन्य कार्यालय का नाम दर्ज करें।',
            ])->withInput();
        }
        if ($request->hasFile('employee_photo')) {
            $file = $request->file('employee_photo');
            $filename = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('uploads/employee_photos', $filename, 'public'); // Store file in 'storage/app/public/uploads/employee_photos'
            $validatedData['employee_photo'] = $filePath;
        }
        $nikay = Auth::guard('web')->user();
        $ulbidcode = $nikay->ulbidcode;

        do {
            $randomNumber = rand(10, 9999);
            $registrationNo = $ulbidcode . $randomNumber;
        } while (Employee::where('current_ulb', $nikay->id)->where('registration_no', $registrationNo)->exists());

        $validatedData['registration_no'] = $registrationNo;
        $employee = Employee::create($validatedData);
        // dd($employee);

        Alert::success('Success!', 'Employee data submitted successfully!');

        return redirect()->route('employeereport')->with('success', 'Employee data Submitted successfully!');
    }

    public function employeeedit($id)
    {

        $loggedInUlbId = Auth::guard('web')->user()->id;
        $employee = Employee::findOrFail($id);
        $ulbs = User::all();
        $employeeposts = EmployeePost::all();
        $employeetypes = EmployeeType::all();
        $payscale = Payscale::all();
        // $qualifications = Qualification::all();

        return view('ulbdashboard.employeeedit', compact(
            'employee',
            'loggedInUlbId',
            'ulbs',
            'employeeposts',
            'employeetypes',
            'payscale',

        ));
    }

    public function employeeupdate(Request $request, $id)
    {
        $employee = Employee::find($id);

        $validatedData = $request->validate([
            'post_name' => 'required|integer',
            'employee_photo' => 'nullable|image|mimes:jpeg,jpg,png|max:50',
            'payscale' => 'required|integer',
            'employee_name' => 'required|string|max:255',
            'father_name' => 'required|string|max:255',
            'gender' => 'required|in:male,female,other',
            'employee_type' => 'required|integer',
            'birth_date' => 'required|date',
            'caste' => 'required|string|max:255',
            'qualification' => 'required|string',
            'work_start_date' => 'required|date',
            'current_designation' => 'required|string',
            'joining_date' => 'required|date',
            'original_ulb_name' => 'required|integer',
            'department_name' => 'required|string',
            'mobile_number' => 'required|string|max:15',
            'email' => 'required|email',
            'temporary_address' => 'required|string',
            'permanent_address' => 'required|string',
            'employee_cl' => 'nullable',
            'employee_ol' => 'nullable',
            'employee_el' => 'nullable',
            'employee_ml' => 'nullable',
            'e_hrms_email' => 'required|string',
            'remarks' => 'nullable|string',
            'reference_id' => 'nullable|string',
        ]);

        if ($request->hasFile('employee_photo')) {
            $file = $request->file('employee_photo');
            $filename = time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('uploads/employee_photos', $filename, 'public');
            $validatedData['employee_photo'] = $filePath;  // Update the photo path
        } else {
            // Retain the old photo path if no new photo is uploaded
            $filePath = $employee->employee_photo;
        }
        $newUlbId = $request->input('current_ulb');
        if ($employee->current_ulb != $newUlbId) {
            $employee->previous_ulb = $employee->current_ulb;
            $employee->is_jd_verify = false;
        }
        $employee->employee_photo = $filePath;
        $employee->current_ulb = $newUlbId;
        $employee->post_name = $request->input('post_name');
        // $employee->post_number = $request->input('post_number');
        $employee->payscale = $request->input('payscale');
        $employee->employee_name = $request->input('employee_name');
        $employee->father_name = $request->input('father_name');
        $employee->gender = $request->input('gender');
        $employee->employee_type = $request->input('employee_type');
        $employee->birth_date = $request->input('birth_date');
        $employee->caste = $request->input('caste');
        $employee->qualification = $request->input('qualification');
        $employee->work_start_date = $request->input('work_start_date');
        $employee->current_designation = $request->input('current_designation');
        // $employee->designation_date = $request->input('designation_date');
        $employee->joining_date = $request->input('joining_date');
        // $employee->suspension_status = $request->input('suspension_status');
        $employee->original_ulb_name = $request->input('original_ulb_name');
        $employee->department_name = $request->input('department_name');
        $employee->phone_number = $request->input('phone_number');
        $employee->mobile_number = $request->input('mobile_number');
        $employee->email = $request->input('email');
        $employee->temporary_address = $request->input('temporary_address');
        $employee->permanent_address = $request->input('permanent_address');
        $employee->remarks = $request->input('remarks');
        $employee->reference_id = $request->input('reference_id');
        if (in_array($employee->employee_type, [1, 5])) {
            $employee->employee_cl = $request->input('employee_cl');
            $employee->employee_ol = $request->input('employee_ol');
            $employee->employee_el = $request->input('employee_el');
            $employee->employee_ml = $request->input('employee_ml');
        }

        $employee->save();
        Alert::success('Success!', 'Application Updated successfully');
        return redirect()->route('employeereport')->with('success', 'Employee data updated successfully!');
    }

    public function salarydetails()
    {

        $nikay = Auth::guard('web')->user();
        $item = SalaryDetail::where('ulb_id', $nikay->id)->get();
        $salary = SalaryDetailPlacement::where('ulb_id', $nikay->id)->get();
        return view('ulbdashboard/salarydetails', compact('item', 'salary', 'nikay'));
    }
    public function salarydetailsubmit(Request $request)
    {
        $salary_number = mt_rand(1000, 9999);
        $rules = ['salary_number_regular' => 'unique:salary_details,salary_number_regular'];
        $validator = Validator::make(['salary_number_regular' => $salary_number], $rules);
        if ($validator->fails()) {
            return $this->submit($request);
        }
        $nikay = Auth::guard('web')->user();

        $request->validate(
            [

                'total_permanent_employee' => 'required',
                'month_upto_which_salary_paid' => 'required',
                'date_of_payement' => 'required',
                'total_amount_paid' => 'required',
                'employees_contribution' => 'required',
                'employer_contribution' => 'required',
                'voucher_no' => 'required',
            ],
            [
                'total_permanent_employee.required' => "* कुल नियमित कर्मचारी की संख्या",
                'month_upto_which_salary_paid.required' => '*किस माह तक का वेतन भुकतान किया गया',
                'date_of_payement.required' => '*किस दिनांक को भुगतान किया गया',
                'total_amount_paid.required' => '*कुल वेतन भुगतान राशि',
                'employees_contribution.required' => '*कर्मचारी का अंशदान',
                'employer_contribution.required' => '*नियोक्ता का अंशदान',
                'voucher_no.required' => '*Voucher No',
            ]
        );
        $data = new SalaryDetail();
        $data->ulb_id = $nikay->id;
        $data->salary_number_regular = $salary_number;
        $data->total_permanent_employee = $request->input('total_permanent_employee');
        $data->month_upto_which_salary_paid = $request->input('month_upto_which_salary_paid');
        $data->date_of_payement = $request->input('date_of_payement');
        $data->total_amount_paid = $request->input('total_amount_paid');
        $data->employees_contribution = $request->input('employees_contribution');
        $data->employer_contribution = $request->input('employer_contribution');
        $data->voucher_no = $request->input('voucher_no');

        $data->save();

        Alert::success('Success!', 'Application submitted successfully');
        return back();
    }


    public function salarydetailsplacementsubmit(Request $request)
    {
        $salary_number = mt_rand(1000, 9999);
        $rules = ['salary_number_placement' => 'unique:salary_detail_placements,salary_number_placement'];
        $validator = Validator::make(['salary_number_placement' => $salary_number], $rules);
        if ($validator->fails()) {
            return $this->submit($request);
        }
        $nikay = Auth::guard('web')->user();

        $request->validate(
            [
                'total_placement_employee' => 'required',
                'month_upto_which_salary_paid_placement' => 'required',
                'date_of_payement_placement' => 'nullable',
                'total_amount_paid_placement' => 'required',
                'placement_agency_name' => 'required',
                'pf_amount' => 'required',
                'esic_amount' => 'required',
            ],
            [
                'total_placement_employee.required' => '*कुल प्लेसमेंट कर्मचारी की संक्या',
                'month_upto_which_salary_paid_placement.required' => '*किस माह तक का वेतन भुकतान किया गया',
                'date_of_payement_placement.required' => '*किस दिनांक को भुगतान किया गया',
                'total_amount_paid_placement.required' => '*कुल वेतन भुगतान राशि',
                'placement_agency_name.required' => '*Placement Agency Name',
                'pf_amount.required' => '*P.F Amount',
                'esic_amount.required' => '*ESIC Amount',

            ]
        );
        $data = new SalaryDetailPlacement();
        $data->ulb_id = $nikay->id;
        $data->salary_number_placement = $salary_number;
        $data->total_placement_employee = $request->input('total_placement_employee');
        $data->month_upto_which_salary_paid_placement = $request->input('month_upto_which_salary_paid_placement');
        $data->date_of_payement_placement = $request->input('date_of_payement_placement');
        $data->total_amount_paid_placement = $request->input('total_amount_paid_placement');
        $data->placement_agency_name = $request->input('placement_agency_name');
        $data->pf_amount = $request->input('pf_amount');
        $data->esic_amount = $request->input('esic_amount');
        $data->save();
        Alert::success('Success!', 'Application submitted successfully');
        return back();
    }
    public function salarydetailupdate(Request $request)
    {
        $request->validate([
            'total_permanent_employee' => 'required',
            'month_upto_which_salary_paid' => 'required',
            'date_of_payement' => 'required|date',
            'total_amount_paid' => 'required|numeric',
            'employees_contribution' => 'required|numeric',
            'employer_contribution' => 'required|numeric',
            'voucher_no' => 'required|string',
        ]);

        $nikay = Auth::guard('web')->user();
        $salary = SalaryDetail::where('ulb_id', $nikay->id)->where('id', $request->id)->firstOrFail();

        $salary->total_permanent_employee = $request->input('total_permanent_employee');
        $salary->month_upto_which_salary_paid = $request->input('month_upto_which_salary_paid');
        $salary->date_of_payement = $request->input('date_of_payement');
        $salary->total_amount_paid = $request->input('total_amount_paid');
        $salary->employees_contribution = $request->input('employees_contribution');
        $salary->employer_contribution = $request->input('employer_contribution');
        $salary->voucher_no = $request->input('voucher_no');
        $salary->save();

        Alert::success('Updated!', 'Salary details updated successfully.');
        return back();
    }

    public function electricitybill()
    {

        $nikay = Auth::guard('web')->user();
        $item = ElectricityBill::where('ulb_id', $nikay->id)->get();
        $yearlyelectricity = ElectricityYearlyData::where('ulb_id', $nikay->id)->get();
        return view('ulbdashboard/electricitybill', compact('item', 'nikay', 'yearlyelectricity'));
    }



    public function electricitybillsubmit(Request $request)
    {

        $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $length = 5;
        $electricitybillno = '';
        do {
            $electricitybillno = '';
            for ($i = 0; $i < $length; $i++) {
                $electricitybillno .= $characters[rand(0, strlen($characters) - 1)];
            }
        } while (ElectricityBill::where('electricity_bill_no', $electricitybillno)->exists()); // Ensure uniqueness
        $request->validate([
            'current_month_bill_amount' => 'required|integer',
            'pending_electricity_bill' =>  'required|integer',
            'surcharge_amount' => 'required|integer',
            'total_amount' => 'required',
            'previous_paid_bill_amount' => 'required|integer',
            'previous_paid_bill_date' => 'required|date',
            'remark' => 'required|string|max:255',
        ]);

        $nikay = Auth::guard('web')->user();
        $data = new ElectricityBill();
        $data->ulb_id = $nikay->id;
        $data->electricity_bill_no = $electricitybillno;
        $data->current_month_bill_amount = $request->input('current_month_bill_amount');
        $data->pending_electricity_bill = $request->input('pending_electricity_bill');
        $data->surcharge_amount = $request->input('surcharge_amount');
        $data->total_amount = $request->pending_electricity_bill + $request->surcharge_amount + $request->current_month_bill_amount;
        // $data->pending_date_from = $request->input('pending_date_from');
        $data->previous_paid_bill_amount = $request->input('previous_paid_bill_amount');
        $data->previous_paid_bill_date = $request->input('previous_paid_bill_date');
        $data->remark = $request->input('remark');
        $data->save();
        Alert::success('Success!', 'Application submitted successfully');
        return back();
    }
    public function electricitybillupdate(Request $request)
    {
        $request->validate([

            'current_month_bill_amount' => 'required|integer',
            'pending_electricity_bill' => 'required|integer',
            'surcharge_amount' => 'required|integer',
            'total_amount' => 'required|numeric',
            'previous_paid_bill_amount' => 'required|integer',
            'previous_paid_bill_date' => 'required|date',
            'remark' => 'required|string|max:255',
        ]);

        $bill = ElectricityBill::where('electricity_bill_no', $request->input('electricity_bill_no'))->firstOrFail();

        $bill->current_month_bill_amount = $request->input('current_month_bill_amount');
        $bill->pending_electricity_bill = $request->input('pending_electricity_bill');
        $bill->surcharge_amount = $request->input('surcharge_amount');
        $bill->total_amount = $request->input('total_amount');
        $bill->previous_paid_bill_amount = $request->input('previous_paid_bill_amount');
        $bill->previous_paid_bill_date = $request->input('previous_paid_bill_date');
        $bill->remark = $request->input('remark');
        $bill->save();

        Alert::success('Updated!', 'Electricity bill updated successfully');
        return back();
    }

    public function electricityyearlysubmit(Request $request)
    {
        $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $length = 5;
        $Yearlyelectricityno = '';
        do {
            $Yearlyelectricityno = '';
            for ($i = 0; $i < $length; $i++) {
                $Yearlyelectricityno .= $characters[rand(0, strlen($characters) - 1)];
            }
        } while (ElectricityYearlyData::where('unique_no', $Yearlyelectricityno)->exists());
        $request->validate([
            'from_year' => 'required',
            'to_year' => 'required',
            'total_amount' => 'required|integer',
            'remark' => 'required',
        ]);
        $nikay = Auth::guard('web')->user();
        $data = new ElectricityYearlyData();
        $data->ulb_id = $nikay->id;
        $data->unique_no = $Yearlyelectricityno;
        $data->from_year = $request->input('from_year');
        $data->to_year = $request->input('to_year');
        $data->total_amount = $request->input('total_amount');
        $data->remark = $request->input('remark');
        $data->save();
        Alert::success('Success!', 'Application submitted successfully');
        return back();
    }
    public function employeereport(Request $request)
    {
        $ulbs = User::get();
        $employeetypes = EmployeeType::get();
        $qualifications = Qualification::get();
        $payscale = Payscale::get();
        $employeeposts = EmployeePost::all();

        $nikay = Auth::guard('web')->user();
        $employeesQuery = Employee::with('qualificationtype', 'post', 'employeeType')
            ->where('current_ulb', $nikay->id);

        if ($request->filled('employee_name')) {
            $employeesQuery->where('employee_name', 'like', '%' . $request->employee_name . '%');
        }
        if ($request->filled('employee_type')) {
            $employeesQuery->where('employee_type', $request->employee_type);
        }
        if ($request->filled('employee_post')) {
            $employeesQuery->where('post_name', $request->employee_post);
        }

        $employees = $employeesQuery->get();
        if ($employees->isNotEmpty()) {
            $employeetypes = $employeetypes->whereIn('id', $employees->pluck('employee_type')->unique());
            $employeeposts = $employeeposts->whereIn('id', $employees->pluck('post_name')->unique());
        }
        return view('ulbdashboard.employeereport', compact(
            'employees',
            'ulbs',
            'employeeposts',
            'employeetypes',
            'payscale'
        ));
    }

    public function newmeter()
    {
        $nikay = Auth::guard('web')->user();
        $item = NewMeterRegistration::where('ulb_id', $nikay->id)->get();
        return view('ulbdashboard/newmeter', compact('item', 'nikay'));
    }

    public function newmetersubmit(Request $request)
    {

        $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        $length = 5;
        $newmeter = '';
        do {
            $newmeter = '';
            for ($i = 0; $i < $length; $i++) {
                $newmeter .= $characters[rand(0, strlen($characters) - 1)];
            }
        } while (NewMeterRegistration::where('unique_meter_no', $newmeter)->exists());

        $request->validate([
            'meter_registrtaion_no' => 'required',
            'meter_authorized_name' => 'required',
            'registration_date' => 'required',

            'latitude' => 'required|numeric',  // Ensures latitude is a numeric value (decimal or integer)
            'longitude' => 'required|numeric', // Ensures longitude is a numeric value (decimal or integer)
            'image' => 'required|image|mimes:jpeg,jpg,png|max:200',
            'meter_status' => 'required',
            'remark' => 'required',
        ]);
        $imagePath = null;
        if ($request->hasFile('image')) {

            $imagePath = $request->file('image')->store('images', 'public');
        }
        $nikay = Auth::guard('web')->user();
        $data = new NewMeterRegistration();
        $data->ulb_id = $nikay->id;
        $data->unique_meter_no = $newmeter;
        $data->meter_registrtaion_no = $request->input('meter_registrtaion_no');
        $data->meter_authorized_name = $request->input('meter_authorized_name');
        $data->registration_date = $request->input('registration_date');
        $data->latitude = $request->input('latitude');
        $data->longitude = $request->input('longitude');
        $data->image = $imagePath;
        $data->meter_status = $request->input('meter_status');
        $data->remark = $request->input('remark');
        $data->save();
        Alert::success('Success!', 'Application submitted successfully');
        return back();
    }


    public function suspensiondetail()
    {
        $loggedInUlbId = Auth::guard('web')->user()->id;

        $employees = Employee::where('current_ulb', $loggedInUlbId)->get();

        $item = Employee::where('current_ulb', $loggedInUlbId)
            ->whereNotNull('suspension_date')
            ->where(function ($query) {
                $query->whereNull('bahal_order_no')
                    ->orWhere('bahal_order_no', ''); // Exclude empty strings as well
            })
            ->get();
        $ulbs = User::all();
        // $item = LeaveApply::where('ulb_id', $nikay->id)->get();

        return view('ulbdashboard.suspensiondetail', compact(
            'employees',
            'loggedInUlbId',
            'ulbs',
            'item',

        ));
    }

    public function suspensiondetailsubmit(Request $request)
    {
        // Get the logged-in ULB ID
        $loggedInUlbId = Auth::guard('web')->user()->id;

        // Validate the suspension form data
        $validatedData = $request->validate([
            'employee_name' => 'required|exists:employees,id',  // Ensure employee ID exists
            'suspension_date' => 'required|date',
            'suspension_order_no' => 'required|string|max:255',
            'suspension_letter' => 'required|mimes:pdf|max:512', // Max size for suspension letter
            'suspension_remark' => 'required|string|max:255',
        ]);

        // Fetch the employee by ID
        $employee = Employee::find($request->employee_name);

        // Handle the suspension details if provided
        if ($request->has('suspension_date')) {
            $employee->suspension_date = $request->input('suspension_date');
            $employee->suspension_order_no = $request->input('suspension_order_no');
            $employee->suspension_remark = $request->input('suspension_remark');

            // Handle file upload for suspension letter
            if ($request->hasFile('suspension_letter')) {
                $file = $request->file('suspension_letter');
                $filePath = $file->store('suspension_letters', 'public');  // Store the file
                $employee->suspension_letter = $filePath;  // Save file path in the database
            }
        }

        // Save the updated employee data
        $employee->save();

        // Flash success message and redirect
        Alert::success('Success!', 'Suspension details updated successfully.');
        return redirect()->route('employeereport')->with('success', 'Suspension details updated successfully!');
    }

    public function bahaldetailsubmit(Request $request)
    {
        $validatedData = $request->validate([

            'bahal_date' => 'required|date',
            'bahal_order_no' => 'required|string|max:255',
            'bahal_letter' => 'required|mimes:pdf|max:512', // Max size for suspension letter
            'bahal_remark' => 'required|string|max:255',
        ]);
        $employee = Employee::find($request->employeeId);

        if ($employee) {
            // Update other employee details
            $employee->bahal_date = $request->bahal_date;
            $employee->bahal_order_no = $request->bahal_order_no;
            $employee->bahal_remark = $request->bahal_remark;

            // Check if a file was uploaded
            if ($request->hasFile('bahal_letter')) {

                $file = $request->file('bahal_letter');
                $filePath = $file->store('bahal_letters', 'public');
                $employee->bahal_letter = $filePath;
            }

            // Save the updated employee data
            $employee->save();

            // Redirect back with success message
            Alert::success('Success!', 'बहाली details updated successfully.');
            return redirect()->back()->with('success', 'Employee data updated successfully!');
        } else {
            // Redirect back with error message if employee not found
            return redirect()->route('Suspension.Report')->with('error', 'Employee not found.');
        }
    }
    public function SuspensionReport()
    {
        $loggedInUlbId = Auth::guard('web')->user()->id;

        $employees = Employee::where('current_ulb', $loggedInUlbId)->get();
        $suspension = Employee::where('current_ulb', $loggedInUlbId)
            ->whereNotNull('suspension_date')
            ->whereNull('bahal_date')
            ->get();

        $item = Employee::where('current_ulb', $loggedInUlbId)
            ->whereNotNull('bahal_date')
            ->get();
        $ulbs = User::all();
        // $item = LeaveApply::where('ulb_id', $nikay->id)->get();

        return view('ulbdashboard.suspensionreport', compact(
            'employees',
            'loggedInUlbId',
            'ulbs',
            'item',
            'suspension',

        ));
    }


    // ULB ALLOTMENTS METHOD
    public function ulballotment()
    {
        $loggedInUser = Auth::guard('web')->user(); // or Auth::user() for default guard
        $ulbId = $loggedInUser->id;
        $data = ChungiMadhVyay::where('ulb_id', $ulbId)  // Filter by logged-in user's ulb_id
            ->orderBy('created_at', 'desc')->first();
        $appatnidhimarammat = AppatnidhiMarammat::where('ulb_id', $ulbId)
            ->orderBy('created_at', 'desc')->first();
        $mahapaurnidhi = MahapaurAdhyakshNidhi::where('ulb_id', $ulbId)
            ->orderBy('created_at', 'desc')->first();
        $parshadnidhi = ParshadNidhi::where('ulb_id', $ulbId)
            ->orderBy('created_at', 'desc')->first();
        $swachcommando = SwachCommando::where('ulb_id', $ulbId)
            ->orderBy('created_at', 'desc')->first();
        $cleancity = MissionCleanCity::where('ulb_id', $ulbId)
            ->orderBy('created_at', 'desc')->first();
        $lambitvetan = LambitVetan::where('ulb_id', $ulbId)
            ->orderBy('created_at', 'desc')->first();
        $anyakarya = AnyaKarya::where('ulb_id', $ulbId)
            ->orderBy('created_at', 'desc')->first();
        $peyjalnivaran = PeyjalkastNivaran::where('ulb_id', $ulbId)
            ->orderBy('created_at', 'desc')->first();
        $jalpradaygirh = JalPradayGirh::where('ulb_id', $ulbId)
            ->orderBy('created_at', 'desc')->first();
        $mudranksulak = MudrankSulak::where('ulb_id', $ulbId)
            ->orderBy('created_at', 'desc')->first();
        $bar = Bar::where('ulb_id', $ulbId)
            ->orderBy('created_at', 'desc')->first();
        $yatrikar = YatriKar::where('ulb_id', $ulbId)
            ->orderBy('created_at', 'desc')->first();
        $utpadkar = Utpadkar::where('ulb_id', $ulbId)
            ->orderBy('created_at', 'desc')->first();

        $nikay = Auth::guard('web')->user();
        $ulb_id = $nikay->id;

        // $totalAmountReleased = ChungiMadhVyay::where('ulb_id', $ulb_id)->sum('amount_released');
        $totalAmountReleased = ChungiMadhVyay::where('ulb_id', $ulb_id)
            ->whereNotNull('month')
            ->sum('amount_released');
        $totalAmountReleasedtwo = AppatnidhiMarammat::where('ulb_id', $ulb_id)->whereNotNull('month')
            ->sum('amount_released');
        $totalAmountReleasedthree = MahapaurAdhyakshNidhi::where('ulb_id', $ulb_id)->whereNotNull('month')
            ->sum('amount_released');
        $totalAmountReleasedfour = ParshadNidhi::where('ulb_id', $ulb_id)->whereNotNull('month')
            ->sum('amount_released');
        $totalAmountReleasedfive = SwachCommando::where('ulb_id', $ulb_id)->whereNotNull('month')
            ->sum('amount_released');
        $totalAmountReleasedsix = MissionCleanCity::where('ulb_id', $ulb_id)->whereNotNull('month')
            ->sum('amount_released');
        $totalAmountReleasedseven  = LambitVetan::where('ulb_id', $ulb_id)->whereNotNull('month')
            ->sum('amount_released');
        $totalAmountReleasedeight  = AnyaKarya::where('ulb_id', $ulb_id)->whereNotNull('month')
            ->sum('amount_released');
        $totalAmountReleasednine  = PeyjalkastNivaran::where('ulb_id', $ulb_id)->whereNotNull('month')
            ->sum('amount_released');
        $totalAmountReleasedten  = JalPradayGirh::where('ulb_id', $ulb_id)->whereNotNull('month')
            ->sum('amount_released');
        $totalAmountReleasedeleven  = MudrankSulak::where('ulb_id', $ulb_id)->whereNotNull('month')
            ->sum('amount_released');
        $totalAmountReleasedtwelve  = Bar::where('ulb_id', $ulb_id)->whereNotNull('month')
            ->sum('amount_released');
        $totalAmountReleasedthirteen  = YatriKar::where('ulb_id', $ulb_id)->whereNotNull('month')
            ->sum('amount_released');
        $totalAmountReleasedfourteen  = Utpadkar::where('ulb_id', $ulb_id)->whereNotNull('month')
            ->sum('amount_released');
        return view('ulbdashboard/ulballotment', compact('totalAmountReleased', 'totalAmountReleasedtwo', 'totalAmountReleasedthree', 'totalAmountReleasedfour', 'totalAmountReleasedfive', 'totalAmountReleasedsix', 'totalAmountReleasedseven', 'totalAmountReleasedeight', 'totalAmountReleasednine', 'totalAmountReleasedten', 'totalAmountReleasedeleven', 'totalAmountReleasedtwelve', 'totalAmountReleasedthirteen', 'totalAmountReleasedfourteen', 'data', 'appatnidhimarammat', 'mahapaurnidhi', 'parshadnidhi', 'swachcommando', 'cleancity', 'lambitvetan', 'anyakarya', 'peyjalnivaran', 'jalpradaygirh', 'mudranksulak', 'bar', 'yatrikar', 'utpadkar'));
    }

    public function chungimadhvyaysubmit(Request $request)
    {
        try {
            $loggedInId = Auth::guard('web')->user()->id;
            $chungi_no = mt_rand(1000, 9999);
            $validatedData = $request->validate([
                'amount_released' => 'nullable|numeric',
                'expenditure_amount' => 'nullable|numeric',
                'balance_amount' => 'required|numeric',
                'remark' => 'required|string',
            ]);
            $response = ChungiMadhVyay::create([
                'amount_released' => $validatedData['amount_released'],
                'expenditure_amount' => $validatedData['expenditure_amount'],
                'balance_amount' => $validatedData['balance_amount'],
                'remark' => $validatedData['remark'],
                'ulb_id' => $loggedInId,
                'chungi_no' => $chungi_no,
            ]);
            Alert::success('Success!', 'Data submitted successfully');

            return back();
        } catch (\Exception $e) {
            Alert::error('Error!', 'There was an issue while submitting the data. Please try again.');
            return back();
        }
    }


    public function appatnidhimarammat(Request $request)
    {
        try {
            $loggedInId = Auth::guard('web')->user()->id;
            $appatnidhi_unique_no = mt_rand(1000, 9999);
            $validatedData = $request->validate([
                'amount_released' => 'nullable|numeric',
                'expenditure_amount' => 'required|numeric',
                'balance_amount' => 'required|numeric',
                'remark' => 'required|string',
            ]);
            $response = AppatnidhiMarammat::create([
                'amount_released' => $validatedData['amount_released'],
                'expenditure_amount' => $validatedData['expenditure_amount'],
                'balance_amount' => $validatedData['balance_amount'],
                'remark' => $validatedData['remark'],
                'ulb_id' => $loggedInId,
                'appatnidhi_unique_no' => $appatnidhi_unique_no,
            ]);
            Alert::success('Success!', 'Data submitted successfully');

            return back();
        } catch (\Exception $e) {
            Alert::error('Error!', 'There was an issue while submitting the data. Please try again.');
            return back();
        }
    }


    public function mahapauradhyakshnidhis(Request $request)
    {
        try {
            $loggedInId = Auth::guard('web')->user()->id;
            $unique_no = mt_rand(1000, 9999);
            $validatedData = $request->validate([
                'amount_released' => 'nullable|numeric',
                'expenditure_amount' => 'required|numeric',
                'balance_amount' => 'required|numeric',
                'remark' => 'required|string',
            ]);
            $response = MahapaurAdhyakshNidhi::create([
                'amount_released' => $validatedData['amount_released'],
                'expenditure_amount' => $validatedData['expenditure_amount'],
                'balance_amount' => $validatedData['balance_amount'],
                'remark' => $validatedData['remark'],
                'ulb_id' => $loggedInId,
                'unique_no' => $unique_no,
            ]);
            Alert::success('Success!', 'Data submitted successfully');

            return back();
        } catch (\Exception $e) {
            Alert::error('Error!', 'There was an issue while submitting the data. Please try again.');
            return back();
        }
    }

    public function parshadnidhis(Request $request)
    {
        try {
            $loggedInId = Auth::guard('web')->user()->id;
            $unique_no = mt_rand(1000, 9999);
            $validatedData = $request->validate([
                'amount_released' => 'nullable|numeric',
                'expenditure_amount' => 'required|numeric',
                'balance_amount' => 'required|numeric',
                'remark' => 'required|string',
            ]);
            $response = ParshadNidhi::create([
                'amount_released' => $validatedData['amount_released'],
                'expenditure_amount' => $validatedData['expenditure_amount'],
                'balance_amount' => $validatedData['balance_amount'],
                'remark' => $validatedData['remark'],
                'ulb_id' => $loggedInId,
                'unique_no' => $unique_no,
            ]);
            Alert::success('Success!', 'Data submitted successfully');

            return back();
        } catch (\Exception $e) {
            Alert::error('Error!', 'There was an issue while submitting the data. Please try again.');
            return back();
        }
    }

    public function swachcommando(Request $request)
    {
        try {
            $loggedInId = Auth::guard('web')->user()->id;
            $unique_no = mt_rand(1000, 9999);
            $validatedData = $request->validate([
                'amount_released' => 'nullable|numeric',
                'expenditure_amount' => 'required|numeric',
                'balance_amount' => 'required|numeric',
                'remark' => 'required|string',
            ]);
            $response = SwachCommando::create([
                'amount_released' => $validatedData['amount_released'],
                'expenditure_amount' => $validatedData['expenditure_amount'],
                'balance_amount' => $validatedData['balance_amount'],
                'remark' => $validatedData['remark'],
                'ulb_id' => $loggedInId,
                'unique_no' => $unique_no,
            ]);
            Alert::success('Success!', 'Data submitted successfully');

            return back();
        } catch (\Exception $e) {
            Alert::error('Error!', 'There was an issue while submitting the data. Please try again.');
            return back();
        }
    }


    public function missioncleancities(Request $request)
    {
        try {
            $loggedInId = Auth::guard('web')->user()->id;
            $unique_no = mt_rand(1000, 9999);
            $validatedData = $request->validate([
                'amount_released' => 'nullable|numeric',
                'expenditure_amount' => 'required|numeric',
                'balance_amount' => 'required|numeric',
                'remark' => 'required|string',
            ]);
            $response = MissionCleanCity::create([
                'amount_released' => $validatedData['amount_released'],
                'expenditure_amount' => $validatedData['expenditure_amount'],
                'balance_amount' => $validatedData['balance_amount'],
                'remark' => $validatedData['remark'],
                'ulb_id' => $loggedInId,
                'unique_no' => $unique_no,
            ]);
            Alert::success('Success!', 'Data submitted successfully');

            return back();
        } catch (\Exception $e) {
            Alert::error('Error!', 'There was an issue while submitting the data. Please try again.');
            return back();
        }
    }
    // public function  lambitvetans(Request $request)
    // {
    //     $unique_no = mt_rand(1000, 9999);
    //     $rules = ['unique_no' => 'unique:lambit_vetans,unique_no'];
    //     $validator = Validator::make(['unique_no' => $unique_no], $rules);
    //     if ($validator->fails()) {
    //         return $this->submit($request);
    //     }
    //     $nikay = Auth::guard('web')->user();
    //     $data = new LambitVetan();
    //     $data->ulb_id = $nikay->id;
    //     $data->unique_no = $unique_no;
    //     $data->amount_released = $request->input('amount_released');
    //     $data->expenditure_amount = $request->input('expenditure_amount');
    //     $data->balance_amount = $request->input('balance_amount');
    //     $data->remark = $request->input('remark');
    //     $data->save();
    //     Alert::success('Success!', 'Application submitted successfully');
    //     return back();
    // }
    public function lambitvetans(Request $request)
    {
        try {
            $loggedInId = Auth::guard('web')->user()->id;
            $unique_no = mt_rand(1000, 9999);
            $validatedData = $request->validate([
                'amount_released' => 'nullable|numeric',
                'expenditure_amount' => 'required|numeric',
                'balance_amount' => 'required|numeric',
                'remark' => 'required|string',
            ]);
            $response = LambitVetan::create([
                'amount_released' => $validatedData['amount_released'],
                'expenditure_amount' => $validatedData['expenditure_amount'],
                'balance_amount' => $validatedData['balance_amount'],
                'remark' => $validatedData['remark'],
                'ulb_id' => $loggedInId,
                'unique_no' => $unique_no,
            ]);
            Alert::success('Success!', 'Data submitted successfully');

            return back();
        } catch (\Exception $e) {
            Alert::error('Error!', 'There was an issue while submitting the data. Please try again.');
            return back();
        }
    }

    public function anyakaryas(Request $request)
    {
        try {
            $loggedInId = Auth::guard('web')->user()->id;
            $unique_no = mt_rand(1000, 9999);
            $validatedData = $request->validate([
                'amount_released' => 'nullable|numeric',
                'expenditure_amount' => 'required|numeric',
                'balance_amount' => 'required|numeric',
                'remark' => 'required|string',
            ]);
            $response = AnyaKarya::create([
                'amount_released' => $validatedData['amount_released'],
                'expenditure_amount' => $validatedData['expenditure_amount'],
                'balance_amount' => $validatedData['balance_amount'],
                'remark' => $validatedData['remark'],
                'ulb_id' => $loggedInId,
                'unique_no' => $unique_no,
            ]);
            Alert::success('Success!', 'Data submitted successfully');

            return back();
        } catch (\Exception $e) {
            Alert::error('Error!', 'There was an issue while submitting the data. Please try again.');
            return back();
        }
    }

    public function peyjalkastnivarans(Request $request)
    {
        try {
            $loggedInId = Auth::guard('web')->user()->id;
            $unique_no = mt_rand(1000, 9999);
            $validatedData = $request->validate([
                'amount_released' => 'nullable|numeric',
                'expenditure_amount' => 'required|numeric',
                'balance_amount' => 'required|numeric',
                'remark' => 'required|string',
            ]);
            $response = PeyjalkastNivaran::create([
                'amount_released' => $validatedData['amount_released'],
                'expenditure_amount' => $validatedData['expenditure_amount'],
                'balance_amount' => $validatedData['balance_amount'],
                'remark' => $validatedData['remark'],
                'ulb_id' => $loggedInId,
                'unique_no' => $unique_no,
            ]);
            Alert::success('Success!', 'Data submitted successfully');

            return back();
        } catch (\Exception $e) {
            Alert::error('Error!', 'There was an issue while submitting the data. Please try again.');
            return back();
        }
    }

    public function jalpradaygirhs(Request $request)
    {
        try {
            $loggedInId = Auth::guard('web')->user()->id;
            $unique_no = mt_rand(1000, 9999);
            $validatedData = $request->validate([
                'amount_released' => 'nullable|numeric',
                'expenditure_amount' => 'required|numeric',
                'balance_amount' => 'required|numeric',
                'remark' => 'required|string',
            ]);
            $response = JalPradayGirh::create([
                'amount_released' => $validatedData['amount_released'],
                'expenditure_amount' => $validatedData['expenditure_amount'],
                'balance_amount' => $validatedData['balance_amount'],
                'remark' => $validatedData['remark'],
                'ulb_id' => $loggedInId,
                'unique_no' => $unique_no,
            ]);
            Alert::success('Success!', 'Data submitted successfully');

            return back();
        } catch (\Exception $e) {
            Alert::error('Error!', 'There was an issue while submitting the data. Please try again.');
            return back();
        }
    }


    public function mudranksulaks(Request $request)
    {
        try {
            $loggedInId = Auth::guard('web')->user()->id;
            $unique_no = mt_rand(1000, 9999);
            $validatedData = $request->validate([
                'amount_released' => 'nullable|numeric',
                'expenditure_amount' => 'required|numeric',
                'balance_amount' => 'required|numeric',
                'remark' => 'required|string',
            ]);
            $response = MudrankSulak::create([
                'amount_released' => $validatedData['amount_released'],
                'expenditure_amount' => $validatedData['expenditure_amount'],
                'balance_amount' => $validatedData['balance_amount'],
                'remark' => $validatedData['remark'],
                'ulb_id' => $loggedInId,
                'unique_no' => $unique_no,
            ]);
            Alert::success('Success!', 'Data submitted successfully');

            return back();
        } catch (\Exception $e) {
            Alert::error('Error!', 'There was an issue while submitting the data. Please try again.');
            return back();
        }
    }

    public function bars(Request $request)
    {
        try {
            $loggedInId = Auth::guard('web')->user()->id;
            $unique_no = mt_rand(1000, 9999);
            $validatedData = $request->validate([
                'amount_released' => 'nullable|numeric',
                'expenditure_amount' => 'required|numeric',
                'balance_amount' => 'required|numeric',
                'remark' => 'required|string',
            ]);
            $response = Bar::create([
                'amount_released' => $validatedData['amount_released'],
                'expenditure_amount' => $validatedData['expenditure_amount'],
                'balance_amount' => $validatedData['balance_amount'],
                'remark' => $validatedData['remark'],
                'ulb_id' => $loggedInId,
                'unique_no' => $unique_no,
            ]);
            Alert::success('Success!', 'Data submitted successfully');

            return back();
        } catch (\Exception $e) {
            Alert::error('Error!', 'There was an issue while submitting the data. Please try again.');
            return back();
        }
    }
    // public function  yatrikars(Request $request)
    // {
    //     $unique_no = mt_rand(1000, 9999);
    //     $rules = ['unique_no' => 'unique:yatri_kars,unique_no'];
    //     $validator = Validator::make(['unique_no' => $unique_no], $rules);
    //     if ($validator->fails()) {
    //         return $this->submit($request);
    //     }
    //     $nikay = Auth::guard('web')->user();
    //     $data = new YatriKar();
    //     $data->ulb_id = $nikay->id;
    //     $data->unique_no = $unique_no;
    //     $data->amount_released = $request->input('amount_released');
    //     $data->expenditure_amount = $request->input('expenditure_amount');
    //     $data->balance_amount = $request->input('balance_amount');
    //     $data->remark = $request->input('remark');
    //     $data->save();
    //     Alert::success('Success!', 'Application submitted successfully');
    //     return back();
    // }
    public function yatrikars(Request $request)
    {
        try {
            $loggedInId = Auth::guard('web')->user()->id;
            $unique_no = mt_rand(1000, 9999);
            $validatedData = $request->validate([
                'amount_released' => 'nullable|numeric',
                'expenditure_amount' => 'required|numeric',
                'balance_amount' => 'required|numeric',
                'remark' => 'required|string',
            ]);
            $response = YatriKar::create([
                'amount_released' => $validatedData['amount_released'],
                'expenditure_amount' => $validatedData['expenditure_amount'],
                'balance_amount' => $validatedData['balance_amount'],
                'remark' => $validatedData['remark'],
                'ulb_id' => $loggedInId,
                'unique_no' => $unique_no,
            ]);
            Alert::success('Success!', 'Data submitted successfully');

            return back();
        } catch (\Exception $e) {
            Alert::error('Error!', 'There was an issue while submitting the data. Please try again.');
            return back();
        }
    }

    public function utpadkars(Request $request)
    {
        try {
            $loggedInId = Auth::guard('web')->user()->id;
            $unique_no = mt_rand(1000, 9999);
            $validatedData = $request->validate([
                'amount_released' => 'nullable|numeric',
                'expenditure_amount' => 'required|numeric',
                'balance_amount' => 'required|numeric',
                'remark' => 'required|string',
            ]);
            $response = Utpadkar::create([
                'amount_released' => $validatedData['amount_released'],
                'expenditure_amount' => $validatedData['expenditure_amount'],
                'balance_amount' => $validatedData['balance_amount'],
                'remark' => $validatedData['remark'],
                'ulb_id' => $loggedInId,
                'unique_no' => $unique_no,
            ]);
            Alert::success('Success!', 'Data submitted successfully');

            return back();
        } catch (\Exception $e) {
            Alert::error('Error!', 'There was an issue while submitting the data. Please try again.');
            return back();
        }
    }

    // public function rajasvvasuliform()
    // {
    //     $loggedInId = Auth::guard('web')->user()->id;
    //     $taxTypes = TaxType::get();
    //     $submittedCollections = RevenueCollection::where('ulb_id', $loggedInId)
    //         ->pluck('month', 'taxtype_id')
    //         ->toArray();
    //     $taxdemands = RevenueDemand::with('ulb', 'taxtype')
    //         ->where('ulb_id', $loggedInId)
    //         ->selectRaw('taxtype_id, SUM(current_demand) as total_current_demand, SUM(pending_demand) as total_pending_demand')
    //         ->groupBy('taxtype_id')
    //         ->get();

    //     $taxdemandsGrouped = $taxdemands->groupBy('taxtype_id');
    //     $currentMonth = \Carbon\Carbon::now()->format('F');
    //     $previousMonths = collect(['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'])
    //         ->takeWhile(fn($month) => $month != $currentMonth)
    //         ->all();  // All months before the current month

    //     $nikay = Auth::guard('web')->user();
    //     $taxtypes = TaxType::all();
    //     return view('ulbdashboard.rajasvvasuliform', compact(
    //         'taxTypes',
    //         'taxdemandsGrouped',
    //         'submittedCollections',

    //         'previousMonths',
    //         // 'allMonths',
    //         // 'disableMonths',
    //         'nikay',
    //         'taxtypes'
    //     ));
    // }
    public function rajasvvasuliform()
    {
        $loggedInId = Auth::guard('web')->user()->id;
        $taxTypes = TaxType::get();
        $submittedCollections = RevenueCollection::where('ulb_id', $loggedInId)
            ->pluck('month', 'taxtype_id')
            ->toArray();
        // $taxdemands = RevenueDemand::with('ulb', 'taxtype')
        //     ->where('ulb_id', $loggedInId)
        //     ->selectRaw('taxtype_id, SUM(current_demand) as total_current_demand, SUM(pending_demand) as total_pending_demand')
        //     ->groupBy('taxtype_id')
        //     ->get();
        $taxdemands = RevenueDemand::with('ulb', 'taxtype')
            ->where('ulb_id', $loggedInId)
            ->latest('id')  // latest inserted first
            ->get()
            ->unique('taxtype_id');  // only latest per taxtype_id
        $taxdemandsGrouped = $taxdemands->groupBy('taxtype_id');
        $currentMonth = \Carbon\Carbon::now()->format('F');
        $currentMonthNumber = \Carbon\Carbon::now()->format('m');  // Example : 04
        $previousMonth = \Carbon\Carbon::now()->subMonthNoOverflow()->format('F');
        $allMonths = [
            'January',
            'February',
            'March',
            'April',
            'May',
            'June',
            'July',
            'August',
            'September',
            'October',
            'November',
            'December'
        ];
        // $submittedTaxTypeCurrentMonth = RevenueDemand::where('ulb_id', $loggedInId)
        //     ->whereMonth('date', $currentMonthNumber)
        //     ->pluck('taxtype_id')
        //     ->toArray();
        $currentYear = Carbon::now()->year;
        $financialYear = (Carbon::now()->month >= 4)
            ? $currentYear . '-' . ($currentYear + 1)
            : ($currentYear - 1) . '-' . $currentYear;

        // Get Already Submitted TaxTypes for Current Financial Year
        $submittedTaxTypeCurrentMonth = RevenueDemand::where('ulb_id', $loggedInId)
            ->where('financial_year', $financialYear)
            ->pluck('taxtype_id')
            ->toArray();
        $nikay = Auth::guard('web')->user();
        return view('ulbdashboard.rajasvvasuliform', compact(
            'taxTypes',
            'taxdemandsGrouped',
            'submittedCollections',
            'currentMonth',
            'previousMonth',
            'allMonths',
            'nikay',
            'submittedTaxTypeCurrentMonth'
        ));
    }


    public function revenuecollection(Request $request)
    {
        $unique_no = mt_rand(1000, 9999);
        $rules = ['unique_no' => 'unique:revenue_collections,unique_no'];
        $validator = Validator::make(['unique_no' => $unique_no], $rules);
        if ($validator->fails()) {
            return $this->submit($request);
        }
        $loggedInUser = Auth::guard('web')->user();
        $request->validate([
            'taxtype_id' => 'required|string|max:255',
            'month' => 'required',
            'current_demand' => 'required|numeric',
            'current_collection' => 'required|numeric',
            'pending_demand' => 'required|numeric',
            'pending_collection' => 'required|numeric',

        ]);
        $submitCollections = new RevenueCollection();
        $submitCollections->unique_no = $unique_no;
        $submitCollections->ulb_id = $loggedInUser->id;
        $submitCollections->taxtype_id = $request['taxtype_id'];
        $submitCollections->month = $request['month'];
        $submitCollections->current_demand = $request['current_demand'];
        $submitCollections->current_collection = $request['current_collection'];
        $submitCollections->pending_demand = $request['pending_demand'];
        $submitCollections->pending_collection = $request['pending_collection'];

        $submitCollections->save();
        Alert::success('Success!', 'Application submitted successfully');
        return redirect()->route('rajasvvasuliform');
    }
    // fetch last updated data 
    // public function revenuecollectionreport(Request $request)
    // {
    //     $loggedInUser = Auth::guard('web')->user();

    //     $revenueCollections = RevenueCollection::with('taxType')
    //         ->where('ulb_id', $loggedInUser->id)
    //         ->get();
    //     $taxTypes = TaxType::all();
    //     return view('ulbdashboard.revenuecollectionreport', compact('revenueCollections', 'taxTypes'));
    // }

    public function revenuecollectionreport(Request $request)
    {
        $loggedInUser = Auth::guard('web')->user();
        $ulbId = $loggedInUser->id;

        // Fetch all collections
        $revenueCollections = RevenueCollection::with('taxType')
            ->where('ulb_id', $ulbId)
            ->get();

        // Step 1: Fetch all demands for this ULB
        $allDemands = RevenueDemand::where('ulb_id', $ulbId)->get();

        // Step 2: Get the latest financial year demand per taxtype
        $latestDemands = $allDemands->groupBy('taxtype_id')->map(function ($group) {
            return $group->sortByDesc('financial_year')->first(); // Only latest per taxtype
        });

        // Step 3: Create lookup array by taxtype_id
        $demandByTaxType = $latestDemands;

        $taxTypes = TaxType::all();

        return view('ulbdashboard.revenuecollectionreport', compact(
            'revenueCollections',
            'taxTypes',
            'demandByTaxType'
        ));
    }



    public function ulbdetailreport(Request $request)
    {
        // Get the logged-in user (ULB)
        $loggedInUser = Auth::guard('web')->user();
        $lastMonth = \Carbon\Carbon::now()->subMonth()->format('Y-m');
        $taxTypes = TaxType::all();
        $item = RevenueCollection::query();
        $item->where('ulb_id', $loggedInUser->id);
        if ($request->has('search') && $request->search != '') {
            $searchItem = $request->search;
            $item->whereHas('ulb', function ($query) use ($searchItem) {
                $query->where('name', 'like', '%' . $searchItem . '%');
            });
        }

        if ($request->has('month') && $request->month != '') {
            $month = $request->month;
            $item->where('month', $month);
        }

        $item = $item->selectRaw('
        ulb_id,
        month,
        SUM(current_demand) as total_current_demand,
        SUM(current_collection) as total_current_collection,
        SUM(pending_demand) as total_pending_demand,
        SUM(pending_collection) as total_pending_collection ')
            ->groupBy('ulb_id', 'month')
            ->orderBy('month', 'asc')
            ->get();

        return view('ulbdashboard.ulbdetailreport', compact('item', 'loggedInUser', 'taxTypes'));
    }

    // public function revenuecollectionedit($id)
    // {
    //     $loggedInUser = Auth::guard('web')->user();
    //     $revenueCollection = RevenueCollection::with('taxType')
    //         ->where('id', $id)
    //         ->where('ulb_id', $loggedInUser->id)
    //         ->first();

    //     if (!$revenueCollection) {
    //         return redirect()->route('revenuecollectionreport')->with('error', 'Record not found');
    //     }

    //     $taxTypes = TaxType::all();
    //     return view('ulbdashboard.revenuecollectionedit', compact('revenueCollection', 'taxTypes'));
    // }
    // public function revenuecollectionupdate(Request $request, $id)
    // {
    //     $loggedInUser = Auth::guard('web')->user();
    //     $revenueCollection = RevenueCollection::where('id', $id)
    //         ->where('ulb_id', $loggedInUser->id)
    //         ->first();
    //     if (!$revenueCollection) {
    //         return redirect()->route('revenuecollectionreport')->with('error', 'Record not found');
    //     }
    //     // $taxType = TaxType::find($request->taxtype_id);
    //     // Update the record with new values
    //     // $revenueCollection->taxtype_id = $request->taxtype_id;
    //     $revenueCollection->current_demand = $request->current_demand;
    //     $revenueCollection->current_collection = $request->current_collection;
    //     $revenueCollection->pending_demand = $request->pending_demand;
    //     $revenueCollection->pending_collection = $request->pending_collection;
    //     $revenueCollection->total_demand = $request->current_demand + $request->pending_demand;
    //     $revenueCollection->save();
    //     Alert::success('Success!', 'Application submitted successfully');
    //     return redirect()->route('revenuecollection.report')->with('success', 'Revenue Collection Updated Successfully');
    // }

    public function revenueupdate(Request $request, $id)
    {
        $collection = RevenueCollection::findOrFail($id);
        $collection->update($request->only([
            'unique_no',
            'month',
            'tax_type_id',
            'current_demand',
            'current_collection',
            'pending_demand',
            'pending_collection',
        ]));
        Alert::success('Success!', 'Record updated successfully.');
        return redirect()->back();
    }


    // public function revenuedemanddetail()
    // {
    //     $taxTypes = TaxType::orderBy('id')->get();

    //     // ✅ Get all unique financial years from the revenue_demand table
    //     $financialYears = RevenueDemand::select('financial_year')
    //         ->distinct()
    //         ->orderBy('financial_year', 'desc')
    //         ->pluck('financial_year');

    //     // ✅ Fetch all tax demand records (this includes current_demand and pending_demand)
    //     $taxDemands = RevenueDemand::all();

    //     return view('ulbdashboard.revenuedemanddetail', compact('taxTypes', 'financialYears', 'taxDemands'));
    // }
    public function revenuedemanddetail()
    {
        $nikay = Auth::guard('web')->user(); // Logged-in ULB user

        $taxTypes = TaxType::orderBy('id')->get();

        // Get unique financial years for this ULB
        $financialYears = RevenueDemand::where('ulb_id', $nikay->id)
            ->select('financial_year')
            ->distinct()
            ->orderBy('financial_year', 'desc')
            ->pluck('financial_year');

        // Get all demand data for this ULB
        $taxDemands = RevenueDemand::where('ulb_id', $nikay->id)->get();

        return view('ulbdashboard.revenuedemanddetail', compact('taxTypes', 'financialYears', 'taxDemands'));
    }

    public function revenuedemandview($year)
    {
        $ulb = Auth::guard('web')->user();

        $demands = RevenueDemand::with('taxtype')
            ->where('ulb_id', $ulb->id)
            ->where('financial_year', $year)
            ->get();

        return view('ulbdashboard.revenuedemandview', compact('year', 'demands'));
    }


    public function revenuedemandupdate(Request $request, $id)
    {
        $request->validate([
            'current_demand' => 'required|numeric|min:0',
            'pending_demand' => 'required|numeric|min:0',
            'date' => 'required|date',
        ]);

        $demand = RevenueDemand::findOrFail($id);
        $demand->current_demand = $request->current_demand;
        $demand->pending_demand = $request->pending_demand;
        $demand->date = $request->date;
        $demand->save();
        Alert::success('Success!', 'Demand updated successfully.');
        return redirect()->back();
    }

    public function exportExcelTaxTypewise(Request $request) // overall pension report
    {
        $nikay = Auth::guard('web')->user();

        $ulbWiseCounts = RevenueCollection::where('ulb_id', $nikay->id)->get(); // Retrieve data
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set headings
        $sheet->setCellValue('A1', 'Sno.');
        $sheet->setCellValue('B1', 'Unique No');
        $sheet->setCellValue('C1', 'Month');
        $sheet->setCellValue('D1', 'Tax Type');
        $sheet->setCellValue('E1', 'चालू मांग');
        $sheet->setCellValue('F1', 'चालू वसूली');
        $sheet->setCellValue('G1', 'चालू वसूली का %');
        $sheet->setCellValue('H1', 'बकाया मांग');
        $sheet->setCellValue('I1', 'बकाया वसूली');
        $sheet->setCellValue('J1', 'बकाया वसूली का %');
        $sheet->setCellValue('K1', 'कुल मांग');
        $sheet->setCellValue('L1', 'कुल वसूली');
        $sheet->setCellValue('M1', 'कुल वसूली का %');

        $row = 2;  // Start from the second row
        foreach ($ulbWiseCounts as $index => $data) {
            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $data->unique_no);
            $sheet->setCellValue('C' . $row, $data->month);
            $sheet->setCellValue('D' . $row, $data->taxType->tax_type);
            $sheet->setCellValue('E' . $row, $data->current_demand);
            $sheet->setCellValue('F' . $row, $data->current_collection);
            $currentPercentage = $data->current_demand != 0 ? ($data->current_collection / $data->current_demand) * 100 : 0;
            $sheet->setCellValue('G' . $row, number_format($currentPercentage, 2));
            $sheet->setCellValue('H' . $row, $data->pending_demand);
            $sheet->setCellValue('I' . $row, $data->pending_collection);
            $pendingPercentage = $data->pending_demand != 0 ? ($data->pending_collection / $data->pending_demand) * 100 : 0;
            $sheet->setCellValue('J' . $row, number_format($pendingPercentage, 2));
            $sheet->setCellValue('K' . $row, $data->current_demand + $data->pending_demand);
            $sheet->setCellValue('L' . $row, $data->current_collection + $data->pending_collection);
            $totalPercentage = ($data->current_demand + $data->pending_demand) != 0
                ? (($data->current_collection + $data->pending_collection) /
                    ($data->current_demand + $data->pending_demand)) * 100
                : 0;
            $sheet->setCellValue('M' . $row, number_format($totalPercentage, 2));
            $row++;
        }
        $writer = new Xlsx($spreadsheet);
        $filename = 'Overall_revenue_collection_tax_typewise.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');
        $writer->save('php://output');
        exit;
    }


    public function exportExcelExcelOverall()
    {
        // Fetch the logged-in ULB (Nikay)
        $nikay = Auth::guard('web')->user();

        // Fetch the required data with selectRaw, groupBy, and orderBy correctly applied
        $item = RevenueCollection::where('ulb_id', $nikay->id)
            ->selectRaw('
            ulb_id,
            month,
            SUM(current_demand) as total_current_demand,
            SUM(current_collection) as total_current_collection,
            SUM(pending_demand) as total_pending_demand,
            SUM(pending_collection) as total_pending_collection ')
            ->groupBy('ulb_id', 'month')
            ->orderBy('month', 'asc')
            ->get(); // Now you can call get() after applying selectRaw

        // Initialize the spreadsheet
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set column headings
        $sheet->setCellValue('A1', 'Sno.');
        $sheet->setCellValue('B1', 'Month');
        $sheet->setCellValue('C1', 'Total Current Demand');
        $sheet->setCellValue('D1', 'Total Current Collection');
        $sheet->setCellValue('E1', 'Total Current Collection (%)');
        $sheet->setCellValue('F1', 'Total Pending Demand');
        $sheet->setCellValue('G1', 'Total Pending Collection');
        $sheet->setCellValue('H1', 'Total Pending Collection (%)');
        $sheet->setCellValue('I1', 'Total Demand');
        $sheet->setCellValue('J1', 'Total Collection');
        $sheet->setCellValue('K1', 'Total Collection (%)');

        $row = 2; // Start from the second row

        // Populate the spreadsheet with the data
        foreach ($item as $index => $data) {
            $sheet->setCellValue('A' . $row, $index + 1); // Serial number
            $sheet->setCellValue('B' . $row, $data->month);
            $sheet->setCellValue('C' . $row, $data->total_current_demand);
            $sheet->setCellValue('D' . $row, $data->total_current_collection);

            // Calculate the percentages for current collection
            $currentCollectionPercentage = $data->total_current_demand != 0
                ? ($data->total_current_collection / $data->total_current_demand) * 100
                : 0;
            $sheet->setCellValue('E' . $row, number_format($currentCollectionPercentage, 2));

            $sheet->setCellValue('F' . $row, $data->total_pending_demand);
            $sheet->setCellValue('G' . $row, $data->total_pending_collection);

            // Calculate the percentages for pending collection
            $pendingCollectionPercentage = $data->total_pending_demand != 0
                ? ($data->total_pending_collection / $data->total_pending_demand) * 100
                : 0;
            $sheet->setCellValue('H' . $row, number_format($pendingCollectionPercentage, 2));

            // Calculate total demand and collection
            $totalDemand = $data->total_current_demand + $data->total_pending_demand;
            $totalCollection = $data->total_current_collection + $data->total_pending_collection;

            $sheet->setCellValue('I' . $row, $totalDemand);
            $sheet->setCellValue('J' . $row, $totalCollection);

            // Calculate total collection percentage
            $totalCollectionPercentage = $totalDemand != 0
                ? ($totalCollection / $totalDemand) * 100
                : 0;
            $sheet->setCellValue('K' . $row, number_format($totalCollectionPercentage, 2));

            $row++; // Move to the next row
        }

        // Write the file
        $writer = new Xlsx($spreadsheet);
        $filename = 'Ulb_detail_report_' . Carbon::now()->format('Y-m-d') . '.xlsx'; // Dynamic filename

        // Set headers for the file download
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        // Save the file to output (browser download)
        $writer->save('php://output');
        exit; // Terminate to ensure the file is sent correctly
    }


    public function exportExcelEmployee(Request $request) // overall pension report
    {
        $nikay = Auth::guard('web')->user();
        $ulbWiseCounts = Employee::where('current_ulb', $nikay->id)->get();
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        // Set headings
        $sheet->setCellValue('A1', 'Sno.');
        $sheet->setCellValue('B1', 'Division name');
        $sheet->setCellValue('C1', 'वर्तमान निकाय का नाम');
        $sheet->setCellValue('D1', 'Previous Ulb name');
        $sheet->setCellValue('E1', 'Registration no');
        $sheet->setCellValue('F1', 'अधिकारी/कर्मचारी का नाम');
        $sheet->setCellValue('G1', 'पिता का नाम');
        $sheet->setCellValue('H1', 'जन्मतिथि');
        $sheet->setCellValue('I1', 'पदनाम');
        $sheet->setCellValue('J1', 'वेतनमान');
        $sheet->setCellValue('K1', 'Gender');
        $sheet->setCellValue('L1', 'शैक्षणिक योग्यता');
        $sheet->setCellValue('M1', 'श्रेणी');
        $sheet->setCellValue('N1', 'कर्मचारी/अधिकारी की सेवा स्वरूप');
        $sheet->setCellValue('O1', 'निकाय सेवा में प्रथम प्रविष्टि का दिनांक');
        $sheet->setCellValue('P1', 'मूल निकाय का नाम');

        $sheet->setCellValue('Q1', 'मूल विभाग');
        $sheet->setCellValue('R1', 'कार्यरत अधिकारी/कर्मचारी का वर्तमान मूल पदनाम');
        $sheet->setCellValue('S1', 'वर्तमान निकाय में कब से कार्यरत हैं');
        $sheet->setCellValue('T1', 'रिफ्रेन्स आईडी(आधार कार्ड नंबर)');
        $sheet->setCellValue('U1', 'मोबाइल नंबर');
        $sheet->setCellValue('V1', 'ईमेल आईडी');
        $sheet->setCellValue('W1', 'अस्थाई पता');
        $sheet->setCellValue('X1', 'स्थाई पता');
        $sheet->setCellValue('Y1', 'कुल शेष CL');
        $sheet->setCellValue('Z1', 'कुल शेष OL');
        $sheet->setCellValue('AA1', 'कुल शेष EL');
        $sheet->setCellValue('AB1', 'कुल शेष ML');
        $sheet->setCellValue('AC1', 'निलंबन तिथि');
        $sheet->setCellValue('AD1', 'निलंबन आदेश क्र');
        $sheet->setCellValue('AE1', 'Remark (निलंबन)');
        $sheet->setCellValue('AF1', 'बहाली तिथि');
        $sheet->setCellValue('AG1', 'बहाली आदेश क्र');
        $sheet->setCellValue('AH1', 'Remark (बहाली)');
        $sheet->setCellValue('AI1', 'e-HRMS मेल आईडी ');
        $sheet->setCellValue('AJ1', 'रिमार्क');

        $row = 2;  // Start from the second row
        foreach ($ulbWiseCounts as $index => $data) {
            $sheet->setCellValue('A' . $row, $index + 1);  // Sno.
            $sheet->setCellValue('B' . $row, $data->ulb->division->name);  // Division Name
            $sheet->setCellValue('C' . $row, $data->ulb->name);  // Current Ulb Name
            $sheet->setCellValue('D' . $row, $data->ulb->name);  // Previous Ulb Name
            $sheet->setCellValue('E' . $row, $data->registration_no);  // Registration No
            $sheet->setCellValue('F' . $row, $data->employee_name);  // Employee Name
            $sheet->setCellValue('G' . $row, $data->father_name);  // Post Name
            $sheet->setCellValue('H' . $row, $data->birth_date);  // Current Designation
            $sheet->setCellValue('I' . $row, $data->post->post_name);  // Payscale
            $sheet->setCellValue('J' . $row, $data->payscalename->payscale);  // Employee Type
            $sheet->setCellValue('K' . $row, $data->gender);  // Birth Date
            $sheet->setCellValue('L' . $row, $data->qualification);  // Joining Date
            $sheet->setCellValue('M' . $row, $data->caste);  // Qualification
            $sheet->setCellValue('N' . $row, $data->employeeType->type);  // Category (or your desired field name)
            $sheet->setCellValue('O' . $row, $data->joining_date);  // Service Status (or your desired field name)
            $sheet->setCellValue('P' . $row, $data->ulbnew->name);  // Entry Date (or your desired field name)
            $sheet->setCellValue('Q' . $row, $data->department_name);  // Original Ulb Name
            $sheet->setCellValue('R' . $row, $data->current_designation);  // Department Name
            $sheet->setCellValue('S' . $row, $data->work_start_date);  // Current Position
            $sheet->setCellValue('T' . $row, $data->reference_id);  // Working Since (or your desired field name)
            $sheet->setCellValue('U' . $row, $data->mobile_number);  // Reference ID
            $sheet->setCellValue('V' . $row, $data->email);  // Mobile Number
            $sheet->setCellValue('W' . $row, $data->temporary_address);  // Email ID
            $sheet->setCellValue('X' . $row, $data->permanent_address);  // Temporary Address
            $sheet->setCellValue('Y' . $row, $data->employee_cl);  // Permanent Address
            $sheet->setCellValue('Z' . $row, $data->employee_ol);  // CL Balance (or your desired field name)
            $sheet->setCellValue('AA' . $row, $data->employee_el);  // OL Balance (or your desired field name)
            $sheet->setCellValue('AB' . $row, $data->employee_ml);  // EL Balance (or your desired field name)
            $sheet->setCellValue('AB' . $row, $data->suspension_date);  // EL Balance (or your desired field name)
            $sheet->setCellValue('AD' . $row, $data->suspension_order_no);  // Suspension Date
            $sheet->setCellValue('AE' . $row, $data->suspension_remark);  // Suspension Order
            $sheet->setCellValue('AF' . $row, $data->bahal_date);  // Suspension Remark
            $sheet->setCellValue('AG' . $row, $data->bahal_order_no);  // Reinstate Date
            $sheet->setCellValue('AH' . $row, $data->bahal_remark);  // Reinstate Order
            $sheet->setCellValue('AI' . $row, $data->e_hrms_email);  // Reinstate Remark
            $sheet->setCellValue('AJ' . $row, $data->remarks);  // Remark

            $row++;
        }
        $writer = new Xlsx($spreadsheet);
        $filename = 'Employee_report.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }

    public function exportexcelNewMeter()
    {

        $nikay = Auth::guard('web')->user();
        $ulbWiseCounts = NewMeterRegistration::where('ulb_id', $nikay->id)->get();
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->setCellValue('A1', 'Sno.');
        $sheet->setCellValue('B1', 'Unique meter no');
        $sheet->setCellValue('C1', 'Division name');
        $sheet->setCellValue('D1', 'Ulb name');
        $sheet->setCellValue('E1', 'मीटर रजिस्ट्रेशन नंबर(बीपी नंबर)');
        $sheet->setCellValue('F1', 'मीटर किस नाम से रजिस्टर है');
        $sheet->setCellValue('G1', 'मीटर किस दिनांक से रजिस्टर है');
        $sheet->setCellValue('H1', 'मीटर लगे स्थान का Lattitude');
        $sheet->setCellValue('I1', 'मीटर लगे स्थान का Longitude');
        $sheet->setCellValue('J1', 'मीटर की स्थिति	');
        $sheet->setCellValue('K1', 'Remark');



        $row = 2;
        foreach ($ulbWiseCounts as $index => $data) {
            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $data->unique_meter_no);
            $sheet->setCellValue('C' . $row, $data->ulb->division->name);
            $sheet->setCellValue('D' . $row, $data->ulb->name);
            $sheet->setCellValue('E' . $row, $data->meter_registrtaion_no);
            $sheet->setCellValue('F' . $row, $data->meter_authorized_name);
            $sheet->setCellValue('G' . $row, $data->registration_date);
            $sheet->setCellValue('H' . $row, $data->latitude);
            $sheet->setCellValue('I' . $row, $data->longitude);
            $sheet->setCellValue('J' . $row, $data->meter_status);
            $sheet->setCellValue('K' . $row, $data->remark);


            $row++;
        }

        $writer = new Xlsx($spreadsheet);
        $filename = 'Newmeter_record.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }
    public function excelElectricityBill()
    {
        $nikay = Auth::guard('web')->user();
        $ulbWiseCounts = ElectricityBill::where('ulb_id', $nikay->id)->get();
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->setCellValue('A1', 'Sno.');
        $sheet->setCellValue('B1', 'Electricity bill no');
        $sheet->setCellValue('C1', 'Division name');
        $sheet->setCellValue('D1', 'Ulb name');
        $sheet->setCellValue('E1', 'Current Month Bill Amount');
        $sheet->setCellValue('F1', 'Previous Pending Bill Amount');
        $sheet->setCellValue('G1', 'Surcharge Amount');
        $sheet->setCellValue('H1', 'Total Amount');
        $sheet->setCellValue('I1', 'Previous Paid Bill Amount');
        $sheet->setCellValue('J1', 'Previous Paid Bill Date');
        $sheet->setCellValue('K1', 'Remark');


        $row = 2;
        foreach ($ulbWiseCounts as $index => $data) {
            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $data->electricity_bill_no);
            $sheet->setCellValue('C' . $row, $data->ulb->division->name);
            $sheet->setCellValue('D' . $row, $data->ulb->name);
            $sheet->setCellValue('E' . $row, $data->current_month_bill_amount);
            $sheet->setCellValue('F' . $row, $data->pending_electricity_bill);
            $sheet->setCellValue('G' . $row, $data->surcharge_amount);
            $sheet->setCellValue('H' . $row, $data->total_amount);
            $sheet->setCellValue('I' . $row, $data->previous_paid_bill_amount);
            $sheet->setCellValue('J' . $row, $data->previous_paid_bill_date);
            $sheet->setCellValue('K' . $row, $data->remark);

            $row++;
        }

        $writer = new Xlsx($spreadsheet);
        $filename = 'electricity_bill.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }

    public function exportexcelLeaveDetail()
    {
        $nikay = Auth::guard('web')->user();
        $ulbWiseCounts = LeaveApply::where('ulb_id', $nikay->id)->get();

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Column Headers (Properly Arranged)
        $headers = [
            'A1' => 'Sno.',
            'B1' => 'Leave Application no.',
            'C1' => 'Division name',
            'D1' => 'निकाय का नाम',
            'E1' => 'अधिकारी का प्रकार',
            'F1' => 'छुट्टी मे जाने वाले अधिकारी का नाम',
            'G1' => 'छुट्टी apply की तारीख',
            'H1' => 'छुट्टी का प्रकार',
            'I1' => 'अवकाश अवधि किस तिथि से',
            'J1' => 'अवकाश अवधि किस तिथि तक',
            'K1' => 'स्वीकृतकर्ता का नाम',
            'L1' => 'स्वीकृतकर्ता का पदनाम',
            'M1' => 'चार्ज लेने वाले अधिकारी / कर्मचारी का नाम',
            'N1' => 'चार्ज लेने वाले अधिकारी / कर्मचारी पदनाम',
            'O1' => 'status'
        ];

        foreach ($headers as $cell => $value) {
            $sheet->setCellValue($cell, $value);
        }

        // Populating Data Rows
        $row = 2;
        foreach ($ulbWiseCounts as $index => $data) {
            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $data->leave_number ?? '');
            $sheet->setCellValue('C' . $row, optional($data->ulb->division)->name ?? '');
            $sheet->setCellValue('D' . $row, optional($data->ulb)->name ?? '');
            $sheet->setCellValue('E' . $row, optional($data->post)->post_name ?? '');
            $sheet->setCellValue('F' . $row, $data->name_of_officer ?? '');
            $sheet->setCellValue('G' . $row, Carbon::parse($data->date_inwhich_leave_applied)->format('d-m-Y') ?? '');
            $sheet->setCellValue('H' . $row, $data->leave_type ?? '');
            $sheet->setCellValue('I' . $row, Carbon::parse($data->leave_date_from)->format('d-m-Y') ?? '');
            $sheet->setCellValue('J' . $row, Carbon::parse($data->leave_date_to)->format('d-m-Y') ?? '');
            $sheet->setCellValue('K' . $row, $data->name_of_officer_approvedleave ?? '');
            $sheet->setCellValue('L' . $row, $data->post_of_officer_approvedleave ?? '');
            $sheet->setCellValue('M' . $row, $data->charged_officer_name ?? '');
            $sheet->setCellValue('N' . $row, $data->charged_officer_post ?? '');
            $sheet->setCellValue('O' . $row, $data->status ?? '');
            $row++;
        }

        // File Output
        $writer = new Xlsx($spreadsheet);
        $filename = 'Leave_report.xlsx';

        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }

    public function exportexcelRegularSalary()
    {
        $nikay = Auth::guard('web')->user();
        $ulbWiseCounts = SalaryDetail::where('ulb_id', $nikay->id)->get();
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->setCellValue('A1', 'Sno.');
        $sheet->setCellValue('B1', 'Salary unique no');
        $sheet->setCellValue('C1', 'Division name');
        $sheet->setCellValue('D1', 'निकाय का नाम');
        $sheet->setCellValue('E1', 'कुल नियमित कर्मचारी की संख्या');
        $sheet->setCellValue('F1', 'किस माह तक का वेतन भुगतान किया गया');
        $sheet->setCellValue('G1', 'किस दिनांक को भुगतान किया गया');
        $sheet->setCellValue('H1', 'कुल वेतन भुगतान राशि');
        $sheet->setCellValue('I1', 'कर्मचारी का अंशदान');
        $sheet->setCellValue('J1', 'नियोक्ता का अंशदान');
        $sheet->setCellValue('K1', 'Voucher No');
        $row = 2;
        foreach ($ulbWiseCounts as $index => $data) {
            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $data->salary_number_regular);
            $sheet->setCellValue('C' . $row, $data->ulb->division->name);
            $sheet->setCellValue('D' . $row, $data->ulb->name);
            $sheet->setCellValue('E' . $row, $data->total_permanent_employee);
            $sheet->setCellValue('F' . $row, $data->month_upto_which_salary_paid);
            $sheet->setCellValue('G' . $row, $data->date_of_payement);
            $sheet->setCellValue('H' . $row, $data->total_amount_paid);
            $sheet->setCellValue('I' . $row, $data->employees_contribution);
            $sheet->setCellValue('J' . $row, $data->employer_contribution);
            $sheet->setCellValue('K' . $row, $data->voucher_no);
            $row++;
        }

        $writer = new Xlsx($spreadsheet);
        $filename = 'RegularSalary_Report.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }
    public function exportexcelPlacementSalary()
    {
        $nikay = Auth::guard('web')->user();
        $ulbWiseCounts = SalaryDetailPlacement::where('ulb_id', $nikay->id)->get();
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->setCellValue('A1', 'Sno.');
        $sheet->setCellValue('B1', 'Salary unique no');
        $sheet->setCellValue('C1', 'Division name');
        $sheet->setCellValue('D1', 'निकाय का नाम');
        $sheet->setCellValue('E1', 'कुल प्लेसमेंट कर्मचारी की संख्या');
        $sheet->setCellValue('F1', 'किस माह तक का वेतन भुगतान किया गया');
        $sheet->setCellValue('G1', 'किस दिनांक को भुगतान किया गया');
        $sheet->setCellValue('H1', 'कुल वेतन भुगतान राशि');
        $sheet->setCellValue('I1', 'Placement Agency Name');
        $sheet->setCellValue('J1', 'P.F Amount');
        $sheet->setCellValue('K1', 'ESIC Amount');

        $row = 2;
        foreach ($ulbWiseCounts as $index => $data) {
            $sheet->setCellValue('A' . $row, $index + 1);
            $sheet->setCellValue('B' . $row, $data->salary_number_placement);
            $sheet->setCellValue('C' . $row, $data->ulb->division->name);
            $sheet->setCellValue('D' . $row, $data->ulb->name);
            $sheet->setCellValue('E' . $row, $data->total_placement_employee);
            $sheet->setCellValue('F' . $row, $data->month_upto_which_salary_paid_placement);
            $sheet->setCellValue('G' . $row, $data->date_of_payement_placement);
            $sheet->setCellValue('H' . $row, $data->total_amount_paid_placement);
            $sheet->setCellValue('I' . $row, $data->placement_agency_name);
            $sheet->setCellValue('J' . $row, $data->pf_amount);
            $sheet->setCellValue('K' . $row, $data->esic_amount);

            $row++;
        }

        $writer = new Xlsx($spreadsheet);
        $filename = 'PlacementSalary_Report.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');

        $writer->save('php://output');
        exit;
    }


    public function otherinformation()
    {
        return view('ulbdashboard/otherinformation');
    }

    public function updatereports()
    {
        return view('ulbdashboard/updatereports');
    }


    public function basicdetails()
    {
        $nikay = Auth::guard('web')->user();
        $basicDetail = UlbBasicDetail::where('ulb_id', $nikay->id)->first();
        return view('ulbdashboard/basicdetails', compact('nikay', 'basicDetail'));
    }

    public function basicdetailssubmit(Request $request)
    {
        $nikay = Auth::guard('web')->user();

        // Check if basic details already exist for this ULB
        $existingDetails = UlbBasicDetail::where('ulb_id', $nikay->id)->first();
        if ($existingDetails) {
            Alert::info('Information!', 'ULB Basic Details have already been submitted and cannot be modified.');
            return redirect('updatereports');
        }

        $validatedData = $request->validate([
            'year_of_establishment' => 'nullable|string|max:4',
            'area' => 'nullable|numeric',
            'population' => 'nullable|integer',
            'number_of_wards' => 'nullable|integer',
            'official_website' => 'nullable|string|max:255',
            'contact_details' => 'nullable|string|max:15',
        ]);

        $basicDetail = UlbBasicDetail::create([
            'ulb_id' => $nikay->id,
            'year_of_establishment' => $validatedData['year_of_establishment'],
            'area' => $validatedData['area'],
            'population' => $validatedData['population'],
            'number_of_wards' => $validatedData['number_of_wards'],
            'official_website' => $validatedData['official_website'],
            'contact_details' => $validatedData['contact_details'],
        ]);

        Alert::success('Success!', 'ULB Basic Details submitted successfully.');
        return redirect('updatereports');
    }

    public function basicdetailsupdate(Request $request)
    {
        $nikay = Auth::guard('web')->user();

        // Check if basic details exist for this ULB
        $existingDetails = UlbBasicDetail::where('ulb_id', $nikay->id)->first();
        if (!$existingDetails) {
            Alert::error('Error!', 'No basic details found to update.');
            return redirect('updatereports');
        }

        // Check attempts limit
        if ($existingDetails->attempts >= 2) {
            Alert::warning('Update Limit Reached!', 'You have already updated this form twice, which is the maximum number of updates allowed.');
            return redirect('updatereports');
        }

        $validatedData = $request->validate([
            'year_of_establishment' => 'nullable|string|max:4',
            'area' => 'nullable|numeric',
            'population' => 'nullable|integer',
            'number_of_wards' => 'nullable|integer',
            'official_website' => 'nullable|string|max:255',
            'contact_details' => 'nullable|string|max:15',
        ]);

        $existingDetails->update([
            'year_of_establishment' => $validatedData['year_of_establishment'],
            'area' => $validatedData['area'],
            'population' => $validatedData['population'],
            'number_of_wards' => $validatedData['number_of_wards'],
            'official_website' => $validatedData['official_website'],
            'contact_details' => $validatedData['contact_details'],
            'attempts' => $existingDetails->attempts + 1,
        ]);

        Alert::success('Success!', 'ULB Basic Details updated successfully. You have ' . (2 - $existingDetails->attempts) . ' updates remaining.');
        return redirect('updatereports');
    }

    public function administrativedetail()
    {
        $nikay = Auth::guard('web')->user();

        // Check if basic details exist (require basic details before administrative details)
        $basicDetail = UlbBasicDetail::where('ulb_id', $nikay->id)->first();

        $administrativeDetail = UlbAdministrativeDetail::where('ulb_id', $nikay->id)->first();

        // Determine the form mode based on request parameters
        $mode = 'create'; // Default mode is create

        if ($administrativeDetail) {
            // If details exist and update is requested
            if (request()->has('update')) {
                $mode = 'update';
            }
            // If just viewing details
            else {
                $mode = 'view';
            }
        }

        // Check if this is an update request but no record exists
        if (request()->has('update') && !$administrativeDetail) {
            Alert::warning('Warning!', 'No Administrative Details found to update. Please submit the details first.');
            return redirect('updatereports');
        }

        return view('ulbdashboard/administrativedetail', compact('nikay', 'administrativeDetail', 'mode'));
    }

    public function administrativedetailsubmit(Request $request)
    {
        $nikay = Auth::guard('web')->user();

        // Validation
        $validated = $request->validate([
            'chairperson_name' => 'nullable|string|max:255',
            'commissioner_name' => 'nullable|string|max:255',
            'elected_representatives_count' => 'nullable|integer',
            'st_representatives_count' => 'nullable|integer',
            'sc_representatives_count' => 'nullable|integer',
            'women_representatives_count' => 'nullable|integer',
            'total_staff_strength' => 'nullable|integer',
        ]);

        // Check if record already exists for this ULB
        $exists = UlbAdministrativeDetail::where('ulb_id', $nikay->id)->exists();

        if ($exists) {
            Alert::warning('Details Already Submitted', 'Administrative details have already been submitted. Please use the update option to modify your information.');
            return redirect('updatereports');
        }

        // Create new record
        try {
            $administrativeDetail = new UlbAdministrativeDetail();
            $administrativeDetail->ulb_id = $nikay->id;
            $administrativeDetail->chairperson_name = $request->chairperson_name;
            $administrativeDetail->commissioner_name = $request->commissioner_name;
            $administrativeDetail->elected_representatives_count = $request->elected_representatives_count;
            $administrativeDetail->st_representatives_count = $request->st_representatives_count;
            $administrativeDetail->sc_representatives_count = $request->sc_representatives_count;
            $administrativeDetail->women_representatives_count = $request->women_representatives_count;
            $administrativeDetail->total_staff_strength = $request->total_staff_strength;
            $administrativeDetail->save();

            Alert::success('Success', 'Administrative Details submitted successfully!');
            return redirect('updatereports');
        } catch (\Exception $e) {
            Alert::error('Error', 'Failed to submit Administrative Details. Please try again. ' . $e->getMessage());
            return back()->withInput();
        }
    }

    public function administrativedetailupdate(Request $request)
    {
        $nikay = Auth::guard('web')->user();

        // Validation
        $validated = $request->validate([
            'chairperson_name' => 'nullable|string|max:255',
            'commissioner_name' => 'nullable|string|max:255',
            'elected_representatives_count' => 'nullable|integer',
            'st_representatives_count' => 'nullable|integer',
            'sc_representatives_count' => 'nullable|integer',
            'women_representatives_count' => 'nullable|integer',
            'total_staff_strength' => 'nullable|integer',
        ]);

        // Find existing record
        $administrativeDetail = UlbAdministrativeDetail::where('ulb_id', $nikay->id)->first();

        if (!$administrativeDetail) {
            Alert::warning('Record Not Found', 'No Administrative Details found to update. Please submit the details first.');
            return redirect('administrativedetail');
        }

        // Check attempts limit
        if ($administrativeDetail->attempts >= 2) {
            Alert::warning('Update Limit Reached!', 'You have already updated this form twice, which is the maximum number of updates allowed.');
            return redirect('updatereports');
        }

        // Update record
        try {
            $administrativeDetail->chairperson_name = $request->chairperson_name;
            $administrativeDetail->commissioner_name = $request->commissioner_name;
            $administrativeDetail->elected_representatives_count = $request->elected_representatives_count;
            $administrativeDetail->st_representatives_count = $request->st_representatives_count;
            $administrativeDetail->sc_representatives_count = $request->sc_representatives_count;
            $administrativeDetail->women_representatives_count = $request->women_representatives_count;
            $administrativeDetail->total_staff_strength = $request->total_staff_strength;
            $administrativeDetail->attempts = $administrativeDetail->attempts + 1;
            $administrativeDetail->save();

            Alert::success('Success', 'Administrative Details updated successfully! You have ' . (2 - $administrativeDetail->attempts) . ' updates remaining.');
            return redirect('administrativedetail');
        } catch (\Exception $e) {
            Alert::error('Error', 'Failed to update Administrative Details. Please try again. ' . $e->getMessage());
            return back()->withInput();
        }
    }

    public function jaljanitbimari()
    {
        $nikay = Auth::guard('web')->user();
        $item = Jaljanitbimari::where('ulb_id', $nikay->id)->get();
        return view('ulbdashboard/jaljanitbimari', compact('nikay', 'item'));
    }


    public function jaljanitbimarisubmit(Request $request)
    {
        $validatedData = $request->validate([
            'dateInput' => 'required|date',
            'kultankiya' => 'nullable|numeric',
            'cleantanki' => 'nullable|numeric',
            'notcleantanki' => 'nullable|numeric',
            'totalsamvel' => 'nullable|numeric',
            'cleansamvel' => 'nullable|numeric',
            'notcleansamvel' => 'nullable|numeric',
            'totalpump' => 'nullable|numeric',
            'pumpnation' => 'nullable|numeric',
            'notpumpnation' => 'nullable|numeric',
            'totalcheck' => 'nullable|numeric',
            'payjalupukt' => 'nullable|numeric',
            'notpayjalupukt' => 'nullable|numeric',
            'totalpipelinelikage' => 'nullable|numeric',
            'pipelinelikage' => 'nullable|numeric',
        ]);

        $nikay = auth()->user();

        do {
            $uniqueId = random_int(10000, 99999);
        } while (Jaljanitbimari::where('unique_id', $uniqueId)->exists());

        $currentYear = now()->year;
        $financialYear = (now()->month >= 4)
            ? $currentYear . '-' . ($currentYear + 1)
            : ($currentYear - 1) . '-' . $currentYear;

        $data = new Jaljanitbimari();
        $data->ulb_id = $nikay->id;
        $data->dateInput = $validatedData['dateInput'];
        $data->unique_id = $uniqueId;
        $data->kultankiya = $validatedData['kultankiya'] ?? 0;
        $data->cleantanki = $validatedData['cleantanki'] ?? 0;
        $data->notcleantanki = $validatedData['notcleantanki'] ?? 0;
        $data->totalsamvel = $validatedData['totalsamvel'] ?? 0;
        $data->cleansamvel = $validatedData['cleansamvel'] ?? 0;
        $data->notcleansamvel = $validatedData['notcleansamvel'] ?? 0;
        $data->totalpump = $validatedData['totalpump'] ?? 0;
        $data->pumpnation = $validatedData['pumpnation'] ?? 0;
        $data->notpumpnation = $validatedData['notpumpnation'] ?? 0;
        $data->totalcheck = $validatedData['totalcheck'] ?? 0;
        $data->payjalupukt = $validatedData['payjalupukt'] ?? 0;
        $data->notpayjalupukt = $validatedData['notpayjalupukt'] ?? 0;
        $data->totalpipelinelikage = $validatedData['totalpipelinelikage'] ?? 0;
        $data->pipelinelikage = $validatedData['pipelinelikage'] ?? 0;
        $data->save();

        Alert::success('Success!', 'जल जनित बीमारियों के रोकथाम information submitted successfully.');
        return back();
    }


    public function muktidham()
    {
        $nikay = Auth::guard('web')->user();
        $item = Muktidham::where('ulb_id', $nikay->id)->get();
        return view('ulbdashboard/muktidham', compact('item', 'nikay'));
    }

    public function muktidhamsubmit(Request $request)
    {
        $validatedData = $request->validate([
            'dateInput' => 'required|date|max:255',
            'ward_no' => 'required|numeric',
            'ward_name' => 'required|string|max:255',
            'muktidham_address' => 'required|string|max:255',
            'longitude' => 'required|numeric',
            'latitude' => 'required|numeric',
        ]);

        $nikay = Auth::guard('web')->user();
        do {
            $uniqueId = random_int(10000, 99999);
        } while (Muktidham::where('unique_id', $uniqueId)->exists());
        $currentYear = Carbon::now()->year;
        $financialYear = (Carbon::now()->month >= 4)
            ? $currentYear . '-' . ($currentYear + 1)
            : ($currentYear - 1) . '-' . $currentYear;

        // Save data
        $data = new Muktidham();
        $data->ulb_id = $nikay->id;
        $data->unique_id = $uniqueId;
        $data->financial_year = $financialYear;
        $data->dateInput = $validatedData['dateInput'];
        $data->ward_no = $validatedData['ward_no'];
        $data->ward_name = $validatedData['ward_name'];
        $data->muktidham_address = $validatedData['muktidham_address'];
        $data->longitude = $validatedData['longitude'];
        $data->latitude = $validatedData['latitude'];

        $data->save();

        Alert::success('Success!', 'Muktidham information submitted successfully.');
        return back();
    }

    public function muktidhamupdate(Request $request)
    {
        $data = Muktidham::findOrFail($request->id);
        $data->update([
            // 'dateInput' => $request->dateInput,
            'ward_no' => $request->ward_no,
            'ward_name' => $request->ward_name,
            'muktidham_address' => $request->muktidham_address,
            'longitude' => $request->longitude,
            'latitude' => $request->latitude,
        ]);
        Alert::success('Success!', 'Updated successfully.');
        return redirect()->back();
    }


    public function talabinformation()
    {
        $nikay = Auth::guard('web')->user();
        $item = TalabInformation::where('ulb_id', $nikay->id)->get();
        return view('ulbdashboard/talabinformation', compact('item', 'nikay'));
    }

    public function talabinformationsubmit(Request $request)
    {
        $validatedData = $request->validate([
            'dateInput' => 'required|date|max:255',
            'ward_no' => 'required|numeric',
            'ward_name' => 'required|string|max:255',
            'talab_address' => 'required|string|max:255',
            'longitude' => 'required|numeric',
            'latitude' => 'required|numeric',
            'area' => 'required|string',
        ]);

        $nikay = Auth::guard('web')->user();
        do {
            $uniqueId = random_int(10000, 99999);
        } while (TalabInformation::where('unique_id', $uniqueId)->exists());
        $currentYear = Carbon::now()->year;
        $financialYear = (Carbon::now()->month >= 4)
            ? $currentYear . '-' . ($currentYear + 1)
            : ($currentYear - 1) . '-' . $currentYear;

        // Save data
        $data = new TalabInformation();
        $data->ulb_id = $nikay->id;
        $data->unique_id = $uniqueId;
        $data->financial_year = $financialYear;
        $data->dateInput = $validatedData['dateInput'];
        $data->ward_no = $validatedData['ward_no'];
        $data->ward_name = $validatedData['ward_name'];
        $data->talab_address = $validatedData['talab_address'];
        $data->longitude = $validatedData['longitude'];
        $data->latitude = $validatedData['latitude'];
        $data->area = $validatedData['area'];

        $data->save();

        Alert::success('Success!', 'Talab information submitted successfully.');
        return back();
    }

    public function talabinfoupdate(Request $request)
    {
        $data = TalabInformation::findOrFail($request->id);
        $data->update([
            'ward_no' => $request->ward_no,
            'ward_name' => $request->ward_name,
            'talab_address' => $request->talab_address,
            'longitude' => $request->longitude,
            'latitude' => $request->latitude,
            'area' => $request->area,
        ]);

        Alert::success('Success!', 'Updated successfully.');
        return redirect()->back();
    }

    public function gardeninformation()
    {
        $nikay = Auth::guard('web')->user();
        $item = GardenInfo::where('ulb_id', $nikay->id)->get();
        return view('ulbdashboard/gardeninformation', compact('item', 'nikay'));
    }
    public function gardeninformationsubmit(Request $request)
    {
        $validatedData = $request->validate([
            'dateInput' => 'required|date|max:255',
            'ward_no' => 'required|numeric',
            'ward_name' => 'required|string|max:255',
            'garden_address' => 'required|string|max:255',
            'longitude' => 'required|numeric',
            'latitude' => 'required|numeric',
            'area' => 'required|string',
        ]);

        $nikay = Auth::guard('web')->user();
        do {
            $uniqueId = random_int(10000, 99999);
        } while (GardenInfo::where('unique_id', $uniqueId)->exists());
        $currentYear = Carbon::now()->year;
        $financialYear = (Carbon::now()->month >= 4)
            ? $currentYear . '-' . ($currentYear + 1)
            : ($currentYear - 1) . '-' . $currentYear;

        // Save data
        $data = new GardenInfo();
        $data->ulb_id = $nikay->id;
        $data->unique_id = $uniqueId;
        $data->financial_year = $financialYear;
        $data->dateInput = $validatedData['dateInput'];
        $data->ward_no = $validatedData['ward_no'];
        $data->ward_name = $validatedData['ward_name'];
        $data->garden_address = $validatedData['garden_address'];
        $data->longitude = $validatedData['longitude'];
        $data->latitude = $validatedData['latitude'];
        $data->area = $validatedData['area'];

        $data->save();

        Alert::success('Success!', 'Garden information submitted successfully.');
        return back();
    }
    public function gardeninfoupdate(Request $request)
    {
        $data = GardenInfo::findOrFail($request->id);
        $data->update([

            'ward_no' => $request->ward_no,
            'ward_name' => $request->ward_name,
            'garden_address' => $request->garden_address,
            'area' => $request->area,
            'longitude' => $request->longitude,
            'latitude' => $request->latitude,
        ]);

        Alert::success('Success!', 'Garden data updated successfully.');
        return redirect()->back();
    }


    public function complexinformation()
    {
        $nikay = Auth::guard('web')->user();
        $item = Complexinformation::where('ulb_id', $nikay->id)->get();
        return view('ulbdashboard/complexinformation', compact('item', 'nikay'));
    }
    public function complexinformationsubmit(Request $request)
    {
        $validatedData = $request->validate([

            'dateInput' => 'required|date|max:255',
            'ward_no' => 'required|numeric',
            'ward_name' => 'required|string|max:255',
            'complex_address' => 'required|string|max:255',
            'complex_conditon' => 'required|string|max:255',
            'longitude' => 'required|numeric',
            'latitude' => 'required|numeric',
            'area' => 'required|string',
        ]);

        $nikay = Auth::guard('web')->user();
        do {
            $uniqueId = random_int(10000, 99999);
        } while (Complexinformation::where('unique_id', $uniqueId)->exists());
        $currentYear = Carbon::now()->year;
        $financialYear = (Carbon::now()->month >= 4)
            ? $currentYear . '-' . ($currentYear + 1)
            : ($currentYear - 1) . '-' . $currentYear;

        // Save data
        $data = new Complexinformation();
        $data->ulb_id = $nikay->id;
        $data->unique_id = $uniqueId;
        $data->financial_year = $financialYear;
        $data->dateInput = $validatedData['dateInput'];
        $data->ward_no = $validatedData['ward_no'];
        $data->ward_name = $validatedData['ward_name'];
        $data->complex_conditon = $validatedData['complex_conditon'];
        $data->complex_address = $validatedData['complex_address'];
        $data->longitude = $validatedData['longitude'];
        $data->latitude = $validatedData['latitude'];
        $data->area = $validatedData['area'];

        $data->save();

        Alert::success('Success!', 'Complex information submitted successfully.');
        return back();
    }
    public function complexinfoupdate(Request $request)
    {
        $data = Complexinformation::findOrFail($request->id);
        $data->update([
            'ward_no' => $request->ward_no,
            'ward_name' => $request->ward_name,
            'complex_address' => $request->complex_address,
            'complex_conditon' => $request->complex_conditon,
            'longitude' => $request->longitude,
            'latitude' => $request->latitude,
            'area' => $request->area,
        ]);

        Alert::success('Success!', 'Complex data updated successfully.');
        return redirect()->back();
    }

    public function streetlightinformation()
    {
        $nikay = Auth::guard('web')->user();
        $item = StreetlightInfo::where('ulb_id', $nikay->id)->get();
        return view('ulbdashboard/streetlightinformation', compact('item', 'nikay'));
    }

    public function streetlightinformationsubmit(Request $request)
    {
        $validatedData = $request->validate([

            'dateInput' => 'required|date|max:255',
            'total_pole' => 'required|numeric',
            'total_light' => 'required|numeric',
            'total_working_light' => 'required|numeric',
            'total_not_working_light' => 'required|numeric',
            'pole_without_light' => 'required|numeric',

        ]);

        $nikay = Auth::guard('web')->user();
        do {
            $uniqueId = random_int(10000, 99999);
        } while (StreetlightInfo::where('unique_id', $uniqueId)->exists());
        $currentYear = Carbon::now()->year;
        $financialYear = (Carbon::now()->month >= 4)
            ? $currentYear . '-' . ($currentYear + 1)
            : ($currentYear - 1) . '-' . $currentYear;

        // Save data
        $data = new StreetlightInfo();
        $data->ulb_id = $nikay->id;
        $data->unique_id = $uniqueId;
        $data->financial_year = $financialYear;
        $data->total_pole = $validatedData['total_pole'];
        $data->dateInput = $validatedData['dateInput'];
        $data->total_light = $validatedData['total_light'];
        $data->total_working_light = $validatedData['total_working_light'];
        $data->total_not_working_light = $validatedData['total_not_working_light'];
        $data->pole_without_light = $validatedData['pole_without_light'];

        $data->save();

        Alert::success('Success!', 'Complex information submitted successfully.');
        return back();
    }
    public function streetlightinfoupdate(Request $request)
    {
        $data = StreetlightInfo::findOrFail($request->id);

        $data->update([
            'total_pole' => $request->total_pole,
            'pole_without_light' => $request->pole_without_light,
            'total_light' => $request->total_light,
            'total_working_light' => $request->total_working_light,
            'total_not_working_light' => $request->total_not_working_light,
        ]);

        return redirect()->back()->with('success', 'Record Updated Successfully');
    }

    public function playgroundinformation()
    {
        $nikay = Auth::guard('web')->user();
        $item = PlaygroundInfo::where('ulb_id', $nikay->id)->get();
        return view('ulbdashboard/playgroundinformation', compact('item', 'nikay'));
    }
    public function playgroundinformationsubmit(Request $request)
    {
        $validatedData = $request->validate([
            'dateInput' => 'required|date|max:255',
            'ward_no' => 'required|numeric',
            'ward_name' => 'required|string|max:255',
            'playground_address' => 'required|string|max:255',
            'longitude' => 'required|numeric',
            'latitude' => 'required|numeric',
            'area' => 'required|string',
        ]);

        $nikay = Auth::guard('web')->user();
        do {
            $uniqueId = random_int(10000, 99999);
        } while (PlaygroundInfo::where('unique_id', $uniqueId)->exists());
        $currentYear = Carbon::now()->year;
        $financialYear = (Carbon::now()->month >= 4)
            ? $currentYear . '-' . ($currentYear + 1)
            : ($currentYear - 1) . '-' . $currentYear;

        // Save data
        $data = new PlaygroundInfo();
        $data->ulb_id = $nikay->id;
        $data->unique_id = $uniqueId;
        $data->financial_year = $financialYear;
        $data->dateInput = $validatedData['dateInput'];
        $data->ward_no = $validatedData['ward_no'];
        $data->ward_name = $validatedData['ward_name'];
        $data->playground_address = $validatedData['playground_address'];
        $data->longitude = $validatedData['longitude'];
        $data->latitude = $validatedData['latitude'];
        $data->area = $validatedData['area'];

        $data->save();

        Alert::success('Success!', 'Garden information submitted successfully.');
        return back();
    }
    public function playgroundinfoupdate(Request $request)
    {
        $data = PlaygroundInfo::findOrFail($request->id);
        $data->update([
            'ward_no' => $request->ward_no,
            'ward_name' => $request->ward_name,
            'playground_address' => $request->playground_address,
            'area' => $request->area,
            'longitude' => $request->longitude,
            'latitude' => $request->latitude,
        ]);

        Alert::success('Success!', 'Playground data updated successfully.');
        return redirect()->back();
    }

    public function handpumpinformation()
    {
        $nikay = Auth::guard('web')->user();
        $item = HandpumInfo::where('ulb_id', $nikay->id)->get();
        return view('ulbdashboard/handpumpinformation', compact('item', 'nikay'));
    }
    public function handpumpinformationsubmit(Request $request)
    {
        $validatedData = $request->validate([
            'dateInput' => 'required|date|max:255',
            'total_handpump' => 'required|numeric',
            'handpump_working' => 'required|numeric',
            'handpump_not_working' => 'required|numeric',
            'total_borewell' => 'required|numeric',
            'borewell_working' => 'required|numeric',
            'borewell_not_working' => 'required|numeric',

        ]);

        $nikay = Auth::guard('web')->user();
        do {
            $uniqueId = random_int(10000, 99999);
        } while (HandpumInfo::where('unique_id', $uniqueId)->exists());
        $currentYear = Carbon::now()->year;
        $financialYear = (Carbon::now()->month >= 4)
            ? $currentYear . '-' . ($currentYear + 1)
            : ($currentYear - 1) . '-' . $currentYear;

        // Save data
        $data = new HandpumInfo();
        $data->ulb_id = $nikay->id;
        $data->unique_id = $uniqueId;
        $data->financial_year = $financialYear;
        $data->dateInput = $validatedData['dateInput'];
        $data->total_handpump = $validatedData['total_handpump'];
        $data->handpump_working = $validatedData['handpump_working'];
        $data->handpump_not_working = $validatedData['handpump_not_working'];
        $data->total_borewell = $validatedData['total_borewell'];
        $data->borewell_working = $validatedData['borewell_working'];
        $data->borewell_not_working = $validatedData['borewell_not_working'];

        $data->save();

        Alert::success('Success!', 'Information submitted successfully.');
        return back();
    }
    public function handpumpinfoupdate(Request $request)
    {
        $data = HandpumInfo::findOrFail($request->id);

        $data->update([
            'total_handpump' => $request->total_handpump,
            'handpump_working' => $request->handpump_working,
            'handpump_not_working' => $request->handpump_not_working,
            'total_borewell' => $request->total_borewell,
            'borewell_working' => $request->borewell_working,
            'borewell_not_working' => $request->borewell_not_working,
        ]);

        Alert::success('Success!', 'Handpump Information Updated Successfully.');

        return redirect()->back();
    }


    public function panitankiinformation()
    {
        $nikay = Auth::guard('web')->user();
        $item = PanitankiInfo::where('ulb_id', $nikay->id)->get();
        return view('ulbdashboard/panitankiinformation', compact('item', 'nikay'));
    }
    public function panitankiinformationsubmit(Request $request)
    {
        $validatedData = $request->validate([
            'dateInput' => 'required|date|max:255',
            'ward_no' => 'required|numeric',
            'ward_name' => 'required|string|max:255',
            'panitaki_address' => 'required|string|max:255',
            'panitaki_capacity' => 'required|string|max:255',
            'longitude' => 'required|numeric',
            'latitude' => 'required|numeric',
        ]);

        $nikay = Auth::guard('web')->user();
        do {
            $uniqueId = random_int(10000, 99999);
        } while (PanitankiInfo::where('unique_id', $uniqueId)->exists());
        $currentYear = Carbon::now()->year;
        $financialYear = (Carbon::now()->month >= 4)
            ? $currentYear . '-' . ($currentYear + 1)
            : ($currentYear - 1) . '-' . $currentYear;

        // Save data
        $data = new PanitankiInfo();
        $data->ulb_id = $nikay->id;
        $data->unique_id = $uniqueId;
        $data->financial_year = $financialYear;
        $data->dateInput = $validatedData['dateInput'];
        $data->ward_no = $validatedData['ward_no'];
        $data->ward_name = $validatedData['ward_name'];
        $data->panitaki_address = $validatedData['panitaki_address'];
        $data->panitaki_capacity = $validatedData['panitaki_capacity'];
        $data->longitude = $validatedData['longitude'];
        $data->latitude = $validatedData['latitude'];

        $data->save();

        Alert::success('Success!', 'Information submitted successfully.');
        return back();
    }
    public function panitankiinfoupdate(Request $request)
    {
        $data = PanitankiInfo::findOrFail($request->id);
        $data->update([
            'ward_no' => $request->ward_no,
            'ward_name' => $request->ward_name,
            'panitaki_address' => $request->panitaki_address,
            'panitaki_capacity' => $request->panitaki_capacity,
            'longitude' => $request->longitude,
            'latitude' => $request->latitude,
        ]);

        Alert::success('Success!', 'Information updated successfully.');
        return redirect()->back();
    }

    public function payjalinformation()
    {
        $nikay = Auth::guard('web')->user();
        $item = PeyjalInfo::where('ulb_id', $nikay->id)->get();
        return view('ulbdashboard/payjalinformation', compact('item', 'nikay'));
    }
    public function payjalinformationsubmit(Request $request)
    {
        $validatedData = $request->validate([
            'dateInput' => 'required|date|max:255',
            'yojna_name' => 'required|string|max:255',
            'phe_handover_date' => 'nullable|date|required_without:not_handover_reason',
            'not_handover_reason' => 'nullable|string|required_without:phe_handover_date',
            'wtp_number' => 'required|numeric|max:255',
            'wtp_location' => 'required|string|max:255',
            'wtp_capcity' => 'required|numeric',
        ]);

        $nikay = Auth::guard('web')->user();
        do {
            $uniqueId = random_int(10000, 99999);
        } while (PeyjalInfo::where('unique_id', $uniqueId)->exists());
        $currentYear = Carbon::now()->year;
        $financialYear = (Carbon::now()->month >= 4)
            ? $currentYear . '-' . ($currentYear + 1)
            : ($currentYear - 1) . '-' . $currentYear;

        // Save data
        $data = new PeyjalInfo();
        $data->ulb_id = $nikay->id;
        $data->unique_id = $uniqueId;
        $data->financial_year = $financialYear;
        $data->dateInput = $validatedData['dateInput'];
        $data->yojna_name = $validatedData['yojna_name'];
        $data->phe_handover_date = $validatedData['phe_handover_date'];
        $data->not_handover_reason = $validatedData['not_handover_reason'];
        $data->wtp_number = $validatedData['wtp_number'];
        $data->wtp_location = $validatedData['wtp_location'];
        $data->wtp_capcity = $validatedData['wtp_capcity'];

        $data->save();

        Alert::success('Success!', 'Information submitted successfully.');
        return back();
    }
    public function payjalininfoupdate(Request $request)
    {
        $data = PeyjalInfo::findOrFail($request->id);
        $data->update([
            'dateInput' => $request->dateInput,
            'yojna_name' => $request->yojna_name,
            'phe_handover_date' => $request->phe_handover_date,
            'not_handover_reason' => $request->not_handover_reason,
            'wtp_number' => $request->wtp_number,
            'wtp_location' => $request->wtp_location,
            'wtp_capcity' => $request->wtp_capcity,
        ]);
        Alert::success('Success!', 'Information updated successfully.');
        return redirect()->back();
    }

    public function trafficlight()
    {
        $nikay = Auth::guard('web')->user();
        $item = TrafficLight::where('ulb_id', $nikay->id)->get();

        return view('ulbdashboard.trafficlights', compact('nikay', 'item'));
    }
    public function trafficlightsubmit(Request $request)
    {
        $validatedData = $request->validate([
            // 'unique_id' => 'required|unique:traffic_lights,unique_id',
            // 'dateInput' => 'required|date',
            // 'financial_year' => 'required|string|max:255',
            'ward_no' => 'required|integer',
            'ward_name' => 'required|string|max:255',
            'traffic_light_id' => 'required|string|max:255',
            'type' => 'required|string|max:255',
            'height' => 'required|numeric',
            'longitude' => 'required|numeric',
            'latitude' => 'required|numeric',
        ]);

        $nikay = Auth::guard('web')->user();
        do {
            $uniqueId = random_int(10000, 99999);
        } while (TrafficLight::where('unique_id', $uniqueId)->exists());
        $currentYear = Carbon::now()->year;
        $financialYear = (Carbon::now()->month >= 4)
            ? $currentYear . '-' . ($currentYear + 1)
            : ($currentYear - 1) . '-' . $currentYear;


        TrafficLight::create([
            'ulb_id' => $nikay->id,
            'unique_id' => $uniqueId,
            'dateInput' => $request->dateInput,
            'financial_year' => $financialYear,
            'ward_no' => $request->ward_no,
            'ward_name' => $request->ward_name,
            'traffic_light_id' => $request->traffic_light_id,
            'type' => $request->type,
            'height' => $request->height,
            'longitude' => $request->longitude,
            'latitude' => $request->latitude,
        ]);

        Alert::success('Success!', 'Traffic Light details submitted successfully.');
        return redirect()->back();
    }

    public function trafficlightupdate(Request $request)
    {
        $trafficLight = TrafficLight::findOrFail($request->id);
        $trafficLight->update([
            // 'dateInput' => $request->dateInput,
            // 'financial_year' => $request->financial_year,
            'ward_no' => $request->ward_no,
            'ward_name' => $request->ward_name,
            'traffic_light_id' => $request->traffic_light_id,
            'type' => $request->type,
            'height' => $request->height,
            'longitude' => $request->longitude,
            'latitude' => $request->latitude,
        ]);
        Alert::success('Success!', 'Traffic Light details updated successfully.');
        return redirect()->back();
    }

    public function govtbuildinginfomation()
    {
        $nikay = Auth::guard('web')->user();
        $item = GovtBuilding::where('ulb_id', $nikay->id)->get();
        return view('ulbdashboard/govtbuildinginfomation', compact('nikay', 'item'));
    }
    public function govtbuildingsubmit(Request $request)
    {
        $validatedData = $request->validate([
            'ward_no' => 'required|integer',
            'ward_name' => 'required|string|max:255',
            'type_of_building' => 'required|string|max:255',
            'name_of_building' => 'required|string|max:255',
            'year_of_establisment' => 'required|numeric',
            'longitude' => 'required|numeric',
            'latitude' => 'required|numeric',
            'floor_area' => 'required|numeric',
            'no_of_floors' => 'required|numeric|max:255',
            'parking_availability' => 'required|string',
            'parking_capacity' => 'required_if:parking_availability,yes|nullable|numeric',
            'elevator_availability' => 'required|string',
            'accessibility_features' => 'required|string',
            'power_backup' => 'required|string',
            'drinking_water' => 'required|string',
            'washroom' => 'required|string',

        ]);

        $nikay = Auth::guard('web')->user();
        do {
            $uniqueId = random_int(10000, 99999);
        } while (GovtBuilding::where('unique_id', $uniqueId)->exists());
        $currentYear = Carbon::now()->year;
        $financialYear = (Carbon::now()->month >= 4)
            ? $currentYear . '-' . ($currentYear + 1)
            : ($currentYear - 1) . '-' . $currentYear;


        GovtBuilding::create([
            'ulb_id' => $nikay->id,
            'unique_id' => $uniqueId,
            'dateInput' => $request->dateInput,
            'financial_year' => $financialYear,
            'ward_no' => $request->ward_no,
            'ward_name' => $request->ward_name,
            'type_of_building' => $request->type_of_building,
            'name_of_building' => $request->name_of_building,
            'year_of_establisment' => $request->year_of_establisment,
            'longitude' => $request->longitude,
            'latitude' => $request->latitude,
            'floor_area' => $request->floor_area,
            'no_of_floors' => $request->no_of_floors,
            'parking_availability' => $request->parking_availability,
            'parking_capacity' => $request->parking_capacity,
            'elevator_availability' => $request->elevator_availability,
            'accessibility_features' => $request->accessibility_features,
            'power_backup' => $request->power_backup,
            'drinking_water' => $request->drinking_water,
            'washroom' => $request->washroom,
        ]);

        Alert::success('Success!', 'Building  details submitted successfully.');
        return redirect()->back();
    }

    public function rainwaterharvestinginfo()
    {
        $nikay = Auth::guard('web')->user();
        $item = RainWaterHarvesting::where('ulb_id', $nikay->id)->get();
        $ulbs = User::all();
        return view('ulbdashboard/rainwaterharvestinginfo', compact('nikay', 'item', 'ulbs'));
    }

    public function rainwaterharvestingsubmit(Request $request)
    {
        $validatedData = $request->validate([
            'dateInput' => 'required|date',
            'personal_house' => 'required|numeric',
            'govt_house' => 'required|numeric',
            'total_house' => 'required|numeric',
            'RWH_personal_house' => 'required|numeric',
            'RWH_govt_house' => 'required|numeric',
            'RWH_total_house' => 'required|numeric',
            'leftover_personal_house' => 'required|numeric',
            'leftover_govt_house' => 'required|numeric',
            'leftover_total_house' => 'required|numeric',
            'RWH_construction_deposited_amount' => 'required|numeric',
            'rwh_from_ulb_deposited_amt' => 'required|numeric',
            'ulbs_rwh_deposited_amount_left' => 'required|numeric',
            'remark' => 'nullable|string',
        ]);

        $nikay = Auth::guard('web')->user();

        do {
            $uniqueId = random_int(10000, 99999);
        } while (RainWaterHarvesting::where('unique_id', $uniqueId)->exists());

        $currentYear = Carbon::now()->year;
        $financialYear = (Carbon::now()->month >= 4)
            ? $currentYear . '-' . ($currentYear + 1)
            : ($currentYear - 1) . '-' . $currentYear;

        $data = new RainWaterHarvesting();
        $data->ulb_id = $nikay->id;
        $data->unique_id = $uniqueId;
        $data->dateInput = $validatedData['dateInput'];
        $data->financial_year = $financialYear;

        $data->personal_house = $request->personal_house;
        $data->govt_house = $request->govt_house;
        $data->total_house = $request->total_house;
        $data->RWH_personal_house = $request->RWH_personal_house;
        $data->RWH_govt_house = $request->RWH_govt_house;
        $data->RWH_total_house = $request->RWH_total_house;
        $data->leftover_personal_house = $request->leftover_personal_house;
        $data->leftover_govt_house = $request->leftover_govt_house;
        $data->leftover_total_house = $request->leftover_total_house;
        $data->RWH_construction_deposited_amount = $request->RWH_construction_deposited_amount;
        $data->rwh_from_ulb_deposited_amt = $request->rwh_from_ulb_deposited_amt;
        $data->ulbs_rwh_deposited_amount_left = $request->ulbs_rwh_deposited_amount_left;
        $data->remark = $request->remark;

        $data->save();

        Alert::success('Success!', 'Rainwater harvesting information submitted successfully.');
        return back();
    }


    public function rainwaterharvestingupdate(Request $request)
    {
        $validatedData = $request->validate([
            'dateInput' => 'required|date',
            'personal_house' => 'required|numeric',
            'govt_house' => 'required|numeric',
            'total_house' => 'required|numeric',
            'RWH_personal_house' => 'required|numeric',
            'RWH_govt_house' => 'required|numeric',
            'RWH_total_house' => 'required|numeric',
            'leftover_personal_house' => 'required|numeric',
            'leftover_govt_house' => 'required|numeric',
            'leftover_total_house' => 'required|numeric',
            'RWH_construction_deposited_amount' => 'required|numeric',
            'rwh_from_ulb_deposited_amt' => 'required|numeric',
            'ulbs_rwh_deposited_amount_left' => 'required|numeric',
            'remark' => 'nullable|string',
        ]);

        $housing = RainWaterHarvesting::findOrFail($request->id);

        $housing->personal_house = $request->personal_house;
        $housing->govt_house = $request->govt_house;
        $housing->total_house = $request->total_house;

        $housing->RWH_personal_house = $request->RWH_personal_house;
        $housing->RWH_govt_house = $request->RWH_govt_house;
        $housing->RWH_total_house = $request->RWH_total_house;

        $housing->leftover_personal_house = $request->leftover_personal_house;
        $housing->leftover_govt_house = $request->leftover_govt_house;
        $housing->leftover_total_house = $request->leftover_total_house;

        $housing->RWH_construction_deposited_amount = $request->RWH_construction_deposited_amount;
        $housing->rwh_from_ulb_deposited_amt = $request->rwh_from_ulb_deposited_amt;
        $housing->ulbs_rwh_deposited_amount_left = $request->ulbs_rwh_deposited_amount_left;
        $housing->remark = $request->remark;

        $housing->save();
        Alert::success('Success!', 'Rainwater harvesting information updated successfully.');
        return redirect()->back();
    }


    public function slrmbhawans()
    {
        $nikay = Auth::guard('web')->user();
        $item = SlrmCenterBhawan::where('ulb_id', $nikay->id)->get();
        $ulbs = User::all();
        return view('ulbdashboard/slrmbhawans', compact('nikay', 'item', 'ulbs'));
    }
    public function slrmbhawansubmit(Request $request)
    {
        $validatedData = $request->validate([
            'dateInput' => 'required|date',
            'slrmcenter_type' => 'required|string|max:255',
            'slrmcenter_location' => 'required|string|max:255',
            'slrmcenter_ward_no' => 'required|integer|min:1|max:100',
            'slrmcenter_ward_name' => 'required|string|max:255',
            'slrmcenter_total_area' => 'required|numeric',
            'slrmcenter_longitude' => 'required|numeric',
            'slrmcenter_latitude' => 'required|numeric',

            'slrmcenter_remark' => 'nullable|string',
        ]);

        $nikay = Auth::guard('web')->user();

        do {
            $uniqueId = random_int(10000, 99999);
        } while (SlrmCenterBhawan::where('unique_id', $uniqueId)->exists());

        $currentYear = Carbon::now()->year;
        $financialYear = (Carbon::now()->month >= 4)
            ? $currentYear . '-' . ($currentYear + 1)
            : ($currentYear - 1) . '-' . $currentYear;

        // Create new record
        $data = new SlrmCenterBhawan();
        $data->ulb_id = $nikay->id;
        $data->unique_id = $uniqueId;
        $data->dateInput = $validatedData['dateInput'];
        $data->financial_year = $financialYear;
        $data->slrmcenter_type = $validatedData['slrmcenter_type'];
        $data->slrmcenter_location = $validatedData['slrmcenter_location'];
        $data->slrmcenter_ward_no = $validatedData['slrmcenter_ward_no'];
        $data->slrmcenter_ward_name = $validatedData['slrmcenter_ward_name'];
        $data->slrmcenter_total_area = $validatedData['slrmcenter_total_area'];
        $data->slrmcenter_longitude = $validatedData['slrmcenter_longitude'];
        $data->slrmcenter_latitude = $validatedData['slrmcenter_latitude'];
        $data->slrmcenter_remark = $validatedData['slrmcenter_remark'];


        $data->save();

        Alert::success('Success!', 'Submitted successfully.');
        return back();
    }

    public function slrmbhawansupdate(Request $request)
    {
        $data = SlrmCenterBhawan::findOrFail($request->id);
        $data->update([
            'slrmcenter_type' => $request->slrmcenter_type,
            'slrmcenter_location' => $request->slrmcenter_location,
            'slrmcenter_ward_no' => $request->slrmcenter_ward_no,
            'slrmcenter_ward_name' => $request->slrmcenter_ward_name,
            'slrmcenter_total_area' => $request->slrmcenter_total_area,
            'slrmcenter_longitude' => $request->slrmcenter_longitude,
            'slrmcenter_latitude' => $request->slrmcenter_latitude,
            'slrmcenter_remark' => $request->slrmcenter_remark,
        ]);

        Alert::success('Success!', 'SLRM Center/Bhawan data updated successfully.');
        return redirect()->back();
    }
    public function gouthangovdhamkanjihouse()
    {
        $nikay = Auth::guard('web')->user();
        $item = GouthanGovdhamKanjiHouse::where('ulb_id', $nikay->id)->get();
        $ulbs = User::all();
        return view('ulbdashboard/gouthangovdhamkanjihouse', compact('nikay', 'item', 'ulbs'));
    }
    public function gouthangovdhamkanjihousesubmit(Request $request)
    {
        $validatedData = $request->validate([
            'dateInput' => 'required|date',
            'ggk_type' => 'required|string|max:255',
            'ggk_total_capacity' => 'required|integer|max:255',
            'ggk_total_area' => 'required|string|max:255',
            'total_maveshi_at_present' => 'required|integer|max:255',
            'ggk_longitude' => 'required|numeric',
            'ggk_latitude' => 'required|numeric',
            'ggk_remark' => 'nullable|string',
        ]);

        $nikay = Auth::guard('web')->user();

        do {
            $uniqueId = random_int(10000, 99999);
        } while (GouthanGovdhamKanjiHouse::where('unique_id', $uniqueId)->exists());

        $currentYear = Carbon::now()->year;
        $financialYear = (Carbon::now()->month >= 4)
            ? $currentYear . '-' . ($currentYear + 1)
            : ($currentYear - 1) . '-' . $currentYear;

        // Create new record
        $data = new GouthanGovdhamKanjiHouse();
        $data->ulb_id = $nikay->id;
        $data->unique_id = $uniqueId;
        $data->dateInput = $validatedData['dateInput'];
        $data->financial_year = $financialYear;
        $data->ggk_type = $validatedData['ggk_type'];
        $data->ggk_total_capacity = $validatedData['ggk_total_capacity'];
        $data->ggk_total_area = $validatedData['ggk_total_area'];
        $data->total_maveshi_at_present = $validatedData['total_maveshi_at_present'];
        $data->ggk_longitude = $validatedData['ggk_longitude'];
        $data->ggk_latitude = $validatedData['ggk_latitude'];
        $data->ggk_remark = $validatedData['ggk_remark'];
        $data->save();

        Alert::success('Success!', 'Submitted successfully.');
        return back();
    }
    public function gouthangovdhamkanjihouseupdate(Request $request)
    {
        $data = GouthanGovdhamKanjiHouse::findOrFail($request->id);

        $data->update([
            'ggk_type' => $request->ggk_type,
            'ggk_total_capacity' => $request->ggk_total_capacity,
            'ggk_total_area' => $request->ggk_total_area,
            'total_maveshi_at_present' => $request->total_maveshi_at_present,
            'ggk_longitude' => $request->ggk_longitude,
            'ggk_latitude' => $request->ggk_latitude,
            'ggk_remark' => $request->ggk_remark,
        ]);

        Alert::success('Success!', 'गौठान/गौधाम/कांजीहाउस की जानकारी सफलतापूर्वक अपडेट हो गई है।');
        return redirect()->back();
    }

    public function stpinfo()
    {
        $nikay = Auth::guard('web')->user();
        $item = Stp::where('ulb_id', $nikay->id)->get();
        $ulbs = User::all();
        return view('ulbdashboard/stpinfo', compact('nikay', 'item', 'ulbs'));
    }
    public function stpsubmit(Request $request)
    {
        $validatedData = $request->validate([
            'dateInput' => 'required|date',

            'stps_longitude' => 'required|numeric',
            'stps_latitude' => 'required|numeric',
            'stp_capitalize_in_amd' => 'required|string|max:255',
            'stps_remark' => 'nullable|string',
        ]);

        $nikay = Auth::guard('web')->user();

        do {
            $uniqueId = random_int(10000, 99999);
        } while (Stp::where('unique_id', $uniqueId)->exists());

        $currentYear = Carbon::now()->year;
        $financialYear = (Carbon::now()->month >= 4)
            ? $currentYear . '-' . ($currentYear + 1)
            : ($currentYear - 1) . '-' . $currentYear;

        // Create new record
        $data = new Stp();
        $data->ulb_id = $nikay->id;
        $data->unique_id = $uniqueId;
        $data->dateInput = $validatedData['dateInput'];
        $data->financial_year = $financialYear;
        $data->stps_longitude = $validatedData['stps_longitude'];
        $data->stps_latitude = $validatedData['stps_latitude'];
        $data->stp_capitalize_in_amd = $validatedData['stp_capitalize_in_amd'];
        $data->stps_remark = $validatedData['stps_remark'];
        $data->save();

        Alert::success('Success!', 'Submitted successfully.');
        return back();
    }
    public function stpupdate(Request $request)
    {
        $data = Stp::findOrFail($request->id);
        $data->update([
            'stps_longitude' => $request->stps_longitude,
            'stps_latitude' => $request->stps_latitude,
            'stp_capitalize_in_amd' => $request->stp_capitalize_in_amd,
            'stps_remark' => $request->stps_remark,
        ]);

        Alert::success('Success!', 'STP information updated successfully.');
        return redirect()->back();
    }
    public function wtpinfo()
    {
        $nikay = Auth::guard('web')->user();
        $item = Wtp::where('ulb_id', $nikay->id)->get();
        $ulbs = User::all();
        return view('ulbdashboard/wtpinfo', compact('nikay', 'item', 'ulbs'));
    }
    public function wtpsubmit(Request $request)
    {
        $validatedData = $request->validate([
            'dateInput' => 'required|date',
            'wtps_location' => 'required|string|max:255',
            'wtps_ward_no' => 'required|integer|min:1|max:100',
            'wtps_ward_name' => 'required|string|max:255',
            'wtps_longitude' => 'required|numeric',
            'wtps_latitude' => 'required|numeric',

            'wtps_remark' => 'nullable|string',
        ]);

        $nikay = Auth::guard('web')->user();

        do {
            $uniqueId = random_int(10000, 99999);
        } while (Wtp::where('unique_id', $uniqueId)->exists());

        $currentYear = Carbon::now()->year;
        $financialYear = (Carbon::now()->month >= 4)
            ? $currentYear . '-' . ($currentYear + 1)
            : ($currentYear - 1) . '-' . $currentYear;

        // Create new record
        $data = new Wtp();
        $data->ulb_id = $nikay->id;
        $data->unique_id = $uniqueId;
        $data->dateInput = $validatedData['dateInput'];
        $data->financial_year = $financialYear;
        $data->wtps_location = $validatedData['wtps_location'];
        $data->wtps_ward_no = $validatedData['wtps_ward_no'];
        $data->wtps_ward_name = $validatedData['wtps_ward_name'];
        $data->wtps_longitude = $validatedData['wtps_longitude'];
        $data->wtps_latitude = $validatedData['wtps_latitude'];
        $data->wtps_remark = $validatedData['wtps_remark'];
        $data->save();

        Alert::success('Success!', 'Submitted successfully.');
        return back();
    }

    public function wtpupdate(Request $request)
    {
        $data = Wtp::findOrFail($request->id);
        $data->update([
            'wtps_location' => $request->wtps_location,
            'wtps_ward_no' => $request->wtps_ward_no,
            'wtps_ward_name' => $request->wtps_ward_name,
            'wtps_longitude' => $request->wtps_longitude,
            'wtps_latitude' => $request->wtps_latitude,
            'wtps_remark' => $request->wtps_remark,
        ]);

        Alert::success('Success!', 'WTP information updated successfully.');
        return redirect()->back();
    }
    public function gyminfo()
    {
        $nikay = Auth::guard('web')->user();
        $item = Gym::where('ulb_id', $nikay->id)->get();
        $ulbs = User::all();
        return view('ulbdashboard/gyminfo', compact('nikay', 'item', 'ulbs'));
    }
    public function gymsubmit(Request $request)
    {
        $validatedData = $request->validate([
            'dateInput' => 'required|date',
            'gyms_type' => 'required|string|max:255',
            'gyms_total_capacity' => 'required|integer',
            'gyms_longitude' => 'required|numeric',
            'gyms_latitude' => 'required|numeric',

            'gyms_remark' => 'nullable|string',
        ]);

        $nikay = Auth::guard('web')->user();

        do {
            $uniqueId = random_int(10000, 99999);
        } while (Gym::where('unique_id', $uniqueId)->exists());

        $currentYear = Carbon::now()->year;
        $financialYear = (Carbon::now()->month >= 4)
            ? $currentYear . '-' . ($currentYear + 1)
            : ($currentYear - 1) . '-' . $currentYear;

        // Create new record
        $data = new Gym();
        $data->ulb_id = $nikay->id;
        $data->unique_id = $uniqueId;
        $data->dateInput = $validatedData['dateInput'];
        $data->financial_year = $financialYear;
        $data->gyms_type = $validatedData['gyms_type'];
        $data->gyms_total_capacity = $validatedData['gyms_total_capacity'];
        $data->gyms_longitude = $validatedData['gyms_longitude'];
        $data->gyms_latitude = $validatedData['gyms_latitude'];
        $data->gyms_remark = $validatedData['gyms_remark'];
        $data->save();

        Alert::success('Success!', 'Submitted successfully.');
        return back();
    }
    public function gymupdate(Request $request)
    {
        $data = Gym::findOrFail($request->id);
        $data->update([
            'gyms_type' => $request->gyms_type,
            'gyms_total_capacity' => $request->gyms_total_capacity,
            'gyms_longitude' => $request->gyms_longitude,
            'gyms_latitude' => $request->gyms_latitude,
            'gyms_remark' => $request->gyms_remark,
        ]);

        Alert::success('Success!', 'Gym information updated successfully.');
        return redirect()->back();
    }
    public function toiletinfo()
    {
        $nikay = Auth::guard('web')->user();
        $item = Toilet::where('ulb_id', $nikay->id)->get();
        $ulbs = User::all();
        return view('ulbdashboard/toiletinfo', compact('nikay', 'item', 'ulbs'));
    }
    public function toiletsubmit(Request $request)
    {
        $validatedData = $request->validate([
            'dateInput' => 'required|date',
            'toilets_type' => 'required|string|max:255',
            'toilets_location' => 'required|string|max:255',
            'toilets_ward_no' => 'required|integer|min:1|max:100',
            'toilets_ward_name' => 'required|string|max:255',
            'toilets_total_seat' => 'required|integer|max:255',
            'toilets_working_seat' => 'required|integer|max:255',
            'toilets_longitude' => 'required|numeric',
            'toilets_latitude' => 'required|numeric',
            'toilets_remark' => 'nullable|string'
        ]);

        $nikay = Auth::guard('web')->user();

        do {
            $uniqueId = random_int(10000, 99999);
        } while (Toilet::where('unique_id', $uniqueId)->exists());

        $currentYear = Carbon::now()->year;
        $financialYear = (Carbon::now()->month >= 4)
            ? $currentYear . '-' . ($currentYear + 1)
            : ($currentYear - 1) . '-' . $currentYear;

        // Create new record
        $data = new Toilet();
        $data->ulb_id = $nikay->id;
        $data->unique_id = $uniqueId;
        $data->dateInput = $validatedData['dateInput'];
        $data->financial_year = $financialYear;
        $data->toilets_type = $validatedData['toilets_type'];
        $data->toilets_location = $validatedData['toilets_location'];
        $data->toilets_ward_no = $validatedData['toilets_ward_no'];
        $data->toilets_ward_name = $validatedData['toilets_ward_name'];
        $data->toilets_total_seat = $validatedData['toilets_total_seat'];
        $data->toilets_working_seat = $validatedData['toilets_working_seat'];
        $data->toilets_longitude = $validatedData['toilets_longitude'];
        $data->toilets_latitude = $validatedData['toilets_latitude'];
        $data->toilets_remark = $validatedData['toilets_remark'];


        $data->save();

        Alert::success('Success!', 'Submitted successfully.');
        return back();
    }
    public function toiletupdate(Request $request)
    {
        $data = Toilet::findOrFail($request->id);
        $data->update([
            'toilets_type' => $request->toilets_type,
            'toilets_location' => $request->toilets_location,
            'toilets_ward_no' => $request->toilets_ward_no,
            'toilets_ward_name' => $request->toilets_ward_name,
            'toilets_total_seat' => $request->toilets_total_seat,
            'toilets_working_seat' => $request->toilets_working_seat,
            'toilets_longitude' => $request->toilets_longitude,
            'toilets_latitude' => $request->toilets_latitude,
            'toilets_remark' => $request->toilets_remark,
        ]);

        Alert::success('Success!', 'Toilet information updated successfully.');
        return redirect()->back();
    }
    public function rajasvasullidemandsubmit(Request $request)
    {
        $request->validate(
            [
                'date' => 'required|date',
                'taxtype_id' => 'required',
                'current_demand' => 'required',
                'pending_demand' => 'required',
            ],
        );
        $nikay = Auth::guard('web')->user();
        $currentYear = Carbon::now()->year;
        $financialYear = (Carbon::now()->month >= 4)
            ? $currentYear . '-' . ($currentYear + 1)
            : ($currentYear - 1) . '-' . $currentYear;

        $data = new RevenueDemand();
        $data->ulb_id = $nikay->id;

        $data->financial_year = $financialYear;
        // $data->ulb_id = $request->input('ulb_id');
        $data->taxtype_id = $request->input('taxtype_id');
        $data->date = $request->input('date');
        $data->current_demand = $request->input('current_demand');
        $data->pending_demand = $request->input('pending_demand');

        $data->save();
        Alert::success('Success!', 'Data submitted successfully');
        return back();
    }

    public function financialdetails()
    {
        $nikay = Auth::guard('web')->user();

        // Check if basic details exist (require basic details before financial details)
        $basicDetail = UlbBasicDetail::where('ulb_id', $nikay->id)->first();

        // if (!$basicDetail) {
        //     Alert::warning('Warning!', 'Please complete the Basic Details form first before accessing Financial Details.');
        //     return redirect('updatereports');
        // }

        $financialDetail = UlbFinancialDetail::where('ulb_id', $nikay->id)->first();

        // Determine the form mode based on request parameters
        $mode = 'create'; // Default mode is create

        if ($financialDetail) {
            // If details exist and update is requested
            if (request()->has('update')) {
                $mode = 'update';
            }
            // If just viewing details
            else {
                $mode = 'view';
            }
        }

        // Check if this is an update request but no record exists
        if (request()->has('update') && !$financialDetail) {
            Alert::warning('Warning!', 'No Financial Details found to update. Please submit the details first.');
            return redirect('updatereports');
        }

        return view('ulbdashboard/financialdetails', compact('nikay', 'financialDetail', 'mode'));
    }

    public function financialdetailssubmit(Request $request)
    {
        $nikay = Auth::guard('web')->user();

        // Validation
        $validated = $request->validate([
            'annual_budget' => 'nullable|numeric',
            'property_tax_demand' => 'nullable|numeric',
            'property_tax_collection' => 'nullable|numeric',
            'water_charges_demand' => 'nullable|numeric',
            'water_charges_collection' => 'nullable|numeric',
            'total_households' => 'nullable|integer',
            'total_water_tap_connections' => 'nullable|integer',
            'water_tap_coverage_percentage' => 'nullable|numeric|max:100',
            'water_crisis_ward_numbers' => 'nullable|string',
            'total_overhead_tanks' => 'nullable|integer',
            'overhead_tanks_capacity' => 'nullable|string',
            'total_wtp' => 'nullable|integer',
            'wtp_capacity' => 'nullable|string',
            'user_fees_demand' => 'nullable|numeric',
            'user_fees_collection' => 'nullable|numeric',
            'total_households_user_fees' => 'nullable|integer',
            'total_households_door_to_door' => 'nullable|integer',
            'total_households_as_per_demand' => 'nullable|integer',
            'total_households_as_per_census' => 'nullable|integer',
            'door_to_door_expenditure' => 'nullable|numeric',
            'grants_from_government' => 'nullable|numeric',
            'other_revenue_sources' => 'nullable|string',
            'expenditure_salaries' => 'nullable|numeric',
            'expenditure_infrastructure' => 'nullable|numeric',
            'expenditure_sanitation' => 'nullable|numeric',
            'expenditure_water_supply' => 'nullable|numeric',
            'other_expenditures' => 'nullable|string'
        ]);

        // Check if record already exists for this ULB
        $exists = UlbFinancialDetail::where('ulb_id', $nikay->id)->exists();

        if ($exists) {
            Alert::warning('Details Already Submitted', 'Financial details have already been submitted. Please use the update option to modify your information.');
            return redirect('updatereports');
        }

        // Create new record
        try {
            $financialDetail = new UlbFinancialDetail();
            $financialDetail->ulb_id = $nikay->id;

            // Set all the fields from the request
            foreach ($validated as $key => $value) {
                $financialDetail->$key = $value;
            }

            $financialDetail->save();

            Alert::success('Success', 'Financial Details submitted successfully!');
            return redirect('updatereports');
        } catch (\Exception $e) {
            Alert::error('Error', 'Failed to submit Financial Details. Please try again. ' . $e->getMessage());
            return back()->withInput();
        }
    }

    public function financialdetailsupdate(Request $request)
    {
        $nikay = Auth::guard('web')->user();

        // Validation
        $validated = $request->validate([
            'annual_budget' => 'nullable|numeric',
            'property_tax_demand' => 'nullable|numeric',
            'property_tax_collection' => 'nullable|numeric',
            'water_charges_demand' => 'nullable|numeric',
            'water_charges_collection' => 'nullable|numeric',
            'total_households' => 'nullable|integer',
            'total_water_tap_connections' => 'nullable|integer',
            'water_tap_coverage_percentage' => 'nullable|numeric|max:100',
            'water_crisis_ward_numbers' => 'nullable|string',
            'total_overhead_tanks' => 'nullable|integer',
            'overhead_tanks_capacity' => 'nullable|string',
            'total_wtp' => 'nullable|integer',
            'wtp_capacity' => 'nullable|string',
            'user_fees_demand' => 'nullable|numeric',
            'user_fees_collection' => 'nullable|numeric',
            'total_households_user_fees' => 'nullable|integer',
            'total_households_door_to_door' => 'nullable|integer',
            'total_households_as_per_demand' => 'nullable|integer',
            'total_households_as_per_census' => 'nullable|integer',
            'door_to_door_expenditure' => 'nullable|numeric',
            'grants_from_government' => 'nullable|numeric',
            'other_revenue_sources' => 'nullable|string',
            'expenditure_salaries' => 'nullable|numeric',
            'expenditure_infrastructure' => 'nullable|numeric',
            'expenditure_sanitation' => 'nullable|numeric',
            'expenditure_water_supply' => 'nullable|numeric',
            'other_expenditures' => 'nullable|string'
        ]);

        // Find existing record
        $financialDetail = UlbFinancialDetail::where('ulb_id', $nikay->id)->first();

        if (!$financialDetail) {
            Alert::warning('Record Not Found', 'No Financial Details found to update. Please submit the details first.');
            return redirect('financialdetails');
        }

        // Check attempts limit
        if ($financialDetail->attempts >= 2) {
            Alert::warning('Update Limit Reached!', 'You have already updated this form twice, which is the maximum number of updates allowed.');
            return redirect('updatereports');
        }

        // Update record
        try {
            // Update all fields from the request
            foreach ($validated as $key => $value) {
                $financialDetail->$key = $value;
            }

            $financialDetail->attempts = $financialDetail->attempts + 1;
            $financialDetail->save();

            Alert::success('Success', 'Financial Details updated successfully! You have ' . (2 - $financialDetail->attempts) . ' updates remaining.');
            return redirect('financialdetails');
        } catch (\Exception $e) {
            Alert::error('Error', 'Failed to update Financial Details. Please try again. ' . $e->getMessage());
            return back()->withInput();
        }
    }

    /**
     * Show the infrastructure services form
     */
    public function infrastructureservices()
    {
        $user = Auth::guard('web')->user();
        $infrastructureService = UlbInfrastructureService::where('ulb_id', $user->id)->first();
        return view('ulbdashboard.infrastructureservices', compact('infrastructureService'));
    }

    /**
     * Store a new infrastructure services record
     */
    public function infrastructureservicessubmit(Request $request)
    {
        $request->validate([
            'has_water_supply' => 'required|boolean',
            'water_supply_coverage_percentage' => 'nullable|numeric|min:0|max:100',
            'has_sewage_system' => 'required|boolean',
            'sewage_system_coverage_percentage' => 'nullable|numeric|min:0|max:100',
            'solid_waste_management_details' => 'nullable|string',
            'public_toilets_count' => 'nullable|integer|min:0',
            'public_toilets_locations' => 'nullable|string',
            'roads_status' => 'nullable|string',
            'drainage_status' => 'nullable|string',
            'street_lighting_coverage_percentage' => 'nullable|numeric|min:0|max:100',
            'public_transport_facilities' => 'nullable|string',
        ]);

        $user = Auth::guard('web')->user();

        UlbInfrastructureService::create([
            'ulb_id' => $user->id,
            'has_water_supply' => $request->has_water_supply,
            'water_supply_coverage_percentage' => $request->water_supply_coverage_percentage,
            'has_sewage_system' => $request->has_sewage_system,
            'sewage_system_coverage_percentage' => $request->sewage_system_coverage_percentage,
            'solid_waste_management_details' => $request->solid_waste_management_details,
            'public_toilets_count' => $request->public_toilets_count,
            'public_toilets_locations' => $request->public_toilets_locations,
            'roads_status' => $request->roads_status,
            'drainage_status' => $request->drainage_status,
            'street_lighting_coverage_percentage' => $request->street_lighting_coverage_percentage,
            'public_transport_facilities' => $request->public_transport_facilities,
        ]);

        Alert::success('Success', 'Infrastructure and Services details submitted successfully');
        return redirect()->back();
    }

    /**
     * Update an existing infrastructure services record
     */
    public function infrastructureservicesupdate(Request $request)
    {
        $request->validate([
            'has_water_supply' => 'required|boolean',
            'water_supply_coverage_percentage' => 'nullable|numeric|min:0|max:100',
            'has_sewage_system' => 'required|boolean',
            'sewage_system_coverage_percentage' => 'nullable|numeric|min:0|max:100',
            'solid_waste_management_details' => 'nullable|string',
            'public_toilets_count' => 'nullable|integer|min:0',
            'public_toilets_locations' => 'nullable|string',
            'roads_status' => 'nullable|string',
            'drainage_status' => 'nullable|string',
            'street_lighting_coverage_percentage' => 'nullable|numeric|min:0|max:100',
            'public_transport_facilities' => 'nullable|string',
        ]);

        $user = Auth::guard('web')->user();
        $infrastructureService = UlbInfrastructureService::where('ulb_id', $user->id)->first();

        if (!$infrastructureService) {
            Alert::error('Error', 'Record not found');
            return redirect('updatereports');
        }

        // Check attempts limit
        if ($infrastructureService->attempts >= 2) {
            Alert::warning('Update Limit Reached!', 'You have already updated this form twice, which is the maximum number of updates allowed.');
            return redirect('updatereports');
        }

        $infrastructureService->update([
            'has_water_supply' => $request->has_water_supply,
            'water_supply_coverage_percentage' => $request->water_supply_coverage_percentage,
            'has_sewage_system' => $request->has_sewage_system,
            'sewage_system_coverage_percentage' => $request->sewage_system_coverage_percentage,
            'solid_waste_management_details' => $request->solid_waste_management_details,
            'public_toilets_count' => $request->public_toilets_count,
            'public_toilets_locations' => $request->public_toilets_locations,
            'roads_status' => $request->roads_status,
            'drainage_status' => $request->drainage_status,
            'street_lighting_coverage_percentage' => $request->street_lighting_coverage_percentage,
            'public_transport_facilities' => $request->public_transport_facilities,
            'attempts' => $infrastructureService->attempts + 1,
        ]);

        Alert::success('Success!', 'Infrastructure and Services details updated successfully. You have ' . (2 - $infrastructureService->attempts) . ' updates remaining.');
        return redirect('updatereports');
    }

    /**
     * Show the development projects form
     */
    public function developmentprojects()
    {
        $user = Auth::guard('web')->user();
        $developmentProject = UlbDevelopmentProject::where('ulb_id', $user->id)->first();
        return view('ulbdashboard.developmentprojects', compact('developmentProject'));
    }

    /**
     * Store a new development projects record
     */
    public function developmentprojectssubmit(Request $request)
    {
        $request->validate([
            'finance_total_works' => 'nullable|integer',
            'finance_completed' => 'nullable|integer',
            'finance_in_progress' => 'nullable|integer',
            'finance_not_started' => 'nullable|integer',
            'finance_tender_floated' => 'nullable|integer',
            'finance_tender_not_floated' => 'nullable|integer',
            'adho_total_works' => 'nullable|integer',
            'adho_completed' => 'nullable|integer',
            'adho_in_progress' => 'nullable|integer',
            'adho_not_started' => 'nullable|integer',
            'adho_tender_floated' => 'nullable|integer',
            'adho_tender_not_floated' => 'nullable|integer',
            'rajya_total_works' => 'nullable|integer',
            'rajya_completed' => 'nullable|integer',
            'rajya_in_progress' => 'nullable|integer',
            'rajya_not_started' => 'nullable|integer',
            'rajya_tender_floated' => 'nullable|integer',
            'rajya_tender_not_floated' => 'nullable|integer',
            'completed_projects' => 'nullable|string',
            'has_smart_city_projects' => 'required|boolean',
            'smart_city_projects_details' => 'nullable|string',
            'has_amrut_projects' => 'required|boolean',
            'amrut_projects_details' => 'nullable|string',
            'has_pmay_projects' => 'required|boolean',
            'pmay_projects_details' => 'nullable|string',
            'has_swachh_bharat_projects' => 'required|boolean',
            'swachh_bharat_projects_details' => 'nullable|string',
        ]);

        $user = Auth::guard('web')->user();

        UlbDevelopmentProject::create([
            'ulb_id' => $user->id,
            'finance_total_works' => $request->finance_total_works,
            'finance_completed' => $request->finance_completed,
            'finance_in_progress' => $request->finance_in_progress,
            'finance_not_started' => $request->finance_not_started,
            'finance_tender_floated' => $request->finance_tender_floated,
            'finance_tender_not_floated' => $request->finance_tender_not_floated,
            'adho_total_works' => $request->adho_total_works,
            'adho_completed' => $request->adho_completed,
            'adho_in_progress' => $request->adho_in_progress,
            'adho_not_started' => $request->adho_not_started,
            'adho_tender_floated' => $request->adho_tender_floated,
            'adho_tender_not_floated' => $request->adho_tender_not_floated,
            'rajya_total_works' => $request->rajya_total_works,
            'rajya_completed' => $request->rajya_completed,
            'rajya_in_progress' => $request->rajya_in_progress,
            'rajya_not_started' => $request->rajya_not_started,
            'rajya_tender_floated' => $request->rajya_tender_floated,
            'rajya_tender_not_floated' => $request->rajya_tender_not_floated,
            'completed_projects' => $request->completed_projects,
            'has_smart_city_projects' => $request->has_smart_city_projects,
            'smart_city_projects_details' => $request->smart_city_projects_details,
            'has_amrut_projects' => $request->has_amrut_projects,
            'amrut_projects_details' => $request->amrut_projects_details,
            'has_pmay_projects' => $request->has_pmay_projects,
            'pmay_projects_details' => $request->pmay_projects_details,
            'has_swachh_bharat_projects' => $request->has_swachh_bharat_projects,
            'swachh_bharat_projects_details' => $request->swachh_bharat_projects_details,
        ]);

        Alert::success('Success', 'Development Projects details submitted successfully');
        return redirect()->back();
    }

    /**
     * Update an existing development projects record
     */
    public function developmentprojectsupdate(Request $request)
    {
        $request->validate([
            'finance_total_works' => 'nullable|integer',
            'finance_completed' => 'nullable|integer',
            'finance_in_progress' => 'nullable|integer',
            'finance_not_started' => 'nullable|integer',
            'finance_tender_floated' => 'nullable|integer',
            'finance_tender_not_floated' => 'nullable|integer',
            'adho_total_works' => 'nullable|integer',
            'adho_completed' => 'nullable|integer',
            'adho_in_progress' => 'nullable|integer',
            'adho_not_started' => 'nullable|integer',
            'adho_tender_floated' => 'nullable|integer',
            'adho_tender_not_floated' => 'nullable|integer',
            'rajya_total_works' => 'nullable|integer',
            'rajya_completed' => 'nullable|integer',
            'rajya_in_progress' => 'nullable|integer',
            'rajya_not_started' => 'nullable|integer',
            'rajya_tender_floated' => 'nullable|integer',
            'rajya_tender_not_floated' => 'nullable|integer',
            'completed_projects' => 'nullable|string',
            'has_smart_city_projects' => 'required|boolean',
            'smart_city_projects_details' => 'nullable|string',
            'has_amrut_projects' => 'required|boolean',
            'amrut_projects_details' => 'nullable|string',
            'has_pmay_projects' => 'required|boolean',
            'pmay_projects_details' => 'nullable|string',
            'has_swachh_bharat_projects' => 'required|boolean',
            'swachh_bharat_projects_details' => 'nullable|string',
        ]);

        $user = Auth::guard('web')->user();
        $developmentProject = UlbDevelopmentProject::where('ulb_id', $user->id)->first();

        if (!$developmentProject) {
            Alert::error('Error', 'Record not found');
            return redirect('updatereports');
        }

        // Check attempts limit
        if ($developmentProject->attempts >= 2) {
            Alert::warning('Update Limit Reached!', 'You have already updated this form twice, which is the maximum number of updates allowed.');
            return redirect('updatereports');
        }

        $developmentProject->update([
            'finance_total_works' => $request->finance_total_works,
            'finance_completed' => $request->finance_completed,
            'finance_in_progress' => $request->finance_in_progress,
            'finance_not_started' => $request->finance_not_started,
            'finance_tender_floated' => $request->finance_tender_floated,
            'finance_tender_not_floated' => $request->finance_tender_not_floated,
            'adho_total_works' => $request->adho_total_works,
            'adho_completed' => $request->adho_completed,
            'adho_in_progress' => $request->adho_in_progress,
            'adho_not_started' => $request->adho_not_started,
            'adho_tender_floated' => $request->adho_tender_floated,
            'adho_tender_not_floated' => $request->adho_tender_not_floated,
            'rajya_total_works' => $request->rajya_total_works,
            'rajya_completed' => $request->rajya_completed,
            'rajya_in_progress' => $request->rajya_in_progress,
            'rajya_not_started' => $request->rajya_not_started,
            'rajya_tender_floated' => $request->rajya_tender_floated,
            'rajya_tender_not_floated' => $request->rajya_tender_not_floated,
            'completed_projects' => $request->completed_projects,
            'has_smart_city_projects' => $request->has_smart_city_projects,
            'smart_city_projects_details' => $request->smart_city_projects_details,
            'has_amrut_projects' => $request->has_amrut_projects,
            'amrut_projects_details' => $request->amrut_projects_details,
            'has_pmay_projects' => $request->has_pmay_projects,
            'pmay_projects_details' => $request->pmay_projects_details,
            'has_swachh_bharat_projects' => $request->has_swachh_bharat_projects,
            'swachh_bharat_projects_details' => $request->swachh_bharat_projects_details,
            'attempts' => $developmentProject->attempts + 1,
        ]);

        Alert::success('Success!', 'Development Projects details updated successfully. You have ' . (2 - $developmentProject->attempts) . ' updates remaining.');
        return redirect('updatereports');
    }

    /**
     * Show the digital governance form
     */
    public function digitalgovernance()
    {
        $user = Auth::guard('web')->user();
        $digitalGovernance = UlbDigitalGovernance::where('ulb_id', $user->id)->first();
        return view('ulbdashboard.digitalgovernance', compact('digitalGovernance'));
    }

    /**
     * Store a new digital governance record
     */
    public function digitalgovernancesubmit(Request $request)
    {
        $request->validate([
            'online_services_provided' => 'nullable|string',
            'uses_gis_tools' => 'required|boolean',
            'gis_mis_tools_details' => 'nullable|string',
            'e_governance_initiatives' => 'nullable|string',
        ]);

        $user = Auth::guard('web')->user();

        UlbDigitalGovernance::create([
            'ulb_id' => $user->id,
            'online_services_provided' => $request->online_services_provided,
            'uses_gis_tools' => $request->uses_gis_tools,
            'gis_mis_tools_details' => $request->gis_mis_tools_details,
            'e_governance_initiatives' => $request->e_governance_initiatives,
        ]);

        Alert::success('Success', 'Digital Governance details submitted successfully');
        return redirect()->back();
    }

    /**
     * Update an existing digital governance record
     */
    public function digitalgovernanceupdate(Request $request)
    {
        $request->validate([
            'online_services_provided' => 'nullable|string',
            'uses_gis_tools' => 'required|boolean',
            'gis_mis_tools_details' => 'nullable|string',
            'e_governance_initiatives' => 'nullable|string',
        ]);

        $user = Auth::guard('web')->user();
        $digitalGovernance = UlbDigitalGovernance::where('ulb_id', $user->id)->first();

        if (!$digitalGovernance) {
            Alert::error('Error', 'Record not found');
            return redirect('updatereports');
        }

        // Check attempts limit
        if ($digitalGovernance->attempts >= 2) {
            Alert::warning('Update Limit Reached!', 'You have already updated this form twice, which is the maximum number of updates allowed.');
            return redirect('updatereports');
        }

        $digitalGovernance->update([
            'online_services_provided' => $request->online_services_provided,
            'uses_gis_tools' => $request->uses_gis_tools,
            'gis_mis_tools_details' => $request->gis_mis_tools_details,
            'e_governance_initiatives' => $request->e_governance_initiatives,
            'attempts' => $digitalGovernance->attempts + 1,
        ]);

        Alert::success('Success!', 'Digital Governance details updated successfully. You have ' . (2 - $digitalGovernance->attempts) . ' updates remaining.');
        return redirect('updatereports');
    }

    /**
     * Show the citizen participation form
     */
    public function citizenparticipation()
    {
        $user = Auth::guard('web')->user();

        // Check if basic details exist (require basic details before this form)
        $basicDetail = UlbBasicDetail::where('ulb_id', $user->id)->first();

        $citizenParticipation = CitizenParticipation::where('ulb_id', $user->id)->first();

        // Determine the form mode based on request parameters
        $mode = 'create'; // Default mode is create

        if ($citizenParticipation) {
            // If details exist and update is requested
            if (request()->has('update')) {
                $mode = 'update';
            }
            // If just viewing details
            else {
                $mode = 'view';
            }
        }

        // Check if this is an update request but no record exists
        if (request()->has('update') && !$citizenParticipation) {
            Alert::warning('Warning!', 'No Citizen Participation details found to update. Please submit the details first.');
            return redirect('updatereports');
        }

        return view('ulbdashboard.citizenparticipation', compact('user', 'citizenParticipation', 'mode'));
    }

    public function citizenparticipationsubmit(Request $request)
    {
        // dd($request);
        $request->validate([
            'grievance_redressal_mechanism' => 'nullable|string',
            'ward_committees_functioning' => 'required|boolean',
            'citizen_feedback_channels' => 'nullable|string',
            'other_initiatives' => 'nullable|string',
        ]);

        $user = Auth::guard('web')->user();

        // Check if record already exists for this ULB
        $exists = CitizenParticipation::where('ulb_id', $user->id)->exists();

        if ($exists) {
            Alert::warning('Details Already Submitted', 'Citizen Participation details have already been submitted. Please use the update option to modify your information.');
            return redirect('updatereports');
        }

        CitizenParticipation::create([
            'ulb_id' => $user->id,
            'grievance_redressal_mechanism' => $request->grievance_redressal_mechanism,
            'ward_committees_functioning' => $request->ward_committees_functioning,
            'citizen_feedback_channels' => $request->citizen_feedback_channels,
            'other_initiatives' => $request->other_initiatives,
        ]);

        Alert::success('Success', 'Citizen Participation details submitted successfully');
        return redirect('updatereports');
    }

    /**
     * Update an existing citizen participation record
     */
    public function citizenparticipationupdate(Request $request)
    {
        $request->validate([
            'grievance_redressal_mechanism' => 'nullable|string',
            'ward_committees_functioning' => 'required|boolean',
            'citizen_feedback_channels' => 'nullable|string',
            'other_initiatives' => 'nullable|string',
        ]);

        $user = Auth::guard('web')->user();

        $citizenParticipation = CitizenParticipation::where('ulb_id', $user->id)->first();

        if (!$citizenParticipation) {
            Alert::error('Error', 'No Citizen Participation details found to update.');
            return redirect('updatereports');
        }

        // Check attempts limit
        if ($citizenParticipation->attempts >= 2) {
            Alert::warning('Update Limit Reached!', 'You have already updated this form twice, which is the maximum number of updates allowed.');
            return redirect('updatereports');
        }

        $citizenParticipation->update([
            'grievance_redressal_mechanism' => $request->grievance_redressal_mechanism,
            'ward_committees_functioning' => $request->ward_committees_functioning,
            'citizen_feedback_channels' => $request->citizen_feedback_channels,
            'other_initiatives' => $request->other_initiatives,
            'attempts' => $citizenParticipation->attempts + 1,
        ]);

        Alert::success('Success!', 'Citizen Participation details updated successfully. You have ' . (2 - $citizenParticipation->attempts) . ' updates remaining.');
        return redirect('updatereports');
    }

    /**
     * Show the environmental initiatives form
     */
    public function environmentalinitiatives()
    {
        $user = Auth::guard('web')->user();

        // Check if basic details exist (require basic details before this form)
        $basicDetail = UlbBasicDetail::where('ulb_id', $user->id)->first();

        $environmentalInitiative = EnvironmentalInitiative::where('ulb_id', $user->id)->first();

        // Determine the form mode based on request parameters
        $mode = 'create'; // Default mode is create

        if ($environmentalInitiative) {
            // If details exist and update is requested
            if (request()->has('update')) {
                $mode = 'update';
            }
            // If just viewing details
            else {
                $mode = 'view';
            }
        }

        // Check if this is an update request but no record exists
        if (request()->has('update') && !$environmentalInitiative) {
            Alert::warning('Warning!', 'No Environmental Initiatives details found to update. Please submit the details first.');
            return redirect('updatereports');
        }

        return view('ulbdashboard.environmentalinitiatives', compact('user', 'environmentalInitiative', 'mode'));
    }

    public function environmentalinitiativessubmit(Request $request)
    {
        $request->validate([
            'green_spaces' => 'nullable|string',
            'tree_plantation_drives' => 'nullable|string',
            'stp_number' => 'nullable|integer',
            'under_progress_stp' => 'nullable|string',
            'rainwater_harvesting_initiatives' => 'nullable|string',
            'household_rainwater_harvesting_count' => 'nullable|integer',
        ]);

        $user = Auth::guard('web')->user();

        // Check if record already exists for this ULB
        $exists = EnvironmentalInitiative::where('ulb_id', $user->id)->exists();

        if ($exists) {
            Alert::warning('Details Already Submitted', 'Environmental Initiatives details have already been submitted. Please use the update option to modify your information.');
            return redirect('updatereports');
        }

        EnvironmentalInitiative::create([
            'ulb_id' => $user->id,
            'green_spaces' => $request->green_spaces,
            'tree_plantation_drives' => $request->tree_plantation_drives,
            'stp_number' => $request->stp_number,
            'under_progress_stp' => $request->under_progress_stp,
            'rainwater_harvesting_initiatives' => $request->rainwater_harvesting_initiatives,
            'household_rainwater_harvesting_count' => $request->household_rainwater_harvesting_count,
        ]);

        Alert::success('Success', 'Environmental Initiatives details submitted successfully');
        return redirect('updatereports');
    }

    /**
     * Update an existing environmental initiatives record
     */
    public function environmentalinitiativesupdate(Request $request)
    {
        $request->validate([
            'green_spaces' => 'nullable|string',
            'tree_plantation_drives' => 'nullable|string',
            'stp_number' => 'nullable|integer',
            'under_progress_stp' => 'nullable|string',
            'rainwater_harvesting_initiatives' => 'nullable|string',
            'household_rainwater_harvesting_count' => 'nullable|integer',
        ]);

        $user = Auth::guard('web')->user();

        $environmentalInitiative = EnvironmentalInitiative::where('ulb_id', $user->id)->first();

        if (!$environmentalInitiative) {
            Alert::error('Error', 'No Environmental Initiatives details found to update.');
            return redirect('updatereports');
        }

        // Check attempts limit
        if ($environmentalInitiative->attempts >= 2) {
            Alert::warning('Update Limit Reached!', 'You have already updated this form twice, which is the maximum number of updates allowed.');
            return redirect('updatereports');
        }

        $environmentalInitiative->update([
            'green_spaces' => $request->green_spaces,
            'tree_plantation_drives' => $request->tree_plantation_drives,
            'stp_number' => $request->stp_number,
            'under_progress_stp' => $request->under_progress_stp,
            'rainwater_harvesting_initiatives' => $request->rainwater_harvesting_initiatives,
            'household_rainwater_harvesting_count' => $request->household_rainwater_harvesting_count,
            'attempts' => $environmentalInitiative->attempts + 1,
        ]);

        Alert::success('Success!', 'Environmental Initiatives details updated successfully. You have ' . (2 - $environmentalInitiative->attempts) . ' updates remaining.');
        return redirect('updatereports');
    }

    /**
     * Show the recreational facilities form
     */
    public function recreationalfacilities()
    {
        $user = Auth::guard('web')->user();

        // Check if basic details exist (require basic details before this form)
        $basicDetail = UlbBasicDetail::where('ulb_id', $user->id)->first();


        $recreationalFacilities = RecreationalFacility::where('ulb_id', $user->id)->get();

        // Get a list of facility types that are already used by this ULB
        $usedFacilityTypes = $recreationalFacilities->pluck('facility_type')->toArray();

        $facilityTypes = [
            'Park / Garden',
            'Playground',
            'Sports Complex',
            'Swimming Pool',
            'Cultural Center',
            'Auditorium',
            'Community Hall'
        ];

        $conditionOptions = ['Good', 'Fair', 'Poor'];
        $ownershipOptions = ['ULB', 'PPP', 'Other'];

        return view('ulbdashboard.recreationalfacilities', compact(
            'user',
            'recreationalFacilities',
            'facilityTypes',
            'usedFacilityTypes',
            'conditionOptions',
            'ownershipOptions'
        ));
    }

    /**
     * Store a new recreational facility record
     */
    public function recreationalfacilitiessubmit(Request $request)
    {
        $request->validate([
            'facility_type' => 'required|string',
            'facility_name' => 'required|string',
            'location' => 'required|string',
            'condition' => 'required|string',
            'ownership' => 'required|string',
            'year_established' => 'nullable|integer|min:1900|max:' . date('Y'),
            'entry_fee' => 'nullable|numeric|min:0',
            'monthly_footfall' => 'nullable|integer|min:0',
            'remarks' => 'nullable|string|max:500',
        ]);

        $user = Auth::guard('web')->user();

        // Check the number of facilities already added
        $facilitiesCount = RecreationalFacility::where('ulb_id', $user->id)->count();

        // Limit the number of facilities to 20 per ULB
        if ($facilitiesCount >= 20) {
            Alert::warning('Limit Reached', 'You have reached the maximum limit of 20 recreational facilities.');
            return redirect()->back();
        }

        // Check if a facility with the same type and name already exists for this ULB
        $existingFacility = RecreationalFacility::where('ulb_id', $user->id)
            ->where('facility_type', $request->facility_type)
            ->where('facility_name', $request->facility_name)
            ->first();

        if ($existingFacility) {
            Alert::warning('Duplicate Entry', 'A facility with this name and type already exists in your inventory.');
            return redirect()->back()->withInput();
        }

        RecreationalFacility::create([
            'ulb_id' => $user->id,
            'facility_type' => $request->facility_type,
            'facility_name' => $request->facility_name,
            'location' => $request->location,
            'year_established' => $request->year_established,
            'condition' => $request->condition,
            'ownership' => $request->ownership,
            'entry_fee' => $request->entry_fee,
            'monthly_footfall' => $request->monthly_footfall,
            'remarks' => $request->remarks,
        ]);

        Alert::success('Success', 'Recreational Facility added successfully');
        return redirect()->back();
    }

    /**
     * Update an existing recreational facility record
     */
    public function recreationalfacilitiesupdate(Request $request)
    {
        $request->validate([
            'id' => 'required|exists:recreational_facilities,id',
            'facility_type' => 'required|string',
            'facility_name' => 'required|string',
            'location' => 'required|string',
            'condition' => 'required|string',
            'ownership' => 'required|string',
            'year_established' => 'nullable|integer|min:1900|max:' . date('Y'),
            'entry_fee' => 'nullable|numeric|min:0',
            'monthly_footfall' => 'nullable|integer|min:0',
            'remarks' => 'nullable|string|max:500',
        ]);

        $user = Auth::guard('web')->user();
        $facility = RecreationalFacility::where('id', $request->id)
            ->where('ulb_id', $user->id)
            ->first();

        if (!$facility) {
            Alert::error('Error', 'Facility not found');
            return redirect()->back();
        }

        // Check attempts limit
        if ($facility->attempts >= 2) {
            Alert::warning('Update Limit Reached!', 'You have already updated this facility twice, which is the maximum number of updates allowed.');
            return redirect()->back();
        }

        // Check for duplicates only if the name or type has changed
        if ($facility->facility_name != $request->facility_name || $facility->facility_type != $request->facility_type) {
            $existingFacility = RecreationalFacility::where('ulb_id', $user->id)
                ->where('facility_type', $request->facility_type)
                ->where('facility_name', $request->facility_name)
                ->where('id', '!=', $request->id) // Exclude the current facility
                ->first();

            if ($existingFacility) {
                Alert::warning('Duplicate Entry', 'Another facility with this name and type already exists in your inventory.');
                return redirect()->back()->withInput();
            }
        }

        $facility->update([
            'facility_type' => $request->facility_type,
            'facility_name' => $request->facility_name,
            'location' => $request->location,
            'year_established' => $request->year_established,
            'condition' => $request->condition,
            'ownership' => $request->ownership,
            'entry_fee' => $request->entry_fee,
            'monthly_footfall' => $request->monthly_footfall,
            'remarks' => $request->remarks,
            'attempts' => $facility->attempts + 1,
        ]);

        Alert::success('Success!', 'Recreational Facility updated successfully. You have ' . (2 - $facility->attempts) . ' updates remaining for this facility.');
        return redirect()->back();
    }

    /**
     * Show the infrastructure works form
     */
    public function infrastructureworks()
    {
        $user = Auth::guard('web')->user();

        // Check if basic details exist (require basic details before this form)
        $basicDetail = UlbBasicDetail::where('ulb_id', $user->id)->first();


        $infrastructureWork = InfrastructureWork::where('ulb_id', $user->id)->first();
        $mode = request()->has('update') ? 'update' : ($infrastructureWork ? 'view' : 'create');

        return view('ulbdashboard.infrastructureworks', compact('infrastructureWork', 'mode'));
    }

    public function infrastructureworkssubmit(Request $request)
    {
        $user = Auth::guard('web')->user();

        try {
            InfrastructureWork::create([
                'ulb_id' => $user->id,
                'adhosanrachana_head_works' => $request->adhosanrachana_head_works,
                'fc_14_works' => $request->fc_14_works,
                'fc_15_works' => $request->fc_15_works,
                'regular_events' => $request->regular_events,
                'ngo_collaboration' => $request->ngo_collaboration,
                'citizen_participation' => $request->citizen_participation,
                'budget_allocation' => $request->budget_allocation,
                'maintenance_method' => $request->maintenance_method
            ]);

            Alert::success('Success', 'Infrastructure Works details submitted successfully');
            return redirect()->back();
        } catch (\Exception $e) {
            Alert::error('Error', 'Failed to submit Infrastructure Works details. ' . $e->getMessage());
            return redirect()->back()->withInput();
        }
    }

    public function infrastructureworksupdate(Request $request)
    {
        $user = Auth::guard('web')->user();

        try {
            $infrastructureWork = InfrastructureWork::where('ulb_id', $user->id)->first();

            if (!$infrastructureWork) {
                throw new \Exception('Infrastructure Work record not found.');
            }

            if ($infrastructureWork->attempts >= 2) {
                throw new \Exception('Update limit reached.');
            }

            $infrastructureWork->update([
                'adhosanrachana_head_works' => $request->adhosanrachana_head_works,
                'fc_14_works' => $request->fc_14_works,
                'fc_15_works' => $request->fc_15_works,
                'regular_events' => $request->regular_events,
                'ngo_collaboration' => $request->ngo_collaboration,
                'citizen_participation' => $request->citizen_participation,
                'budget_allocation' => $request->budget_allocation,
                'maintenance_method' => $request->maintenance_method,
                'attempts' => $infrastructureWork->attempts + 1
            ]);

            Alert::success('Success', 'Infrastructure Works details updated successfully');
            return redirect()->back();
        } catch (\Exception $e) {
            Alert::error('Error', 'Failed to update Infrastructure Works details. ' . $e->getMessage());
            return redirect()->back()->withInput();
        }
    }

    public function digitalsmartfeatures()
    {
        $user = Auth::guard('web')->user();

        // Check if basic details exist (require basic details before this form)
        $basicDetail = UlbBasicDetail::where('ulb_id', $user->id)->first();

        $digitalFeatures = DigitalSmartFeature::where('ulb_id', $user->id)->first();

        return view('ulbdashboard.digitalsmartfeatures', compact('digitalFeatures'));
    }

    public function digitalsmartfeaturessubmit(Request $request)
    {
        $user = Auth::guard('web')->user();

        try {
            DigitalSmartFeature::create([
                'ulb_id' => $user->id,
                'has_digital_management' => $request->has_digital_management,
                'feedback_mechanism' => $request->feedback_mechanism,
                'future_plans' => $request->future_plans
            ]);

            Alert::success('Success', 'Digital & Smart Features details submitted successfully');
            return redirect()->back();
        } catch (\Exception $e) {
            Alert::error('Error', 'Failed to submit Digital & Smart Features details. ' . $e->getMessage());
            return redirect()->back()->withInput();
        }
    }

    public function digitalsmartfeaturesupdate(Request $request)
    {
        $user = Auth::guard('web')->user();

        try {
            $features = DigitalSmartFeature::where('ulb_id', $user->id)->first();

            if (!$features) {
                throw new \Exception('Features record not found.');
            }

            if ($features->attempts >= 2) {
                throw new \Exception('Update limit reached.');
            }

            $features->update([
                'has_digital_management' => $request->has_digital_management,
                'feedback_mechanism' => $request->feedback_mechanism,
                'future_plans' => $request->future_plans,
                'attempts' => $features->attempts + 1
            ]);

            Alert::success('Success', 'Digital & Smart Features details updated successfully');
            return redirect()->back();
        } catch (\Exception $e) {
            Alert::error('Error', 'Failed to update Digital & Smart Features details. ' . $e->getMessage());
            return redirect()->back()->withInput();
        }
    }


    public function incomeexpenditure()
    {
        $nikay = Auth::guard('web')->user();
        $item = IncomeExpenditure::where('ulb_id', $nikay->id)->get();

        $currentMonth = now()->subMonth()->format('Y-m');


        $alreadySubmitted = IncomeExpenditure::where('ulb_id', $nikay->id)
            ->where('month', $currentMonth)
            ->exists();


        return view('ulbdashboard/incomeexpenditure', compact('item', 'nikay', 'alreadySubmitted'));
    }


    public function submitincomeexpenditure(Request $request)
    {

        $validatedData = $request->validate([

            'month' => 'required|string',
            'monthly_chungi' => 'required|numeric',
            'utpadkar' => 'required|numeric',
            'yatrikar' => 'required|numeric',
            'bar_license' => 'required|numeric',
            'mudrank_shulk' => 'required|numeric',
            'own_revenue' => 'required|numeric',
            'anya_madh' => 'required|numeric',
            'permanent_employee_salary' => 'required|numeric',
            'placement_employee_salary' => 'required|numeric',
            'padhadhikari_mandeya' => 'required|numeric',
            'gpf_cpf' => 'required|numeric',
            'travel_allowance' => 'required|numeric',
            'avkash_nakdikaran' => 'required|numeric',
            'electricity_bill' => 'required|numeric',
            'diesal_petrol' => 'required|numeric',
            'telephone_stationary' => 'required|numeric',
            'arrears' => 'required|numeric',
            'advertising_expense' => 'required|numeric',
            'vividh_vyay' => 'required|numeric',
            'jalkarya_sandharan' => 'required|numeric',
            'bhawan_anughya_rashi_bhugtan' => 'required|numeric',
            'freehold_madh_vyay' => 'required|numeric',
            'nikay_nidhi_nirman_karya_vyay' => 'required|numeric',
            'total_income' => 'required|numeric', // Assuming this is the sum of all income fields
            'total_expenditure' => 'required|numeric', // Assuming this is the sum of all expenditure fields

            'balance_amount' => 'required|numeric',
            'remark' => 'nullable|string',
        ]);

        $nikay = Auth::guard('web')->user();
        do {
            $uniqueId = random_int(10000, 99999);
        } while (IncomeExpenditure::where('unique_id', $uniqueId)->exists());

        // Save data
        $data = new IncomeExpenditure();
        $data->ulb_id = $nikay->id;
        $data->unique_id = $uniqueId;

        $data->month = $request->input('month');
        $data->monthly_chungi = $request->input('monthly_chungi');
        $data->utpadkar = $request->input('utpadkar');
        $data->yatrikar = $request->input('yatrikar');
        $data->bar_license = $request->input('bar_license');
        $data->mudrank_shulk = $request->input('mudrank_shulk');
        $data->own_revenue = $request->input('own_revenue');
        $data->anya_madh = $request->input('anya_madh');
        $data->permanent_employee_salary = $request->input('permanent_employee_salary');
        $data->placement_employee_salary = $request->input('placement_employee_salary');
        $data->padhadhikari_mandeya = $request->input('padhadhikari_mandeya');
        $data->gpf_cpf = $request->input('gpf_cpf');
        $data->travel_allowance = $request->input('travel_allowance');
        $data->avkash_nakdikaran = $request->input('avkash_nakdikaran');
        $data->electricity_bill = $request->input('electricity_bill');
        $data->diesal_petrol = $request->input('diesal_petrol');
        $data->telephone_stationary = $request->input('telephone_stationary');
        $data->arrears = $request->input('arrears');
        $data->advertising_expense = $request->input('advertising_expense');
        $data->vividh_vyay = $request->input('vividh_vyay');
        $data->jalkarya_sandharan = $request->input('jalkarya_sandharan');
        $data->bhawan_anughya_rashi_bhugtan = $request->input('bhawan_anughya_rashi_bhugtan');
        $data->freehold_madh_vyay = $request->input('freehold_madh_vyay');
        $data->nikay_nidhi_nirman_karya_vyay = $request->input('nikay_nidhi_nirman_karya_vyay');
        $data->total_income = $request->input('total_income');
        $data->total_expenditure = $request->input('total_expenditure');
        $data->balance_amount = $request->input('balance_amount');
        $data->remark = $request->input('remark');

        $data->save();

        Alert::success('Success!', 'Income and expenditure data submitted successfully.');

        return redirect()->back();
    }


    public function updateincomeexpenditure(Request $request)
    {
        $validatedData = $request->validate([

            'month' => 'required|string',
            'monthly_chungi' => 'required|numeric',
            'utpadkar' => 'required|numeric',
            'yatrikar' => 'required|numeric',
            'bar_license' => 'required|numeric',
            'mudrank_shulk' => 'required|numeric',
            'own_revenue' => 'required|numeric',
            'anya_madh' => 'required|numeric',
            'permanent_employee_salary' => 'required|numeric',
            'placement_employee_salary' => 'required|numeric',
            'padhadhikari_mandeya' => 'required|numeric',
            'gpf_cpf' => 'required|numeric',
            'travel_allowance' => 'required|numeric',
            'avkash_nakdikaran' => 'required|numeric',
            'electricity_bill' => 'required|numeric',
            'diesal_petrol' => 'required|numeric',
            'telephone_stationary' => 'required|numeric',
            'arrears' => 'required|numeric',
            'advertising_expense' => 'required|numeric',
            'vividh_vyay' => 'required|numeric',
            'jalkarya_sandharan' => 'required|numeric',
            'bhawan_anughya_rashi_bhugtan' => 'required|numeric',
            'freehold_madh_vyay' => 'required|numeric',
            'nikay_nidhi_nirman_karya_vyay' => 'required|numeric',
            'total_income' => 'required|numeric', // Assuming this is the sum of all income fields
            'total_expenditure' => 'required|numeric', // Assuming this is the sum of all expenditure fields
            'balance_amount' => 'required|numeric',
            'remark' => 'nullable|string|max:500',
        ]);

        $nikay = Auth::guard('web')->user();
        $data = IncomeExpenditure::where('unique_id', $request->input('unique_id'))->firstOrFail();
        // Update data
        $data->month = $request->input('month');
        $data->monthly_chungi = $request->input('monthly_chungi');
        $data->utpadkar = $request->input('utpadkar');
        $data->yatrikar = $request->input('yatrikar');
        $data->bar_license = $request->input('bar_license');
        $data->mudrank_shulk = $request->input('mudrank_shulk');
        $data->own_revenue = $request->input('own_revenue');
        $data->anya_madh = $request->input('anya_madh');
        $data->permanent_employee_salary = $request->input('permanent_employee_salary');
        $data->placement_employee_salary = $request->input('placement_employee_salary');
        $data->padhadhikari_mandeya = $request->input('padhadhikari_mandeya');
        $data->gpf_cpf = $request->input('gpf_cpf');
        $data->travel_allowance = $request->input('travel_allowance');
        $data->avkash_nakdikaran = $request->input('avkash_nakdikaran');
        $data->electricity_bill = $request->input('electricity_bill');
        $data->diesal_petrol = $request->input('diesal_petrol');
        $data->telephone_stationary = $request->input('telephone_stationary');
        $data->arrears = $request->input('arrears');
        $data->advertising_expense = $request->input('advertising_expense');
        $data->vividh_vyay = $request->input('vividh_vyay');
        $data->jalkarya_sandharan = $request->input('jalkarya_sandharan');
        $data->bhawan_anughya_rashi_bhugtan = $request->input('bhawan_anughya_rashi_bhugtan');
        $data->freehold_madh_vyay = $request->input('freehold_madh_vyay');
        $data->nikay_nidhi_nirman_karya_vyay = $request->input('nikay_nidhi_nirman_karya_vyay');

        $data->total_income = $request->input('total_income');
        $data->total_expenditure = $request->input('total_expenditure');
        $data->balance_amount = $request->input('balance_amount');
        $data->remark = $request->input('remark');
        $data->attempts = ($data->attempts ?? 0) + 1;
        $data->save();

        Alert::success('Success!', 'Income and Expenditure data updated successfully.');
        return redirect()->back();
    }

    public function ulbbpnumber()
    {
        $loggedInId = Auth::guard('web')->user()->id;

        // Get all BP numbers from BakayaRashi with pagination
        $allBpNumbers = BakayaRashi::select('business_partner')
            ->distinct()
            ->paginate(10); // Show 10 items per page

        // Get verified BP numbers for the current ULB
        $verifiedBpNumbers = BpVerification::where('ulb_id', $loggedInId)
            ->pluck('bp_number')
            ->toArray();

        // Prepare data with verification status
        $bpData = $allBpNumbers->map(function($bp) use ($verifiedBpNumbers, $loggedInId) {
            $isVerified = in_array($bp->business_partner, $verifiedBpNumbers);
            $verificationRecord = null;

            if ($isVerified) {
                $verificationRecord = BpVerification::where('bp_number', $bp->business_partner)
                    ->where('ulb_id', $loggedInId)
                    ->first();
            }

            return (object) [
                'bp_number' => $bp->business_partner,
                'is_verified' => $isVerified,
                'status' => $isVerified ? 'verified' : 'not_verified', 
                'verified_at' => $verificationRecord ? $verificationRecord->verified_at : null
            ];
        });

        // Keep pagination information
        $bpData = new \Illuminate\Pagination\LengthAwarePaginator(
            $bpData,
            $allBpNumbers->total(),
            $allBpNumbers->perPage(),
            $allBpNumbers->currentPage(),
            ['path' => request()->url()]
        );

        return view('ulbdashboard/ulbbpnumber', compact('bpData'));
    }

    public function verifyBpNumber(Request $request)
    {
        try {
            $loggedInId = Auth::guard('web')->user()->id;

            $validatedData = $request->validate([
                'bp_number' => 'required|string|max:255',
            ]);

            $bpNumber = $validatedData['bp_number'];

            // Check if BP number exists in BakayaRashi
            $bpExists = BakayaRashi::where('business_partner', $bpNumber)->exists();

            if (!$bpExists) {
                if ($request->ajax()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'BP Number not found in the system.'
                    ], 404);
                }
                Alert::error('Error!', 'BP Number not found in the system.');
                return redirect()->back();
            }

            // Check if BP number is already verified by this ULB
            $existingVerification = BpVerification::where('bp_number', $bpNumber)
                ->where('ulb_id', $loggedInId)
                ->first();

            if ($existingVerification) {
                if ($request->ajax()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'BP Number is already verified.'
                    ], 400);
                }
                Alert::info('Information!', 'BP Number is already verified.');
                return redirect()->back();
            }

            // Create new BP verification record
            BpVerification::create([
                'bp_number' => $bpNumber,
                'ulb_id' => $loggedInId,
                'status' => 'verified',
                'verified_at' => now(),
            ]);

            // Check if it's an AJAX request
            if ($request->ajax()) {
                return response()->json([
                    'success' => true,
                    'message' => 'BP Number verified successfully.'
                ]);
            }

            Alert::success('Success!', 'BP Number verified successfully.');
            return redirect()->back();

        } catch (\Exception $e) {
            // Check if it's an AJAX request
            if ($request->ajax()) {
                return response()->json([
                    'success' => false,
                    'message' => 'An error occurred while verifying BP Number: ' . $e->getMessage()
                ], 400);
            }

            Alert::error('Error!', 'An error occurred while verifying BP Number: ' . $e->getMessage());
            return redirect()->back();
        }
    }

    public function searchBpNumber(Request $request)
    {
        try {
            $validatedData = $request->validate([
                'search_term' => 'required|string|max:255',
            ]);

            $searchTerm = $validatedData['search_term'];

            // Search for BP numbers that match the search term
            $searchResults = BakayaRashi::where('business_partner', 'LIKE', '%' . $searchTerm . '%')
                ->select('business_partner')
                ->distinct()
                ->limit(10)
                ->get();

            return response()->json([
                'success' => true,
                'data' => $searchResults
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error searching BP numbers: ' . $e->getMessage()
            ]);
        }
    }
}
