<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LeaveApply extends Model
{
    use HasFactory;

    public function ulb()
    {

        return $this->belongsTo(User::class,'ulb_id');
    }
    public function division()
    {
        return $this->belongsTo(MasterDivision::class, 'division_id');
    }
    public function post()
    {
        return $this->belongsTo(EmployeePost::class, 'type_of_officer');  // Assuming 'post_id' is the foreign key
    }
}
