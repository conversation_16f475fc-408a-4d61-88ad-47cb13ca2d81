<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MudrankSulak extends Model
{
    use HasFactory;
    protected $fillable = [
       'month',
        'amount_released',
        'expenditure_amount',
        'remark',
        'unique_no',
        'ulb_id',
        'balance_amount'
    ];
    
    public function ulb()
    {
        return $this->belongsTo(User::class,'ulb_id');
    }
}
