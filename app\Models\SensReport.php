<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SensReport extends Model
{
    use HasFactory;
     public function ulb()
    {
        return $this->belongsTo(User::class,'ulb_id');
    }
    public function division()
    {
        return $this->belongsTo(MasterDivision::class, 'division_id');
    }
      public function district()
    {
        return $this->belongsTo(MasterDistrict::class, 'district_id');
    }
}
