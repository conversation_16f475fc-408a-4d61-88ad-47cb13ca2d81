<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use RealRashid\SweetAlert\Facades\Alert;

class AdminAuthController extends Controller
{

    public function adminLogin()
    {
        return view('adminlogin');
    }

    public function doLogin(Request $request)
    {
        try {
            // Validate the request data
            $request->validate([
                'email' => 'required|email',
                'password' => 'required',
            ]);

            $credentials = $request->only('email', 'password');

            // Attempt to log the user in using the admin guard
            if (Auth::guard('admin')->attempt($credentials)) {
                $user = Auth::guard('admin')->user();
                // dd($user);
                $roleId = $user->role_id; // Retrieve the role ID

                // Set a flash message for SweetAlert with auto-close options
                Alert::success('Login Successful', 'Welcome back, ' . $user->name . '!')->persistent(true);


                // Check if the user has the appropriate role to log in
                switch ($roleId) {
                    case 1: // SuperAdmin
                        return redirect()->intended(route('admin.dashboard'));
                    case 2: // Department
                        return redirect()->intended(route('department.dashboard'));
                    case 3: // CEO
                        return redirect()->intended(route('jddashboard'));
                    default:
                        Auth::guard('admin')->logout();
                        return response()->json(['error' => 'Unauthorized'], 401);
                }
            }

            // If login attempt fails
            return back()->withErrors(['email' => 'Login details are not valid'])->withInput();
        } catch (ValidationException $e) {
            // Handle validation exceptions
            return back()->withErrors($e->errors())->withInput();
        } catch (\Exception $e) {
            // Log the error and return a generic error message
            Log::error('Login attempt failed: ' . $e->getMessage());

            return response()->json(['error' => 'An error occurred during login. Please try again later.'], 500);
        }
    }

    public function logout()
    {
        Auth::guard('admin')->logout();
        Alert::success('Logged Out', 'Admin have been successfully logged out.')->persistent(true);
        return redirect()->route('admin.loginpage');
    }

    public function ulbLogout()
    {
        Auth::guard('web')->logout();
        Alert::success('Logged Out', 'You have been successfully logged out.')->persistent(true);
        return redirect()->route('ulb.loginpage');
    }

    // public function ulbLogin(Request $request)
    // {
    //     // dd($request);
    //     $request->validate([
    //         'email' => 'required|email',
    //         'password' => 'required',
    //     ]);
    //     $check = $request->all();
    //     $data = [
    //         'email' => $check['email'],
    //         'password' => $check['password'],
    //     ];

    //     if (Auth::guard('web')->attempt($data)) {
    //         return redirect(route('ulb.dashboard'))->with('success', 'Login Successfully!');
    //     }

    //     // Authentication failed, redirect back with error message
    //     return redirect()
    //         ->back()
    //         ->withInput($request->only('email', 'remember'))
    //         ->withErrors([
    //             'email' => 'These credentials do not match our records.',
    //         ]);
    // }
    public function ulbLogin(Request $request)
{
    $request->validate([
        'email' => 'required|email',
        'password' => 'required',
    ]);

    $credentials = $request->only('email', 'password');

    // Attempt login with the provided credentials.
    if (Auth::guard('web')->attempt($credentials)) {
        // Retrieve the authenticated user.
        $user = Auth::guard('web')->user();

        // Check if the status is active.
        if ($user->status !== 'active') {
            // If the user is inactive, log them out.
            Auth::guard('web')->logout();
            return redirect()->back()
                ->withInput($request->only('email', 'remember'))
                ->withErrors(['email' => 'Your account is inactive. It will be active soon.']);
        }

        // If active, redirect to the dashboard.
        return redirect(route('ulb.dashboard'))->with('success', 'Login Successfully!');
    }

    // Authentication failed, redirect back with error message.
    return redirect()->back()
        ->withInput($request->only('email', 'remember'))
        ->withErrors(['email' => 'These credentials do not match our records.']);
}

}
