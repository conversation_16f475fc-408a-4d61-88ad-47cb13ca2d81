<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PlaygroundInfo extends Model
{
    use HasFactory;
    public function ulb()
    {
        return $this->belongsTo(User::class,'ulb_id');
    }
    public function division()
    {
        return $this->belongsTo(MasterDivision::class, 'division_id');
    }
    protected $fillable = [
        'unique_id',
        'ulb_id',
        'dateInput',
        'ward_no',
        'ward_name',
        'playground_address',
        'area',
        'latitude',
        'longitude',
    ];
}
