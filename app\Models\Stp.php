<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Stp extends Model
{
    use HasFactory;
    public function ulb()
    {
        return $this->belongsTo(User::class, 'ulb_id');
    }
    public function division()
    {
        return $this->belongsTo(MasterDivision::class, 'division_id');
    }
    protected $fillable = [
        'financial_year',
        'dateInput',
        'stps_latitude',
        'stps_longitude',
        'stp_capitalize_in_amd',
        'stps_remark',
    ];
}
