@include('ulbdashboard/header')

<style>
    .search-container {
        position: relative;
    }

    #searchResults {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        z-index: 1000;
        background: white;
        border: 1px solid #ddd;
        border-radius: 0.375rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    .bp-search-item:hover {
        background-color: #f8f9fa;
    }

    .table th {
        background-color: #343a40;
        color: white;
    }

    .badge {
        font-size: 0.875em;
    }

    .table-light {
        background-color: #f8f9fa;
    }

    .quick-verify-btn {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
    }

    .table tbody tr:hover {
        background-color: #f5f5f5;
    }

    .stats-summary {
        background-color: #e9ecef;
        padding: 0.5rem;
        border-radius: 0.375rem;
    }
</style>

<div class="container-scroller">
    <!-- partial:partials/_navbar.html -->
    @include('ulbdashboard/topnav')
    <!-- partial -->
    <div class="container-fluid page-body-wrapper">

        @include('ulbdashboard/sidenav')

        <!-- partial -->
        <div class="main-panel">
            <div class="content-wrapper p-2">
                <div class="bg-primary-subtle d-flex justify-content-between align-middle p-2 rounded border">

                    <div>
                        <img src="{{ asset('images/cmsir.jpg') }}" width="60px" height="auto" />
                    </div>
                    <div class="dascen">
                        <h3><b>
                                BP NUMBER VERIFY
                            </b></h3>
                    </div>
                    <div>
                        <img src="{{ asset('images/newdeptycm.jpeg') }}" width="60px" height="auto" />
                    </div>

                </div>

                <div class="mt-3">

                    <form method="POST" action="{{ route('verify.bp.number') }}" class="mb-3" id="bpVerificationForm">
                        @csrf
                        <div class="mb-3 search-container">
                            <label for="bp_number" class="form-label">BP Number</label>
                            <input type="text" class="form-control" id="bp_number" name="bp_number"
                                placeholder="Enter BP Number to Verify" required autocomplete="off">
                            <div id="searchResults" class="dropdown-menu" style="display: none; width: 100%; max-height: 200px; overflow-y: auto;"></div>
                        </div>

                        <button type="submit" class="btn btn-success btn-sm">Verify BP Number</button>
                    </form>


                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h5>All BP Numbers</h5>
                        </div>
                        <div class="col-md-6 text-end">
                            <div class="btn-group btn-group-sm mb-2" role="group">
                                <button type="button" class="btn btn-outline-primary filter-btn active" data-filter="all">
                                    All ({{ $bpData->count() }})
                                </button>
                                <button type="button" class="btn btn-outline-success filter-btn" data-filter="verified">
                                    Verified ({{ $bpData->where('is_verified', true)->count() }})
                                </button>
                                <button type="button" class="btn btn-outline-warning filter-btn" data-filter="not_verified">
                                    Not Verified ({{ $bpData->where('is_verified', false)->count() }})
                                </button>
                            </div>
                        </div>
                    </div>

                    <table class="table table-bordered table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th scope="col">S.No</th>
                                <th scope="col">BP Number</th>
                                <th scope="col">Status</th>
                                <th scope="col">Verified Date</th>
                                <th scope="col">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse ($bpData as $index => $bp)
                            <tr class="{{ $bp->is_verified ? 'table-light' : '' }} bp-row"
                                data-status="{{ $bp->is_verified ? 'verified' : 'not_verified' }}">
                                <th scope="row">{{ ($bpData->currentPage() - 1) * $bpData->perPage() + $index + 1 }}</th>
                                <td>{{ $bp->bp_number }}</td>
                                <td>
                                    @if($bp->is_verified)
                                        <span class="badge bg-success">Verified</span>
                                    @else
                                        <span class="badge bg-warning text-dark">Not Verified</span>
                                    @endif
                                </td>
                                <td>
                                    {{ $bp->verified_at ? $bp->verified_at->format('d-m-Y H:i') : 'N/A' }}
                                </td>
                                <td>
                                    @if(!$bp->is_verified)
                                        <button type="button" class="btn btn-sm btn-outline-success quick-verify-btn"
                                                data-bp="{{ $bp->bp_number }}">
                                            Verify
                                        </button>
                                    @else
                                        <span class="text-success">
                                            <i class="fas fa-check-circle"></i> Verified
                                        </span>
                                    @endif
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="5" class="text-center text-muted">No BP numbers found in the system.</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                    <div class="d-flex justify-content-end">
                        {{ $bpData->links('pagination::bootstrap-5') }}
                    </div>
                </div>
            </div>

            <footer class="footer">
                <div class="d-sm-flex justify-content-center justify-content-sm-between">
                    <span class="text-muted text-center text-sm-left d-block d-sm-inline-block">Copyright © 2024. <a
                            href="#" target="_blank">Designed by Datacenter</a>. All rights reserved.</span>
                </div>
            </footer>
            <!-- partial -->
        </div>
        <!-- main-panel ends -->
    </div>
    <!-- page-body-wrapper ends -->
</div>

@include('ulbdashboard/footer')

<script>
$(document).ready(function() {
    let searchTimeout;

    // BP Number search functionality
    $('#bp_number').on('input', function() {
        const searchTerm = $(this).val().trim();
        const searchResults = $('#searchResults');

        clearTimeout(searchTimeout);

        if (searchTerm.length < 2) {
            searchResults.hide();
            return;
        }

        searchTimeout = setTimeout(function() {
            $.ajax({
                url: '{{ route("search.bp.number") }}',
                method: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    search_term: searchTerm
                },
                success: function(response) {
                    if (response.success && response.data.length > 0) {
                        let html = '';
                        response.data.forEach(function(item) {
                            html += `<a href="#" class="dropdown-item bp-search-item" data-bp="${item.business_partner}">${item.business_partner}</a>`;
                        });
                        searchResults.html(html).show();
                    } else {
                        searchResults.html('<div class="dropdown-item text-muted">No BP numbers found</div>').show();
                    }
                },
                error: function() {
                    searchResults.html('<div class="dropdown-item text-danger">Error searching BP numbers</div>').show();
                }
            });
        }, 300);
    });

    // Handle BP number selection from search results
    $(document).on('click', '.bp-search-item', function(e) {
        e.preventDefault();
        const bpNumber = $(this).data('bp');
        $('#bp_number').val(bpNumber);
        $('#searchResults').hide();
    });

    // Hide search results when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('#bp_number, #searchResults').length) {
            $('#searchResults').hide();
        }
    });

    // Form submission with confirmation
    $('#bpVerificationForm').on('submit', function(e) {
        const bpNumber = $('#bp_number').val().trim();
        if (!bpNumber) {
            e.preventDefault();
            alert('Please enter a BP number to verify.');
            return false;
        }

        if (!confirm(`Are you sure you want to verify BP Number: ${bpNumber}?`)) {
            e.preventDefault();
            return false;
        }
    });

    // Quick verify button functionality
    $('.quick-verify-btn').on('click', function() {
        const bpNumber = $(this).data('bp');
        const button = $(this);

        if (!confirm(`Are you sure you want to verify BP Number: ${bpNumber}?`)) {
            return;
        }

        // Disable button and show loading
        button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> Verifying...');

        $.ajax({
            url: '{{ route("verify.bp.number") }}',
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}',
                bp_number: bpNumber
            },
            success: function(response) {
                // Reload the page to show updated status
                location.reload();
            },
            error: function(xhr) {
                button.prop('disabled', false).html('Quick Verify');
                let errorMessage = 'Error verifying BP number';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                alert(errorMessage);
            }
        });
    });

    // Filter functionality
    $('.filter-btn').on('click', function() {
        const filter = $(this).data('filter');
        const rows = $('.bp-row');

        // Update active button
        $('.filter-btn').removeClass('active');
        $(this).addClass('active');

        // Show/hide rows based on filter
        if (filter === 'all') {
            rows.show();
        } else {
            rows.hide();
            rows.filter(`[data-status="${filter}"]`).show();
        }

        // Update row numbers
        let visibleIndex = 1;
        rows.filter(':visible').each(function() {
            $(this).find('th:first').text(visibleIndex++);
        });
    });
});
</script>