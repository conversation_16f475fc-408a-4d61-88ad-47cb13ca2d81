<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UlbAdministrativeDetail extends Model
{
    use HasFactory;

    protected $fillable = [
        'ulb_id',
        'chairperson_name',
        'commissioner_name',
        'elected_representatives_count',
        'st_representatives_count',
        'sc_representatives_count',
        'women_representatives_count',
        'total_staff_strength',
        'attempts'
    ];

    public function ulb()
    {
        return $this->belongsTo(User::class, 'ulb_id');
    }

    public function basicDetail()
    {
        return $this->belongsTo(UlbBasicDetail::class, 'ulb_id', 'ulb_id');
    }
} 