<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class InfrastructureWork extends Model
{
    use HasFactory;

    protected $fillable = [
        'ulb_id',
        'adhosan<PERSON><PERSON>_head_works',
        'fc_14_works',
        'fc_15_works',
        'regular_events',
        'ngo_collaboration',
        'citizen_participation',
        'budget_allocation',
        'maintenance_method',
        'attempts'
    ];

    protected $casts = [
        'start_date' => 'date',
        'completion_date' => 'date',
        'estimated_cost' => 'decimal:2',
        'actual_cost' => 'decimal:2'
    ];

    public function ulb()
    {
        return $this->belongsTo(User::class, 'ulb_id');
    }
} 