<?php

namespace App\Imports;

use App\Models\YatriKar;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToModel;

class Dataimportyatrikar implements ToModel
{
    /**
    * @param Collection $collection
    */
    public function model(array $row)
    {
        // Make sure the column indices match the structure of the Excel sheet
        return new YatriKar([

            'ulb_id'            => $row[1], // Assuming ulb_id is in the first column
            'unique_no'         => $row[2], // chungi_no in the second column
            'month'             => $row[3],   // month is in the fourth column (index 3)
            'amount_released'   => $row[4],   // amount_released is in the fifth column (index 4)
            'expenditure_amount' => $row[5] ,   // expenditure_amount is in the sixth column (index 5)
            'balance_amount'    => $row[6],   // balance_amount is in the seventh column (index 6)
            'remark'            => $row[7],   // remark is in the eighth column (index 7)
            'status'            => $row[8] ?? 'pending',   // status (default 'pending' if null)
            'attempt'           => $row[9] ?? '0',         // attempt (default '0' if null)
            'is_jd_verify'      => $row[10] ?? false,      // is_jd_verify (default false if null)
        ]);
    }
}
