<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TrafficLight extends Model
{
    use HasFactory;
     public function ulb()
    {
        return $this->belongsTo(User::class,'ulb_id');
    }
    public function division()
    {
        return $this->belongsTo(MasterDivision::class, 'division_id');
    }
    protected $fillable = [
        'ulb_id',
        'unique_id',
        'dateInput',
        'financial_year',
        'ward_no',
        'ward_name',
        'traffic_light_id',
        'type',
        'height',
        'longitude',
        'latitude',
    ];

}
