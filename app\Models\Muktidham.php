<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Muktidham extends Model
{
    use HasFactory;
    public function ulb()
    {

        return $this->belongsTo(User::class,'ulb_id');
    }
    public function division()
    {
        return $this->belongsTo(MasterDivision::class, 'division_id');
    }
    protected $fillable = [
        'ulb_id',
        'dateInput',
        'ward_no',
        'ward_name',
        'muktidham_address',
        'longitude',
        'latitude',
        'unique_id',
        // add any other fields you're updating via mass assignment
    ];
}
