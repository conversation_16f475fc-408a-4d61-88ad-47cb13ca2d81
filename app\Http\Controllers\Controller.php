<?php

namespace App\Http\Controllers;

use App\Models\MahapaurAdhyakshNidhi;
use Illuminate\Http\Request;
use App\Models\MayorChairmanList;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class Controller extends BaseController
{
    use AuthorizesRequests, ValidatesRequests;


    public function login()
    {
        return view('login');
    }

  
    public function electionresult(Request $request)
    {
        $ulbNames = MayorChairmanList::distinct()->pluck('ulb_name');
        $partyNames = MayorChairmanList::where('partyname', '!=', '-')->distinct()->pluck('partyname');

        $query = MayorChairmanList::query();
        if ($request->has('ulb_name') && $request->ulb_name != '') {
            $ulbname = $request->ulb_name;
            $query->where('ulb_name', $ulbname);
        }
        if ($request->has('partyname') && $request->partyname != '') {
          
            $query->where('partyname',$request->partyname);
        }

        $itemmayor = MayorChairmanList::where('role', 'M')->get();
        $itemchairmen = $query->where('role', 'C')->get();

        return view('electionresult', compact('itemmayor', 'itemchairmen', 'ulbNames','partyNames'));
    }

    public function exportexcelmayor() // overall pension report
    {
        $ulbWiseCounts = MayorChairmanList::where('role', 'M')->get();
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        // Set headings
        $sheet->setCellValue('A1', 'Sno.');
        $sheet->setCellValue('B1', 'निकाय का नाम');
        $sheet->setCellValue('C1', 'नाम');
        $sheet->setCellValue('D1', 'पार्टी');
        $sheet->setCellValue('E1', 'कुल वार्ड की संख्या');

        $row = 2;  // Start from the second row
        foreach ($ulbWiseCounts as $index => $data) {
            $sheet->setCellValue('A' . $row, $index + 1);  
            $sheet->setCellValue('B' . $row, $data->ulb_name);  
            $sheet->setCellValue('C' . $row, $data->name);
            $sheet->setCellValue('D' . $row, $data->partyname);  
            $sheet->setCellValue('E' . $row, $data->total_ward); 
            $row++;
        }
        $writer = new Xlsx($spreadsheet);
        $filename = 'Mayor_list.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');
        $writer->save('php://output');
        exit;
    }
    public function exportexcelchairman()
    {

        $ulbWiseCounts = MayorChairmanList::where('role', 'C')->get();
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setCellValue('A1', 'Sno.');
        $sheet->setCellValue('B1', 'निकाय का नाम');
        $sheet->setCellValue('C1', 'नाम');
        $sheet->setCellValue('D1', 'पार्टी');
        $sheet->setCellValue('E1', 'कुल वार्ड की संख्या');


        $row = 2;  
        foreach ($ulbWiseCounts as $index => $data) {
            $sheet->setCellValue('A' . $row, $index + 1);  
            $sheet->setCellValue('B' . $row, $data->ulb_name);  
            $sheet->setCellValue('C' . $row, $data->name); 
            $sheet->setCellValue('D' . $row, $data->partyname);  
            $sheet->setCellValue('E' . $row, $data->total_ward);  
            $row++;
        }
        $writer = new Xlsx($spreadsheet);
        $filename = 'Chairman_list.xlsx';
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: max-age=0');
        $writer->save('php://output');
        exit;
    }
}
